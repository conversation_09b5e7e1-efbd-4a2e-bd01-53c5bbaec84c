<template>
  <div class="h-full flex flex-col bg-gradient-to-br from-[#0a1628] to-[#1a2332] p-[1vw]">
    <!-- 顶部统计面板 -->
    <div class="grid grid-cols-4 gap-[0.8vw] mb-[1vw]">
      <div v-for="stat in hvacStats" :key="stat.label" class="bg-[rgba(255,165,0,0.1)] p-[0.8vw] rounded-lg border border-[rgba(255,165,0,0.2)]">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-[1.2vw] font-bold" :class="stat.valueClass">{{ stat.value }}</div>
            <div class="text-[0.7vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          </div>
          <component :is="stat.icon" class="text-[1.5vw]" :class="stat.iconClass" />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 grid grid-cols-2 gap-[1vw]">
      <!-- 左侧：暖通设备控制面板 -->
      <div class="space-y-[1vw]">
        <!-- 空调系统 -->
        <div class="bg-[rgba(255,165,0,0.1)] p-[0.8vw] rounded-lg border border-[rgba(255,165,0,0.2)]">
          <div class="text-[0.8vw] text-white mb-[0.8vw] flex items-center">
            <CloudOutlined class="mr-[0.4vw] text-blue-400" />
            空调系统控制
          </div>
          <div class="grid grid-cols-2 gap-[0.6vw]">
            <div
              v-for="ac in airConditioners"
              :key="ac.id"
              class="bg-black/20 p-[0.6vw] rounded border"
              :class="ac.status === 'running' ? 'border-green-400' : 'border-red-400'"
            >
              <div class="flex items-center justify-between mb-[0.4vw]">
                <span class="text-[0.7vw] text-white">{{ ac.name }}</span>
                <div class="w-[0.4vw] h-[0.4vw] rounded-full" :class="ac.status === 'running' ? 'bg-green-400' : 'bg-red-400'"></div>
              </div>
              <div class="space-y-[0.3vw]">
                <div class="flex justify-between">
                  <span class="text-[0.6vw] text-gray-400">温度:</span>
                  <span class="text-[0.6vw] text-white">{{ ac.temperature }}°C</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-[0.6vw] text-gray-400">湿度:</span>
                  <span class="text-[0.6vw] text-white">{{ ac.humidity }}%</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-[0.6vw] text-gray-400">功率:</span>
                  <span class="text-[0.6vw] text-white">{{ ac.power }}kW</span>
                </div>
              </div>
              <div class="flex gap-[0.3vw] mt-[0.4vw]">
                <button
                  class="flex-1 bg-green-500 text-white text-[0.5vw] py-[0.2vw] rounded hover:bg-green-600 transition-colors"
                  @click="controlAC(ac.id, 'start')"
                >
                  启动
                </button>
                <button
                  class="flex-1 bg-red-500 text-white text-[0.5vw] py-[0.2vw] rounded hover:bg-red-600 transition-colors"
                  @click="controlAC(ac.id, 'stop')"
                >
                  停止
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 通风系统 -->
        <div class="bg-[rgba(255,165,0,0.1)] p-[0.8vw] rounded-lg border border-[rgba(255,165,0,0.2)]">
          <div class="text-[0.8vw] text-white mb-[0.8vw] flex items-center">
            <ThunderboltOutlined class="mr-[0.4vw] text-yellow-400" />
            通风系统控制
          </div>
          <div class="grid grid-cols-2 gap-[0.6vw]">
            <div
              v-for="fan in ventilationFans"
              :key="fan.id"
              class="bg-black/20 p-[0.6vw] rounded border"
              :class="fan.status === 'running' ? 'border-green-400' : 'border-gray-400'"
            >
              <div class="flex items-center justify-between mb-[0.4vw]">
                <span class="text-[0.7vw] text-white">{{ fan.name }}</span>
                <div class="w-[0.4vw] h-[0.4vw] rounded-full" :class="fan.status === 'running' ? 'bg-green-400' : 'bg-gray-400'"></div>
              </div>
              <div class="space-y-[0.3vw]">
                <div class="flex justify-between">
                  <span class="text-[0.6vw] text-gray-400">转速:</span>
                  <span class="text-[0.6vw] text-white">{{ fan.speed }}rpm</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-[0.6vw] text-gray-400">风量:</span>
                  <span class="text-[0.6vw] text-white">{{ fan.airflow }}m³/h</span>
                </div>
              </div>
              <div class="flex gap-[0.3vw] mt-[0.4vw]">
                <button
                  class="flex-1 bg-blue-500 text-white text-[0.5vw] py-[0.2vw] rounded hover:bg-blue-600 transition-colors"
                  @click="controlFan(fan.id, 'start')"
                >
                  启动
                </button>
                <button
                  class="flex-1 bg-gray-500 text-white text-[0.5vw] py-[0.2vw] rounded hover:bg-gray-600 transition-colors"
                  @click="controlFan(fan.id, 'stop')"
                >
                  停止
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：监控和报表 -->
      <div class="space-y-[1vw]">
        <!-- 环境监控 -->
        <div class="bg-[rgba(255,165,0,0.1)] p-[0.8vw] rounded-lg border border-[rgba(255,165,0,0.2)]">
          <div class="text-[0.8vw] text-white mb-[0.8vw] flex items-center">
            <DashboardOutlined class="mr-[0.4vw] text-green-400" />
            环境监控
          </div>
          <div class="grid grid-cols-2 gap-[0.6vw]">
            <div v-for="sensor in environmentSensors" :key="sensor.id" class="bg-black/20 p-[0.6vw] rounded">
              <div class="text-[0.7vw] text-white mb-[0.4vw]">{{ sensor.location }}</div>
              <div class="space-y-[0.3vw]">
                <div class="flex justify-between items-center">
                  <span class="text-[0.6vw] text-gray-400">温度:</span>
                  <div class="flex items-center">
                    <span class="text-[0.6vw] text-white mr-[0.3vw]">{{ sensor.temperature }}°C</span>
                    <div class="w-[0.3vw] h-[0.3vw] rounded-full" :class="sensor.temperature > 25 ? 'bg-red-400' : 'bg-green-400'"></div>
                  </div>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-[0.6vw] text-gray-400">湿度:</span>
                  <div class="flex items-center">
                    <span class="text-[0.6vw] text-white mr-[0.3vw]">{{ sensor.humidity }}%</span>
                    <div class="w-[0.3vw] h-[0.3vw] rounded-full" :class="sensor.humidity > 60 ? 'bg-yellow-400' : 'bg-green-400'"></div>
                  </div>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-[0.6vw] text-gray-400">气压:</span>
                  <span class="text-[0.6vw] text-white">{{ sensor.pressure }}Pa</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 能耗统计 -->
        <div class="bg-[rgba(255,165,0,0.1)] p-[0.8vw] rounded-lg border border-[rgba(255,165,0,0.2)]">
          <div class="text-[0.8vw] text-white mb-[0.8vw] flex items-center">
            <BarChartOutlined class="mr-[0.4vw] text-purple-400" />
            能耗统计
          </div>
          <div class="space-y-[0.6vw]">
            <div v-for="consumption in energyConsumption" :key="consumption.type" class="bg-black/20 p-[0.6vw] rounded">
              <div class="flex justify-between items-center mb-[0.3vw]">
                <span class="text-[0.7vw] text-white">{{ consumption.type }}</span>
                <span class="text-[0.7vw]" :class="consumption.trend === 'up' ? 'text-red-400' : 'text-green-400'">
                  {{ consumption.value }}
                </span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400">
                <span>今日消耗</span>
                <span>{{ consumption.daily }}</span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400">
                <span>本月消耗</span>
                <span>{{ consumption.monthly }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 维护计划 -->
        <div class="bg-[rgba(255,165,0,0.1)] p-[0.8vw] rounded-lg border border-[rgba(255,165,0,0.2)]">
          <div class="text-[0.8vw] text-white mb-[0.8vw] flex items-center">
            <ToolOutlined class="mr-[0.4vw] text-orange-400" />
            维护计划
          </div>
          <div class="space-y-[0.4vw] max-h-[8vw] overflow-y-auto">
            <div v-for="maintenance in maintenanceSchedule" :key="maintenance.id" class="bg-black/20 p-[0.4vw] rounded">
              <div class="flex justify-between items-center">
                <span class="text-[0.6vw] text-white">{{ maintenance.equipment }}</span>
                <span
                  class="text-[0.5vw] px-[0.3vw] py-[0.1vw] rounded"
                  :class="
                    maintenance.priority === 'high'
                      ? 'bg-red-500 text-white'
                      : maintenance.priority === 'medium'
                        ? 'bg-yellow-500 text-black'
                        : 'bg-green-500 text-white'
                  "
                >
                  {{ maintenance.priority === 'high' ? '紧急' : maintenance.priority === 'medium' ? '一般' : '低' }}
                </span>
              </div>
              <div class="text-[0.5vw] text-gray-400 mt-[0.2vw]">{{ maintenance.task }}</div>
              <div class="text-[0.5vw] text-gray-400">计划时间: {{ maintenance.scheduledDate }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import {
    CloudOutlined,
    ThunderboltOutlined,
    DashboardOutlined,
    BarChartOutlined,
    ToolOutlined,
    FireOutlined,
    CheckCircleOutlined,
  } from '@ant-design/icons-vue';

  // 暖通系统统计数据
  const hvacStats = ref([
    { label: '空调设备', value: '12台', valueClass: 'text-blue-400', icon: CloudOutlined, iconClass: 'text-blue-400' },
    { label: '通风设备', value: '8台', valueClass: 'text-yellow-400', icon: ThunderboltOutlined, iconClass: 'text-yellow-400' },
    { label: '平均温度', value: '22.5°C', valueClass: 'text-green-400', icon: FireOutlined, iconClass: 'text-green-400' },
    { label: '系统效率', value: '94.2%', valueClass: 'text-white', icon: CheckCircleOutlined, iconClass: 'text-white' },
  ]);

  // 空调设备数据
  const airConditioners = ref([
    { id: 1, name: '精密空调-01', status: 'running', temperature: 22, humidity: 45, power: 15.2, location: '1楼-主机房' },
    { id: 2, name: '精密空调-02', status: 'running', temperature: 23, humidity: 48, power: 14.8, location: '1楼-主机房' },
    { id: 3, name: '精密空调-03', status: 'stopped', temperature: 0, humidity: 0, power: 0, location: '2楼-机房' },
    { id: 4, name: '精密空调-04', status: 'running', temperature: 21, humidity: 42, power: 16.1, location: '2楼-机房' },
  ]);

  // 通风设备数据
  const ventilationFans = ref([
    { id: 1, name: '排风扇-01', status: 'running', speed: 1200, airflow: 3500, location: '1楼-主机房' },
    { id: 2, name: '排风扇-02', status: 'running', speed: 1150, airflow: 3200, location: '1楼-主机房' },
    { id: 3, name: '新风机-01', status: 'stopped', speed: 0, airflow: 0, location: '2楼-机房' },
    { id: 4, name: '新风机-02', status: 'running', speed: 980, airflow: 2800, location: '2楼-机房' },
  ]);

  // 环境传感器数据
  const environmentSensors = ref([
    { id: 1, location: '1楼-主机房-A区', temperature: 22.5, humidity: 45, pressure: 101325 },
    { id: 2, location: '1楼-主机房-B区', temperature: 23.1, humidity: 47, pressure: 101320 },
    { id: 3, location: '2楼-机房-A区', temperature: 21.8, humidity: 43, pressure: 101318 },
    { id: 4, location: '2楼-机房-B区', temperature: 24.2, humidity: 52, pressure: 101322 },
  ]);

  // 能耗统计数据
  const energyConsumption = ref([
    { type: '空调系统', value: '156.8kWh', daily: '3,765kWh', monthly: '112,950kWh', trend: 'down' },
    { type: '通风系统', value: '45.2kWh', daily: '1,085kWh', monthly: '32,550kWh', trend: 'up' },
    { type: '照明系统', value: '28.6kWh', daily: '686kWh', monthly: '20,580kWh', trend: 'down' },
  ]);

  // 维护计划数据
  const maintenanceSchedule = ref([
    { id: 1, equipment: '精密空调-01', task: '更换过滤网', scheduledDate: '2023-11-15', priority: 'high' },
    { id: 2, equipment: '排风扇-02', task: '清洁叶片', scheduledDate: '2023-11-18', priority: 'medium' },
    { id: 3, equipment: '新风机-01', task: '检查电机', scheduledDate: '2023-11-20', priority: 'low' },
    { id: 4, equipment: '精密空调-03', task: '系统检修', scheduledDate: '2023-11-22', priority: 'high' },
    { id: 5, equipment: '排风扇-01', task: '润滑保养', scheduledDate: '2023-11-25', priority: 'medium' },
  ]);

  // 控制空调设备
  const controlAC = (id, action) => {
    const ac = airConditioners.value.find((item) => item.id === id);
    if (ac) {
      if (action === 'start') {
        ac.status = 'running';
        ac.temperature = Math.floor(Math.random() * 5) + 20;
        ac.humidity = Math.floor(Math.random() * 10) + 40;
        ac.power = Math.floor(Math.random() * 5) + 14;
      } else {
        ac.status = 'stopped';
        ac.temperature = 0;
        ac.humidity = 0;
        ac.power = 0;
      }
    }
  };

  // 控制通风设备
  const controlFan = (id, action) => {
    const fan = ventilationFans.value.find((item) => item.id === id);
    if (fan) {
      if (action === 'start') {
        fan.status = 'running';
        fan.speed = Math.floor(Math.random() * 300) + 1000;
        fan.airflow = Math.floor(Math.random() * 1000) + 2500;
      } else {
        fan.status = 'stopped';
        fan.speed = 0;
        fan.airflow = 0;
      }
    }
  };
</script>

<style scoped>
  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(21, 39, 77, 0.3);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(255, 165, 0, 0.5);
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 165, 0, 0.7);
  }
</style>
