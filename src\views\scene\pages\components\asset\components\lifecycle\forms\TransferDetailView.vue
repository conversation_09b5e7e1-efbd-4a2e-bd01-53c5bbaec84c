<template>
  <div class="p-[1vw] space-y-[1vw]">
    <!-- 调拨基本信息 -->
    <div class="bg-blue-500/10 border border-blue-500/20 rounded p-[0.8vw]">
      <h3 class="text-[0.8vw] text-blue-400 font-semibold mb-[0.6vw] flex items-center">
        <span class="mr-[0.4vw]">🔄</span>
        调拨单号：{{ transfer?.transferCode }}
      </h3>
      <div class="grid grid-cols-3 gap-[0.8vw] text-[0.6vw]">
        <div
          ><span class="text-gray-400">资产名称：</span><span class="text-white">{{ transfer?.assetName }}</span></div
        >
        <div
          ><span class="text-gray-400">调拨类型：</span><span class="text-white">{{ getTypeText(transfer?.type) }}</span></div
        >
        <div
          ><span class="text-gray-400">当前状态：</span>
          <span
            :class="[
              transfer?.status === 'completed'
                ? 'text-green-400'
                : transfer?.status === 'in_transit'
                  ? 'text-blue-400'
                  : transfer?.status === 'pending'
                    ? 'text-yellow-400'
                    : 'text-red-400',
            ]"
            >{{ getStatusText(transfer?.status) }}</span
          >
        </div>
      </div>
    </div>

    <!-- 调拨详情 -->
    <div class="grid grid-cols-2 gap-[1vw]">
      <!-- 调拨信息 -->
      <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
        <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">调拨信息</h4>
        <div class="space-y-[0.4vw] text-[0.6vw]">
          <div class="flex justify-between">
            <span class="text-gray-400">调出方：</span>
            <span class="text-white">{{ transfer?.fromLocation }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">调入方：</span>
            <span class="text-white">{{ transfer?.toLocation }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">申请人：</span>
            <span class="text-white">{{ transfer?.requester }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">申请时间：</span>
            <span class="text-white">{{ transfer?.requestDate }}</span>
          </div>
        </div>
      </div>

      <!-- 进度信息 -->
      <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
        <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">进度信息</h4>
        <div class="space-y-[0.4vw] text-[0.6vw]">
          <div class="flex justify-between">
            <span class="text-gray-400">确认时间：</span>
            <span class="text-white">{{ transfer?.confirmDate || '待确认' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">完成时间：</span>
            <span class="text-white">{{ transfer?.completeDate || '未完成' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">处理人：</span>
            <span class="text-white">{{ transfer?.handler || '系统管理员' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 调拨原因 -->
    <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
      <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">调拨原因</h4>
      <div class="text-[0.6vw] text-gray-300">
        {{ transfer?.reason || '部门重组需要，将设备调拨至新的使用部门。' }}
      </div>
    </div>

    <!-- 调拨流程 -->
    <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
      <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">调拨流程</h4>
      <div class="space-y-[0.4vw]">
        <div v-for="step in transferSteps" :key="step.id" class="flex justify-between items-center p-[0.4vw] bg-white/5 rounded">
          <div>
            <div class="text-[0.5vw] text-white">{{ step.title }}</div>
            <div class="text-[0.4vw] text-gray-400">{{ step.time }}</div>
          </div>
          <span
            :class="[
              'px-[0.3vw] py-[0.1vw] rounded text-[0.4vw]',
              step.status === 'completed'
                ? 'bg-green-500/20 text-green-400'
                : step.status === 'current'
                  ? 'bg-blue-500/20 text-blue-400'
                  : 'bg-gray-500/20 text-gray-400',
            ]"
          >
            {{ step.status === 'completed' ? '已完成' : step.status === 'current' ? '进行中' : '待处理' }}
          </span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-[0.6vw] pt-[0.6vw] border-t border-white/10">
      <button
        v-if="transfer?.status === 'pending'"
        class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
        @click="confirmTransfer"
      >
        确认调拨
      </button>
      <button
        v-if="transfer?.status === 'in_transit'"
        class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
        @click="completeTransfer"
      >
        完成调拨
      </button>
      <button
        class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors"
        @click="printTransfer"
      >
        打印单据
      </button>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  const props = defineProps({
    transfer: { type: Object, default: null },
  });

  // 调拨流程步骤
  const transferSteps = computed(() => {
    const status = props.transfer?.status;
    return [
      {
        id: 1,
        title: '提交调拨申请',
        time: props.transfer?.requestDate + ' 09:30',
        status: 'completed',
      },
      {
        id: 2,
        title: '调拨确认',
        time: props.transfer?.confirmDate ? props.transfer.confirmDate + ' 14:20' : '待确认',
        status: status === 'pending' ? 'current' : status === 'cancelled' ? 'cancelled' : 'completed',
      },
      {
        id: 3,
        title: '资产转移',
        time: status === 'completed' ? props.transfer?.completeDate + ' 16:45' : '待执行',
        status: status === 'completed' ? 'completed' : status === 'in_transit' ? 'current' : 'pending',
      },
      {
        id: 4,
        title: '调拨完成',
        time: status === 'completed' ? props.transfer?.completeDate + ' 17:00' : '待完成',
        status: status === 'completed' ? 'completed' : 'pending',
      },
    ];
  });

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      pending: '待确认',
      in_transit: '调拨中',
      completed: '已完成',
      cancelled: '已取消',
    };
    return statusMap[status] || status;
  };

  const getTypeText = (type) => {
    const typeMap = {
      department: '部门调拨',
      location: '位置调拨',
      person: '人员调拨',
    };
    return typeMap[type] || type;
  };

  const confirmTransfer = () => {
    alert('确认调拨操作');
  };

  const completeTransfer = () => {
    alert('完成调拨操作');
  };

  const printTransfer = () => {
    alert('正在生成调拨单据...');
  };
</script>
