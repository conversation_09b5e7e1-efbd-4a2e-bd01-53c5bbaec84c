import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { PhysicsManager, PhysicsObjectType, PhysicsShapeType } from './PhysicsManager';

/**
 * 玩家物理控制器配置接口
 */
export interface PlayerPhysicsConfig {
  height: number; // 玩家高度（米）
  radius: number; // 玩家半径（米）
  mass: number; // 玩家质量（千克）
  jumpVelocity: number; // 跳跃初速度（米/秒）
  moveSpeed: number; // 移动速度（米/秒）
  gravity: number; // 重力加速度（米/秒²）
}

/**
 * 玩家物理控制器
 * 负责管理玩家的物理行为
 */
export class PlayerPhysicsController {
  private static instance: PlayerPhysicsController;
  private physicsManager: PhysicsManager;
  private playerBody: CANNON.Body | null = null;
  private isGrounded: boolean = false;
  private contactNormal: CANNON.Vec3 = new CANNON.Vec3(0, 1, 0);
  private upAxis: CANNON.Vec3 = new CANNON.Vec3(0, 1, 0);
  // 性能优化：预先创建所有可能需要的临时对象，避免运行时创建
  private tempVec: CANNON.Vec3 = new CANNON.Vec3();
  private position: THREE.Vector3 = new THREE.Vector3();
  private velocity: THREE.Vector3 = new THREE.Vector3();
  private movement: {
    forward: boolean;
    backward: boolean;
    left: boolean;
    right: boolean;
    jump: boolean;
  } = {
    forward: false,
    backward: false,
    left: false,
    right: false,
    jump: false,
  };
  private config: PlayerPhysicsConfig = {
    height: 1.7,
    radius: 0.3,
    mass: 70,
    jumpVelocity: 5,
    moveSpeed: 5,
    gravity: 9.82,
  };
  private cameraDirection: THREE.Vector3 = new THREE.Vector3(0, 0, -1);
  private cameraQuaternion: THREE.Quaternion = new THREE.Quaternion();

  // 性能优化：预先创建临时对象，避免每帧创建新对象
  private _tempMoveDirection: THREE.Vector3 = new THREE.Vector3();
  private _tempHorizontalVelocity: THREE.Vector2 = new THREE.Vector2();

  /**
   * 获取单例实例
   */
  public static getInstance(): PlayerPhysicsController {
    if (!PlayerPhysicsController.instance) {
      PlayerPhysicsController.instance = new PlayerPhysicsController();
    }
    return PlayerPhysicsController.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    this.physicsManager = PhysicsManager.getInstance();
  }

  /**
   * 初始化玩家物理控制器
   * @param config 玩家物理配置
   */
  public initialize(config?: Partial<PlayerPhysicsConfig>): void {
    // 合并配置
    if (config) {
      this.config = { ...this.config, ...config };
    }

    // 创建玩家物理刚体
    this._createPlayerBody();

    // 设置碰撞回调
    this._setupCollisionCallbacks();

    console.log('[PlayerPhysicsController] 玩家物理控制器初始化完成');
  }

  /**
   * 创建玩家物理刚体
   */
  private _createPlayerBody(): void {
    // 如果已经存在玩家刚体，先移除
    if (this.playerBody) {
      this.physicsManager['world'].removeBody(this.playerBody);
      this.playerBody = null;
    }

    // 创建简化的胶囊体形状 - 极度性能优化：减少分段数
    const shape = new CANNON.Cylinder(
      this.config.radius, // 顶部半径
      this.config.radius, // 底部半径
      this.config.height, // 高度
      8 // 分段数减少一半，大幅提高性能
    );

    // 创建玩家材质
    const playerMaterial = new CANNON.Material('player');

    // 创建玩家刚体 - 极度性能优化：减少物理计算
    this.playerBody = new CANNON.Body({
      mass: this.config.mass,
      material: playerMaterial,
      shape: shape,
      type: CANNON.BODY_TYPES.DYNAMIC,
      fixedRotation: true, // 固定旋转，防止玩家倒下
      linearDamping: 0.7, // 降低线性阻尼，减少计算量
      allowSleep: true, // 允许休眠，提高性能
      sleepSpeedLimit: 0.5, // 休眠速度阈值
      sleepTimeLimit: 0.5, // 休眠时间阈值
      position: new CANNON.Vec3(0, this.config.height / 2 + 0.1, 0), // 初始位置
    });

    // 添加到物理世界
    this.physicsManager['world'].addBody(this.playerBody);

    // 初始化位置和速度
    this.position.set(0, this.config.height / 2 + 0.1, 0);
    this.velocity.set(0, 0, 0);
  }

  /**
   * 设置碰撞回调 - 极度性能优化版本
   */
  private _setupCollisionCallbacks(): void {
    if (!this.playerBody) return;

    // 碰撞开始回调 - 使用更高效的碰撞检测
    // 性能优化：使用静态变量跟踪上次碰撞时间，减少回调频率
    let lastCollisionTime = 0;

    this.playerBody.addEventListener('collide', (event: any) => {
      // 性能优化：限制碰撞检测频率，每50ms最多处理一次
      const now = performance.now();
      if (now - lastCollisionTime < 50) {
        return;
      }
      lastCollisionTime = now;

      const { contact } = event;

      // 确定接触法线 - 简化计算
      if (contact.bi.id === this.playerBody?.id) {
        contact.ni.negate(this.contactNormal);
      } else {
        this.contactNormal.copy(contact.ni);
      }

      // 检查是否接触地面 - 使用更宽松的阈值，减少边缘情况
      if (this.contactNormal.dot(this.upAxis) > 0.4) {
        this.isGrounded = true;
      }
    });
  }

  /**
   * 设置玩家位置
   * @param position 位置
   */
  public setPosition(position: THREE.Vector3): void {
    if (!this.playerBody) return;

    // 更新物理刚体位置
    this.playerBody.position.set(position.x, position.y, position.z);
    this.playerBody.previousPosition.copy(this.playerBody.position);
    this.playerBody.interpolatedPosition.copy(this.playerBody.position);

    // 更新本地位置
    this.position.copy(position);
  }

  /**
   * 获取玩家位置
   */
  public getPosition(): THREE.Vector3 {
    if (this.playerBody) {
      // 从物理刚体更新位置
      this.position.set(this.playerBody.position.x, this.playerBody.position.y, this.playerBody.position.z);
    }
    return this.position.clone();
  }

  /**
   * 设置玩家速度
   * @param velocity 速度
   */
  public setVelocity(velocity: THREE.Vector3): void {
    if (!this.playerBody) return;

    // 更新物理刚体速度
    this.playerBody.velocity.set(velocity.x, velocity.y, velocity.z);

    // 更新本地速度
    this.velocity.copy(velocity);
  }

  /**
   * 获取玩家速度
   */
  public getVelocity(): THREE.Vector3 {
    if (this.playerBody) {
      // 从物理刚体更新速度
      this.velocity.set(this.playerBody.velocity.x, this.playerBody.velocity.y, this.playerBody.velocity.z);
    }
    return this.velocity.clone();
  }

  /**
   * 设置相机方向
   * @param direction 方向向量
   * @param quaternion 四元数
   */
  public setCameraDirection(direction: THREE.Vector3, quaternion: THREE.Quaternion): void {
    this.cameraDirection.copy(direction).normalize();
    this.cameraQuaternion.copy(quaternion);
  }

  /**
   * 设置移动输入
   * @param movement 移动输入
   */
  public setMovement(movement: { forward: boolean; backward: boolean; left: boolean; right: boolean; jump: boolean }): void {
    this.movement = { ...movement };
  }

  /**
   * 更新玩家物理
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    if (!this.playerBody) return;

    // 重置接地状态
    this.isGrounded = false;

    // 使用预先创建的临时对象，避免每帧创建新对象
    const moveDirection = this._tempMoveDirection;
    moveDirection.set(0, 0, 0);

    // 前后移动 - 使用简化的条件检查
    if (this.movement.forward) moveDirection.z -= 1;
    if (this.movement.backward) moveDirection.z += 1;

    // 左右移动 - 使用简化的条件检查
    if (this.movement.left) moveDirection.x -= 1;
    if (this.movement.right) moveDirection.x += 1;

    // 如果有移动输入 - 使用快速长度检查
    const hasMovement = moveDirection.x !== 0 || moveDirection.z !== 0;
    if (hasMovement) {
      // 标准化方向向量 - 只在必要时标准化
      if (Math.abs(moveDirection.x) === 1 && Math.abs(moveDirection.z) === 1) {
        // 对角线移动需要标准化
        moveDirection.normalize();
      }

      // 应用相机旋转到移动方向
      moveDirection.applyQuaternion(this.cameraQuaternion);
      moveDirection.y = 0; // 保持水平移动

      // 只在必要时标准化
      const lenSq = moveDirection.x * moveDirection.x + moveDirection.z * moveDirection.z;
      if (Math.abs(lenSq - 1.0) > 0.01) {
        const len = Math.sqrt(lenSq);
        if (len > 0) {
          moveDirection.x /= len;
          moveDirection.z /= len;
        }
      }

      // 计算移动速度 - 直接使用配置值
      const moveSpeed = this.config.moveSpeed;

      // 应用移动力 - 使用简化的条件检查
      if (this.isGrounded) {
        // 在地面上时直接设置速度
        this.playerBody.velocity.x = moveDirection.x * moveSpeed;
        this.playerBody.velocity.z = moveDirection.z * moveSpeed;
      } else {
        // 在空中时应用较小的力
        this.playerBody.velocity.x += moveDirection.x * moveSpeed * 0.1;
        this.playerBody.velocity.z += moveDirection.z * moveSpeed * 0.1;

        // 限制空中速度 - 使用预先创建的临时对象
        const horizontalVelocity = this._tempHorizontalVelocity;
        horizontalVelocity.set(this.playerBody.velocity.x, this.playerBody.velocity.z);

        const currentSpeed = horizontalVelocity.length();
        if (currentSpeed > moveSpeed) {
          // 使用简化的标准化和缩放
          const scale = moveSpeed / currentSpeed;
          this.playerBody.velocity.x *= scale;
          this.playerBody.velocity.z *= scale;
        }
      }
    } else if (this.isGrounded) {
      // 没有移动输入且在地面上时，应用摩擦力
      this.playerBody.velocity.x *= 0.8;
      this.playerBody.velocity.z *= 0.8;
    }

    // 处理跳跃
    if (this.movement.jump && this.isGrounded) {
      this.playerBody.velocity.y = this.config.jumpVelocity;
      this.isGrounded = false;
    }

    // 更新本地位置和速度
    this.position.set(this.playerBody.position.x, this.playerBody.position.y, this.playerBody.position.z);
    this.velocity.set(this.playerBody.velocity.x, this.playerBody.velocity.y, this.playerBody.velocity.z);
  }

  /**
   * 检查是否在地面上
   */
  public isOnGround(): boolean {
    return this.isGrounded;
  }

  /**
   * 销毁玩家物理控制器
   */
  public dispose(): void {
    if (this.playerBody) {
      this.physicsManager['world'].removeBody(this.playerBody);
      this.playerBody = null;
    }
  }
}
