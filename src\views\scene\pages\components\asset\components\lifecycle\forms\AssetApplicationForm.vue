<template>
  <div class="p-[1vw] space-y-[1vw]">
    <div class="grid grid-cols-2 gap-[1vw]">
      <!-- 申请信息 -->
      <div class="space-y-[0.8vw]">
        <h3 class="text-[0.8vw] text-white font-semibold mb-[0.6vw]">申请信息</h3>
        
        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">申请人 *</label>
          <input
            v-model="formData.applicant"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入申请人姓名"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">所属部门 *</label>
          <select
            v-model="formData.department"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="">请选择部门</option>
            <option value="IT">IT部门</option>
            <option value="HR">人力资源部</option>
            <option value="Finance">财务部</option>
            <option value="Operations">运营部</option>
            <option value="Marketing">市场部</option>
            <option value="Sales">销售部</option>
          </select>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">联系电话</label>
          <input
            v-model="formData.phone"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入联系电话"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">邮箱地址</label>
          <input
            v-model="formData.email"
            type="email"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入邮箱地址"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">申请日期 *</label>
          <input
            v-model="formData.applicationDate"
            type="date"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          />
        </div>
      </div>

      <!-- 资产需求 -->
      <div class="space-y-[0.8vw]">
        <h3 class="text-[0.8vw] text-white font-semibold mb-[0.6vw]">资产需求</h3>
        
        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">资产类型 *</label>
          <select
            v-model="formData.assetType"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="">请选择资产类型</option>
            <option value="笔记本电脑">笔记本电脑</option>
            <option value="台式电脑">台式电脑</option>
            <option value="显示器">显示器</option>
            <option value="打印机">打印机</option>
            <option value="服务器">服务器</option>
            <option value="网络设备">网络设备</option>
            <option value="其他设备">其他设备</option>
          </select>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">申请数量 *</label>
          <input
            v-model.number="formData.quantity"
            type="number"
            min="1"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入申请数量"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">规格要求</label>
          <textarea
            v-model="formData.specifications"
            rows="3"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400 resize-none"
            placeholder="请输入具体的规格要求"
          ></textarea>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">期望获得时间</label>
          <input
            v-model="formData.expectedDate"
            type="date"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">紧急程度</label>
          <select
            v-model="formData.urgency"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="low">一般</option>
            <option value="medium">紧急</option>
            <option value="high">非常紧急</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 申请原因 -->
    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">申请原因 *</label>
      <textarea
        v-model="formData.reason"
        rows="4"
        class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400 resize-none"
        placeholder="请详细说明申请原因和用途"
      ></textarea>
    </div>

    <!-- 预算信息 -->
    <div class="grid grid-cols-2 gap-[1vw]">
      <div>
        <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">预算范围</label>
        <input
          v-model.number="formData.budget"
          type="number"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          placeholder="请输入预算金额"
        />
      </div>
      
      <div>
        <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">成本中心</label>
        <input
          v-model="formData.costCenter"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          placeholder="请输入成本中心代码"
        />
      </div>
    </div>

    <!-- 审批流程 -->
    <div>
      <h3 class="text-[0.8vw] text-white font-semibold mb-[0.6vw]">审批流程</h3>
      <div class="bg-black/20 rounded p-[0.8vw]">
        <div class="flex items-center space-x-[1vw]">
          <div class="flex items-center space-x-[0.4vw]">
            <div class="w-[0.8vw] h-[0.8vw] bg-blue-500 rounded-full"></div>
            <span class="text-[0.6vw] text-white">部门主管审批</span>
          </div>
          <div class="flex-1 h-[0.1vw] bg-gray-600"></div>
          <div class="flex items-center space-x-[0.4vw]">
            <div class="w-[0.8vw] h-[0.8vw] bg-gray-600 rounded-full"></div>
            <span class="text-[0.6vw] text-gray-400">IT部门审批</span>
          </div>
          <div class="flex-1 h-[0.1vw] bg-gray-600"></div>
          <div class="flex items-center space-x-[0.4vw]">
            <div class="w-[0.8vw] h-[0.8vw] bg-gray-600 rounded-full"></div>
            <span class="text-[0.6vw] text-gray-400">资产分配</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 附件上传 -->
    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">相关附件</label>
      <div class="border-2 border-dashed border-white/20 rounded p-[1vw] text-center">
        <div class="text-[0.6vw] text-gray-400 mb-[0.4vw]">
          上传相关文件（如需求说明、预算申请等）
        </div>
        <div class="text-[0.5vw] text-gray-500">
          支持 PDF、DOC、XLS、JPG、PNG 格式
        </div>
        <input
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
          class="hidden"
          @change="handleFileUpload"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    formData: {
      type: Object,
      default: () => ({})
    }
  });

  const emit = defineEmits(['update:formData']);

  // 表单数据
  const formData = ref({
    applicant: '',
    department: '',
    phone: '',
    email: '',
    applicationDate: new Date().toISOString().split('T')[0],
    assetType: '',
    quantity: 1,
    specifications: '',
    expectedDate: '',
    urgency: 'low',
    reason: '',
    budget: null,
    costCenter: '',
    attachments: [],
    ...props.formData
  });

  // 监听表单数据变化
  watch(formData, (newValue) => {
    emit('update:formData', newValue);
  }, { deep: true });

  // 文件上传处理
  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    formData.value.attachments = [...formData.value.attachments, ...files];
  };
</script>
