<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 节能统计概览 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-chart-pie mr-[0.4vw] text-blue-400"></i>
        节能统计概览
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in savingsStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.trend" class="text-[0.5vw] mt-[0.2vw]" :class="stat.trendClass">
            {{ stat.trend }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：节能项目统计 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-leaf mr-[0.4vw] text-blue-400"></i>
            节能项目统计
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="period in timePeriods"
              :key="period.key"
              @click="activePeriod = period.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activePeriod === period.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30',
              ]"
            >
              {{ period.label }}
            </button>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div v-for="project in energyProjects" :key="project.id" class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all">
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <i :class="project.icon" class="mr-[0.6vw] text-green-400"></i>
                  <span class="text-[0.65vw] text-white font-medium">{{ project.name }}</span>
                </div>
                <span class="text-[0.6vw] text-green-400">{{ project.savings }}</span>
              </div>
              <div class="text-[0.6vw] text-gray-400 mb-[0.3vw]">{{ project.description }}</div>
              <div class="flex justify-between text-[0.6vw]">
                <span class="text-gray-400">投资回收期：{{ project.paybackPeriod }}</span>
                <span class="text-gray-400"
                  >实施状态：<span class="text-green-400">{{ project.status }}</span></span
                >
              </div>
              <div class="mt-[0.3vw]">
                <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                  <div class="bg-green-400 h-[0.3vw] rounded-full transition-all" :style="{ width: project.progress + '%' }"></div>
                </div>
                <div class="text-[0.5vw] text-gray-400 mt-[0.2vw]">完成进度：{{ project.progress }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：节能效果和成本分析 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 节能效果统计 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-chart-bar mr-[0.4vw] text-blue-400"></i>
            节能效果统计
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="effect in savingsEffects" :key="effect.category" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ effect.category }}</span>
                <span class="text-[0.6vw] text-green-400">{{ effect.amount }}</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>占比：{{ effect.percentage }}%</span>
                <span>同比：{{ effect.yearOverYear }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 成本效益分析 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-coins mr-[0.4vw] text-blue-400"></i>
            成本效益分析
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="cost in costAnalysis" :key="cost.item" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ cost.item }}</span>
              <span class="text-[0.6vw] text-white font-medium">{{ cost.value }}</span>
            </div>
          </div>

          <div class="mt-[0.6vw] pt-[0.6vw] border-t border-gray-600">
            <div class="flex justify-between text-[0.6vw]">
              <span class="text-gray-400">投资回报率</span>
              <span class="text-green-400 font-medium">{{ roi }}</span>
            </div>
          </div>
        </div>

        <!-- 节能目标达成 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-target mr-[0.4vw] text-blue-400"></i>
            节能目标达成
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="target in savingsTargets" :key="target.name" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ target.name }}</span>
                <span class="text-[0.6vw]" :class="getTargetStatusClass(target.achievement)"> {{ target.achievement }}% </span>
              </div>
              <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                <div
                  :class="getTargetProgressClass(target.achievement)"
                  class="h-[0.3vw] rounded-full transition-all"
                  :style="{ width: Math.min(target.achievement, 100) + '%' }"
                ></div>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400 mt-[0.2vw]">
                <span>目标：{{ target.target }}</span>
                <span>实际：{{ target.actual }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';

  // 节能统计概览数据
  const savingsStats = ref([
    {
      label: '总节能量',
      value: '2,456 kWh',
      valueClass: 'text-green-400',
      trend: '↑ 15.2%',
      trendClass: 'text-green-400',
    },
    {
      label: '节能率',
      value: '18.5%',
      valueClass: 'text-blue-400',
      trend: '↑ 3.2%',
      trendClass: 'text-green-400',
    },
    {
      label: '节约成本',
      value: '¥18.6万',
      valueClass: 'text-yellow-400',
      trend: '↑ 12.8%',
      trendClass: 'text-green-400',
    },
    {
      label: '减排CO₂',
      value: '1.85 t',
      valueClass: 'text-green-400',
      trend: '↑ 16.5%',
      trendClass: 'text-green-400',
    },
    {
      label: '项目完成率',
      value: '92.3%',
      valueClass: 'text-blue-400',
      trend: '↑ 5.1%',
      trendClass: 'text-green-400',
    },
  ]);

  // 时间周期
  const timePeriods = ref([
    { key: 'month', label: '本月' },
    { key: 'quarter', label: '本季度' },
    { key: 'year', label: '本年度' },
  ]);

  const activePeriod = ref('month');

  // 节能项目
  const energyProjects = ref([
    {
      id: 1,
      name: 'LED照明改造',
      icon: 'fas fa-lightbulb',
      description: '将传统照明设备更换为高效LED灯具',
      savings: '节能40%',
      paybackPeriod: '1.2年',
      status: '已完成',
      progress: 100,
    },
    {
      id: 2,
      name: '智能空调控制系统',
      icon: 'fas fa-snowflake',
      description: '基于AI算法的智能温控系统',
      savings: '节能25%',
      paybackPeriod: '1.8年',
      status: '运行中',
      progress: 95,
    },
    {
      id: 3,
      name: '变频器节能改造',
      icon: 'fas fa-cog',
      description: '风机、水泵等设备变频技术应用',
      savings: '节能30%',
      paybackPeriod: '2.1年',
      status: '已完成',
      progress: 100,
    },
    {
      id: 4,
      name: '余热回收系统',
      icon: 'fas fa-recycle',
      description: '服务器废热回收利用系统',
      savings: '节能35%',
      paybackPeriod: '2.5年',
      status: '实施中',
      progress: 78,
    },
    {
      id: 5,
      name: '能源管理系统升级',
      icon: 'fas fa-chart-line',
      description: '智能化能源监控与管理平台',
      savings: '节能15%',
      paybackPeriod: '1.5年',
      status: '已完成',
      progress: 100,
    },
  ]);

  // 节能效果统计
  const savingsEffects = ref([
    { category: '照明节能', amount: '856 kWh', percentage: 35, yearOverYear: '↑ 18%' },
    { category: '空调节能', amount: '742 kWh', percentage: 30, yearOverYear: '↑ 22%' },
    { category: '设备优化', amount: '524 kWh', percentage: 21, yearOverYear: '↑ 15%' },
    { category: '余热回收', amount: '334 kWh', percentage: 14, yearOverYear: '↑ 28%' },
  ]);

  // 成本效益分析
  const costAnalysis = ref([
    { item: '总投资成本', value: '¥125.6万' },
    { item: '年节约成本', value: '¥68.4万' },
    { item: '累计节约', value: '¥186.2万' },
    { item: '维护成本', value: '¥8.5万/年' },
  ]);

  const roi = ref('145.2%');

  // 节能目标
  const savingsTargets = ref([
    { name: '年度节能目标', target: '2,400 kWh', actual: '2,456 kWh', achievement: 102.3 },
    { name: '成本节约目标', target: '¥16万', actual: '¥18.6万', achievement: 116.3 },
    { name: 'CO₂减排目标', target: '1.8 t', actual: '1.85 t', achievement: 102.8 },
    { name: '能效提升目标', target: '15%', actual: '18.5%', achievement: 123.3 },
  ]);

  // 获取目标状态样式
  const getTargetStatusClass = (achievement) => {
    if (achievement >= 100) return 'text-green-400';
    if (achievement >= 80) return 'text-yellow-400';
    return 'text-red-400';
  };

  // 获取目标进度条样式
  const getTargetProgressClass = (achievement) => {
    if (achievement >= 100) return 'bg-green-400';
    if (achievement >= 80) return 'bg-yellow-400';
    return 'bg-red-400';
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
