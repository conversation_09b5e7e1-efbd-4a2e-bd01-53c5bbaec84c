import { BasicColumn } from '/@/components/Table/src/types/table';

export function getBasicColumns(): BasicColumn[] {
  return [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      width: 150,
    },
    {
      title: '信号编号',
      dataIndex: 'signalId',
      width: 120,
    },
    {
      title: '告警描述',
      dataIndex: 'alarmDesc',
      width: 200,
    },
    {
      title: '告警级别',
      dataIndex: 'alarmLevel',
      width: 100,
      customRender: ({ text }) => {
        const levelMap: Record<string, string> = {
          '0': '正常数据',
          '1': '一级告警',
          '2': '二级告警',
          '3': '三级告警',
          '4': '四级告警',
          '5': '操作事件',
          '6': '无效数据',
        };
        return levelMap[text as string] || text;
      },
    },
    {
      title: '告警时间',
      dataIndex: 'createDate',
      width: 180,
    },
  ];
}
