/* 全局动画定义 */
@keyframes scan {
  from {
    transform: translateX(-100%) rotate(45deg);
  }
  to {
    transform: translateX(100%) rotate(45deg);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(36, 108, 249, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(36, 108, 249, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(36, 108, 249, 0);
  }
}

/* 控制按钮通用样式 */
.control-btn {
  width: 2.2vw;
  height: 2.2vw;
  background-color: rgba(23, 43, 77, 0.8);
  border: 1px solid rgba(36, 108, 249, 0.3);
  border-radius: 0.3vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.control-btn:hover {
  background-color: rgba(36, 108, 249, 0.2);
  border-color: rgba(36, 108, 249, 0.5);
  transform: scale(1.05);
}

.control-btn:active {
  transform: scale(0.98);
}

.control-btn::before {
  content: '';
  position: absolute;
  left: -100%;
  top: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(90deg, transparent, rgba(36, 108, 249, 0.3), transparent);
  transform: rotate(45deg);
}

.control-btn:hover::before {
  animation: scan 2s linear infinite;
}

.control-btn.active {
  background-color: rgba(36, 108, 249, 0.3);
  border-color: rgba(36, 108, 249, 0.8);
  box-shadow: 0 0 10px rgba(36, 108, 249, 0.3);
}

.control-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.control-btn.disabled:hover {
  background-color: rgba(23, 43, 77, 0.8);
  border-color: rgba(36, 108, 249, 0.3);
  transform: none;
}

.control-btn.disabled:hover::before {
  animation: none;
}

/* 工具栏按钮样式 */
.toolbar-btn {
  width: 1.8vw;
  height: 1.8vw;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 0.2vw;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.toolbar-btn:hover {
  background-color: rgba(36, 108, 249, 0.2);
  transform: scale(1.05);
  box-shadow: 0 0 8px rgba(36, 108, 249, 0.3);
}

.toolbar-btn:active {
  transform: scale(0.98);
  box-shadow: none;
}

.toolbar-btn.active {
  background-color: rgba(36, 108, 249, 0.3);
  box-shadow: 0 0 8px rgba(36, 108, 249, 0.4);
}

.toolbar-btn.active svg {
  color: #3b82f6;
}

.toolbar-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.toolbar-btn.disabled:hover {
  background-color: transparent;
  transform: none;
  box-shadow: none;
}
