// Web Worker for edge geometry preprocessing
// 导入必要的Three.js类
import * as THREE from 'three';
import { EdgesGeometry } from 'three';

// 定义消息类型
interface PreprocessRequest {
  meshId: string;
  geometryData: {
    position: number[];
    normal?: number[];
    uv?: number[];
    index?: number[];
  };
  threshold: number;
  batchId: number;
}

interface PreprocessResponse {
  meshId: string;
  edgesData: {
    position: number[];
    index?: number[];
  };
  batchId: number;
}

// 处理主线程发来的消息
self.onmessage = function(e: MessageEvent<PreprocessRequest>) {
  const { geometryData, threshold, meshId, batchId } = e.data;
  
  try {
    // 重建几何体
    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(geometryData.position), 3));
    
    if (geometryData.normal) {
      geometry.setAttribute('normal', new THREE.BufferAttribute(new Float32Array(geometryData.normal), 3));
    }
    
    if (geometryData.uv) {
      geometry.setAttribute('uv', new THREE.BufferAttribute(new Float32Array(geometryData.uv), 2));
    }
    
    if (geometryData.index) {
      geometry.setIndex(new THREE.BufferAttribute(new Uint32Array(geometryData.index), 1));
    }
    
    // 计算边缘几何体
    const edges = new EdgesGeometry(geometry, threshold);
    
    // 提取边缘数据
    const positionAttribute = edges.getAttribute('position');
    const edgesData = {
      position: Array.from(positionAttribute.array),
      index: edges.index ? Array.from(edges.index.array) : undefined
    };
    
    // 发送处理结果回主线程
    const response: PreprocessResponse = {
      meshId,
      edgesData,
      batchId
    };
    
    self.postMessage(response);
  } catch (error) {
    // 发送错误信息回主线程
    self.postMessage({
      meshId,
      error: (error as Error).message,
      batchId
    });
  }
};
