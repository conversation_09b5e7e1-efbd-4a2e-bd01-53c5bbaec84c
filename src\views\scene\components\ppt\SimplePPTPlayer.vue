<template>
  <div class="simple-ppt-player h-full flex flex-col bg-[#0a0f1c]">
    <!-- 标题栏 -->
    <div class="flex items-center justify-between p-4 bg-[#1a2332] border-b border-[#2a3441]">
      <div class="flex items-center">
        <FileTextOutlined class="text-[#3B8EE6] text-lg mr-2" />
        <span class="text-white text-sm font-medium">图片演示</span>
      </div>
      <div class="flex items-center space-x-2">
        <span class="text-gray-400 text-xs">{{ currentSlide + 1 }} / {{ totalSlides }}</span>
        <CustomButton icon="upload" @click="showImageManager = true" size="small" type="primary"> 管理图片 </CustomButton>
        <a-button size="small" type="text" @click="$emit('close')" class="text-gray-400 hover:text-white">
          <CloseOutlined />
        </a-button>
      </div>
    </div>

    <!-- 图片内容区域 -->
    <div class="flex-1 relative overflow-hidden">
      <!-- 图片显示区域 -->
      <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-900 to-gray-800 relative">
        <!-- 有图片时显示图片 -->
        <div v-if="images.length > 0 && currentImage" class="w-full h-full flex items-center justify-center">
          <img :src="currentImage.url" :alt="currentImage.name" class="max-w-full max-h-full object-contain" @error="handleImageError" />
        </div>

        <!-- 无图片时显示提示 -->
        <div v-else class="text-center text-gray-400">
          <div class="text-6xl mb-4">📷</div>
          <div class="text-xl mb-2">暂无图片</div>
          <div class="text-sm">点击"管理图片"按钮上传图片</div>
        </div>

        <!-- 导航覆盖层 -->
        <div class="absolute inset-0 pointer-events-none">
          <!-- 左侧导航区域 -->
          <div
            class="absolute left-0 top-0 w-16 h-full pointer-events-auto cursor-pointer hover:bg-black/10 transition-colors flex items-center justify-center"
            @click="previousSlide"
            v-if="currentSlide > 0"
          >
            <LeftOutlined class="text-gray-600 text-2xl opacity-70 hover:opacity-100" />
          </div>

          <!-- 右侧导航区域 -->
          <div
            class="absolute right-0 top-0 w-16 h-full pointer-events-auto cursor-pointer hover:bg-black/10 transition-colors flex items-center justify-center"
            @click="nextSlide"
            v-if="currentSlide < totalSlides - 1"
          >
            <RightOutlined class="text-gray-600 text-2xl opacity-70 hover:opacity-100" />
          </div>
        </div>
      </div>
    </div>

    <!-- 控制栏 - 调整底部间距避免被遮挡 -->
    <div class="p-4 pb-16 bg-[#1a2332] border-t border-[#2a3441]">
      <div class="flex items-center justify-between">
        <!-- 播放控制 -->
        <div class="flex items-center space-x-2">
          <CustomButton :icon="isAutoPlaying ? 'pause' : 'play'" @click="toggleAutoPlay" type="primary">
            {{ isAutoPlaying ? '暂停' : '自动播放' }}
          </CustomButton>
          <CustomSelect v-model:value="autoPlayInterval" :options="intervalOptions" :disabled="!isAutoPlaying" style="width: 100px" />
        </div>

        <!-- 页面导航 -->
        <div class="flex items-center space-x-2">
          <CustomButton icon="left" @click="previousSlide" :disabled="currentSlide <= 0"> 上一页 </CustomButton>
          <CustomInputNumber v-model:value="jumpToSlide" :min="1" :max="totalSlides" style="width: 80px" @pressEnter="goToSlide" />
          <CustomButton icon="right" @click="nextSlide" :disabled="currentSlide >= totalSlides - 1"> 下一页 </CustomButton>
        </div>

        <!-- 视角绑定 -->
        <div class="flex items-center space-x-2">
          <CustomButton icon="eye" @click="showViewBindingModal = true"> 视角绑定 </CustomButton>
          <CustomButton type="primary" variant="ghost" @click="bindCurrentView"> 绑定当前视角 </CustomButton>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="mt-3">
        <CustomSlider
          v-model:value="currentSlide"
          :min="0"
          :max="totalSlides - 1"
          :step="1"
          @change="goToSlideBySlider"
          :tooltip-formatter="(value) => `第 ${value + 1} 页`"
        />
      </div>
    </div>

    <!-- 视角绑定模态框 -->
    <ViewBindingModal
      v-model:visible="showViewBindingModal"
      :current-slide="currentSlide"
      :total-slides="totalSlides"
      @bind-view="handleViewBinding"
      @remove-binding="handleRemoveBinding"
    />

    <!-- 图片管理模态框 -->
    <a-modal v-model:visible="showImageManager" title="图片管理" width="900px" :footer="null" :maskClosable="false">
      <div class="image-manager">
        <!-- 上传区域 -->
        <div class="mb-6">
          <a-upload-dragger
            v-model:fileList="fileList"
            name="file"
            multiple
            accept="image/*"
            :before-upload="beforeUpload"
            :show-upload-list="false"
            @change="handleUploadChange"
          >
            <p class="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p class="ant-upload-text">点击或拖拽图片到此区域上传</p>
            <p class="ant-upload-hint">
              支持单个或批量上传，支持 JPG、PNG、GIF 格式<br />
              <span class="text-green-600">✨ 自动智能压缩，单张图片可达20MB</span><br />
              <span class="text-blue-600">压缩后目标大小: 200KB以内，保持高质量</span>
            </p>
          </a-upload-dragger>
        </div>

        <!-- 图片列表 -->
        <div class="mb-4">
          <div class="flex items-center justify-between mb-3">
            <h4 class="text-lg font-medium">图片列表 ({{ images.length }})</h4>
            <div class="space-x-2">
              <a-button @click="clearAllImages" danger size="small" :disabled="images.length === 0"> 清空所有 </a-button>
              <a-button @click="showImageManager = false" size="small"> 关闭 </a-button>
            </div>
          </div>

          <!-- 图片网格 -->
          <div v-if="images.length > 0" class="grid grid-cols-4 gap-4 max-h-96 overflow-y-auto">
            <div
              v-for="(image, index) in images"
              :key="image.id"
              class="relative group border-2 rounded-lg overflow-hidden cursor-move"
              :class="{ 'border-blue-500': index === currentSlide, 'border-gray-200': index !== currentSlide }"
              draggable="true"
              @dragstart="handleDragStart(index)"
              @dragover.prevent
              @drop="handleDrop(index)"
            >
              <!-- 图片预览 -->
              <div class="aspect-video bg-gray-100 flex items-center justify-center">
                <img :src="image.url" :alt="image.name" class="max-w-full max-h-full object-cover" @error="handleImageError" />
              </div>

              <!-- 图片信息 -->
              <div class="p-2 bg-white">
                <div class="text-xs text-gray-600 truncate" :title="image.name">
                  {{ image.name }}
                </div>
                <div class="text-xs text-gray-400">
                  {{ formatFileSize(image.size) }}
                  <span v-if="image.compressionRatio" class="text-green-600 ml-1"> (压缩{{ image.compressionRatio }}%) </span>
                </div>
                <div v-if="image.originalSize" class="text-xs text-gray-400"> 原始: {{ formatFileSize(image.originalSize) }} </div>
              </div>

              <!-- 操作按钮 -->
              <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <a-button size="small" danger type="primary" @click="removeImage(index)" class="!p-1">
                  <DeleteOutlined />
                </a-button>
              </div>

              <!-- 序号 -->
              <div class="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                {{ index + 1 }}
              </div>

              <!-- 当前图片标识 -->
              <div v-if="index === currentSlide" class="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded"> 当前 </div>
            </div>
          </div>

          <!-- 无图片提示 -->
          <div v-else class="text-center text-gray-400 py-8">
            <div class="text-4xl mb-2">📷</div>
            <div>暂无图片，请上传图片</div>
          </div>
        </div>

        <!-- 操作说明 -->
        <div class="text-sm text-gray-500 bg-gray-50 p-3 rounded">
          <div class="font-medium mb-1">操作说明：</div>
          <ul class="space-y-1">
            <li>• 拖拽图片可以调整顺序</li>
            <li>• 蓝色边框表示当前显示的图片</li>
            <li>• 点击删除按钮可以移除图片</li>
            <li>• 图片数据保存在浏览器本地存储中</li>
          </ul>

          <!-- 调试信息 -->
          <div class="mt-3 pt-3 border-t border-gray-200">
            <div class="font-medium mb-1">存储状态：</div>
            <div class="text-xs space-y-1">
              <div>内存中图片数量: {{ images.length }}</div>
              <div>localStorage中图片数量: {{ getStorageImageCount() }}</div>
              <div>当前数据大小: {{ formatFileSize(getStorageSize()) }}</div>
              <div>localStorage总使用量: {{ formatFileSize(getTotalStorageSize()) }}</div>
              <div>存储键名: {{ STORAGE_KEY }}</div>
            </div>
            <div class="mt-2 space-x-2">
              <a-button size="small" @click="forceReloadFromStorage" type="link" class="!p-0 !h-auto"> 重新加载 </a-button>
              <a-button size="small" @click="showStorageInfo" type="link" class="!p-0 !h-auto"> 存储详情 </a-button>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
  import {
    CloseOutlined,
    LeftOutlined,
    RightOutlined,
    PlayCircleOutlined,
    PauseOutlined,
    EyeOutlined,
    FileTextOutlined,
    InboxOutlined,
    DeleteOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { useGlobalThreeStore } from '../../store/globalThreeStore';
  import type { PPTViewBinding } from '../../store/globalThreeStore';
  import ViewBindingModal from './ViewBindingModal.vue';
  import { CameraController } from '../../lib/CameraController';
  import CustomButton from './CustomButton.vue';
  import CustomSelect from './CustomSelect.vue';
  import CustomInputNumber from './CustomInputNumber.vue';
  import CustomSlider from './CustomSlider.vue';

  // 图片接口定义
  interface ImageItem {
    id: string;
    name: string;
    url: string;
    size: number;
    type: string;
    originalSize?: number;
    compressionRatio?: string;
  }

  // Props
  interface Props {
    pptPath: string;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    close: [];
    slideChange: [slideIndex: number];
  }>();

  // Store
  const globalThreeStore = useGlobalThreeStore();

  // 响应式数据
  const currentSlide = ref(0);
  const totalSlides = ref(0);
  const jumpToSlide = ref(1);
  const isAutoPlaying = ref(false);
  const autoPlayInterval = ref(5000);
  const showViewBindingModal = ref(false);
  const showImageManager = ref(false);

  // 图片相关数据
  const images = ref<ImageItem[]>([]);
  const fileList = ref([]);
  const draggedIndex = ref<number | null>(null);

  // 自动播放定时器
  let autoPlayTimer: number | null = null;

  // 选项数据
  const intervalOptions = ref([
    { value: 3000, label: '3秒' },
    { value: 5000, label: '5秒' },
    { value: 8000, label: '8秒' },
    { value: 10000, label: '10秒' },
  ]);

  // 本地存储键名
  const STORAGE_KEY = 'ppt-images';

  // 计算属性
  const currentImage = computed(() => {
    return images.value[currentSlide.value] || null;
  });

  // 方法
  const initializePPT = () => {
    loadImagesFromStorage();
    updateTotalSlides();
    console.log('图片演示初始化成功，总页数:', totalSlides.value);
  };

  // 从本地存储加载图片
  const loadImagesFromStorage = () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        images.value = JSON.parse(stored);
      }
    } catch (error) {
      console.error('加载图片数据失败:', error);
      images.value = [];
    }
  };

  // 保存图片到本地存储
  const saveImagesToStorage = () => {
    try {
      const dataToSave = JSON.stringify(images.value);
      const dataSize = new Blob([dataToSave]).size;

      // 检查数据大小
      if (dataSize > 5 * 1024 * 1024) {
        // 5MB限制
        message.error('数据过大，无法保存到localStorage');
        return false;
      }

      localStorage.setItem(STORAGE_KEY, dataToSave);
      console.log(`已保存 ${images.value.length} 张图片到localStorage，大小: ${formatFileSize(dataSize)}`);
      return true;
    } catch (error) {
      console.error('保存图片数据失败:', error);

      if (error instanceof DOMException && error.name === 'QuotaExceededError') {
        message.error({
          content: '存储空间不足！请删除一些图片或清空所有图片后重试。',
          duration: 5,
        });

        // 尝试恢复到之前的状态
        try {
          loadImagesFromStorage();
        } catch (e) {
          console.error('恢复数据失败:', e);
        }
      } else {
        message.error('保存图片数据失败');
      }
      return false;
    }
  };

  // 更新总页数
  const updateTotalSlides = () => {
    totalSlides.value = images.value.length;
    globalThreeStore.setPPTTotalSlides(totalSlides.value);

    // 如果当前页超出范围，重置到第一页
    if (currentSlide.value >= totalSlides.value && totalSlides.value > 0) {
      currentSlide.value = 0;
      jumpToSlide.value = 1;
    }
  };

  const previousSlide = () => {
    if (currentSlide.value > 0) {
      goToSlide(currentSlide.value - 1);
    }
  };

  const nextSlide = () => {
    if (currentSlide.value < totalSlides.value - 1) {
      goToSlide(currentSlide.value + 1);
    }
  };

  const goToSlide = (slideIndex?: number) => {
    const targetSlide = slideIndex !== undefined ? slideIndex : jumpToSlide.value - 1;
    if (targetSlide >= 0 && targetSlide < totalSlides.value) {
      currentSlide.value = targetSlide;
      jumpToSlide.value = targetSlide + 1;

      // 更新store状态
      globalThreeStore.setPPTCurrentSlide(targetSlide);

      // 触发视角切换
      triggerViewChange(targetSlide);

      // 触发事件
      emit('slideChange', targetSlide);
    }
  };

  // 图片上传相关方法
  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return false;
    }

    // 放宽文件大小限制，因为我们会自动压缩
    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
      message.error('图片大小不能超过 20MB!');
      return false;
    }

    return false; // 阻止自动上传，手动处理
  };

  const handleUploadChange = (info: any) => {
    const { fileList } = info;

    // 处理新上传的文件
    const newFiles = fileList.filter(
      (file: any) => file.originFileObj && file.status !== 'done' && !images.value.find((img) => img.name === file.name && img.size === file.size)
    );

    if (newFiles.length === 0) {
      return;
    }

    let processedCount = 0;
    const totalFiles = newFiles.length;

    newFiles.forEach((file: any) => {
      // 显示压缩进度
      const hideLoading = message.loading(`正在压缩图片 ${file.name}...`, 0);

      compressImage(file.originFileObj)
        .then((compressedDataUrl) => {
          hideLoading();

          // 计算压缩后的大小
          const compressedSize = Math.round((compressedDataUrl.length * 3) / 4); // base64大小估算
          const originalSize = file.size;
          const compressionRatio = (((originalSize - compressedSize) / originalSize) * 100).toFixed(1);

          const imageItem: ImageItem = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            name: file.name,
            url: compressedDataUrl,
            size: compressedSize,
            type: file.type,
            originalSize: originalSize,
            compressionRatio: compressionRatio,
          };

          images.value.push(imageItem);
          processedCount++;

          console.log(
            `图片 ${file.name} 压缩完成: ${formatFileSize(originalSize)} → ${formatFileSize(compressedSize)} (压缩率: ${compressionRatio}%)`
          );

          // 所有文件处理完成后统一保存和更新
          if (processedCount === totalFiles) {
            const saveSuccess = saveImagesToStorage();

            if (saveSuccess) {
              updateTotalSlides();

              // 清空fileList，避免重复处理
              fileList.value = [];

              if (totalFiles === 1) {
                message.success(`图片 ${file.name} 上传成功 (压缩率: ${compressionRatio}%)`);
              } else {
                message.success(`成功上传 ${totalFiles} 张图片 (已自动压缩)`);
              }
            } else {
              // 保存失败，移除刚添加的图片
              images.value.splice(-totalFiles, totalFiles);
              fileList.value = [];
              message.error('上传失败，存储空间不足');
            }
          }
        })
        .catch((error) => {
          hideLoading();
          console.error(`图片 ${file.name} 压缩失败:`, error);
          message.error(`图片 ${file.name} 压缩失败`);
          processedCount++;

          if (processedCount === totalFiles) {
            fileList.value = [];
          }
        });
    });
  };

  // 移除图片
  const removeImage = (index: number) => {
    const image = images.value[index];
    images.value.splice(index, 1);
    saveImagesToStorage();
    updateTotalSlides();
    message.success(`图片 ${image.name} 已删除`);
  };

  // 清空所有图片
  const clearAllImages = () => {
    // 清空内存中的图片数组
    images.value = [];

    // 清空fileList
    fileList.value = [];

    // 清空localStorage
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('清空localStorage失败:', error);
    }

    // 更新状态
    updateTotalSlides();
    currentSlide.value = 0;
    jumpToSlide.value = 1;

    // 清空视角绑定
    globalThreeStore.clearPPTViewBindings();

    message.success('已清空所有图片和相关数据');
  };

  // 拖拽排序相关方法
  const handleDragStart = (index: number) => {
    draggedIndex.value = index;
  };

  const handleDrop = (targetIndex: number) => {
    if (draggedIndex.value !== null && draggedIndex.value !== targetIndex) {
      const draggedItem = images.value[draggedIndex.value];
      images.value.splice(draggedIndex.value, 1);
      images.value.splice(targetIndex, 0, draggedItem);

      // 更新当前页面索引
      if (currentSlide.value === draggedIndex.value) {
        currentSlide.value = targetIndex;
        jumpToSlide.value = targetIndex + 1;
      } else if (currentSlide.value === targetIndex) {
        currentSlide.value = draggedIndex.value;
        jumpToSlide.value = draggedIndex.value + 1;
      }

      saveImagesToStorage();
      message.success('图片顺序已调整');
    }
    draggedIndex.value = null;
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 处理图片加载错误
  const handleImageError = () => {
    message.error('图片加载失败');
  };

  // 获取localStorage中的图片数量
  const getStorageImageCount = (): number => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedData = JSON.parse(stored);
        return Array.isArray(parsedData) ? parsedData.length : 0;
      }
      return 0;
    } catch (error) {
      console.error('读取localStorage失败:', error);
      return 0;
    }
  };

  // 强制从存储重新加载
  const forceReloadFromStorage = () => {
    console.log('强制从localStorage重新加载图片数据');
    loadImagesFromStorage();
    updateTotalSlides();
    message.success('已从存储重新加载图片数据');
  };

  // 获取当前localStorage使用的存储空间大小
  const getStorageSize = (): number => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        return new Blob([stored]).size;
      }
      return 0;
    } catch (error) {
      console.error('获取存储大小失败:', error);
      return 0;
    }
  };

  // 获取localStorage总使用量
  const getTotalStorageSize = (): number => {
    try {
      let total = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          total += localStorage[key].length + key.length;
        }
      }
      return total * 2; // 每个字符在UTF-16中占用2字节
    } catch (error) {
      console.error('获取总存储大小失败:', error);
      return 0;
    }
  };

  // 显示存储详情
  const showStorageInfo = () => {
    const currentSize = getStorageSize();
    const totalSize = getTotalStorageSize();
    const maxSize = 5 * 1024 * 1024; // 假设5MB限制
    const usagePercent = ((totalSize / maxSize) * 100).toFixed(1);

    const info = [
      `图片数据大小: ${formatFileSize(currentSize)}`,
      `localStorage总使用量: ${formatFileSize(totalSize)}`,
      `预估最大容量: ${formatFileSize(maxSize)}`,
      `使用率: ${usagePercent}%`,
      `剩余空间: ${formatFileSize(maxSize - totalSize)}`,
      '',
      '压缩设置:',
      '• 自动压缩大图片',
      '• 目标大小: 200KB以内',
      '• 保持图片质量: 80%',
      '• 最大尺寸: 1920x1080',
    ].join('\n');

    message.info({
      content: info,
      duration: 8,
    });
  };

  // 图片压缩函数
  const compressImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // 计算压缩后的尺寸
        const maxWidth = 1920;
        const maxHeight = 1080;
        let { width, height } = img;

        // 按比例缩放
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width = Math.round(width * ratio);
          height = Math.round(height * ratio);
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制图片
        ctx?.drawImage(img, 0, 0, width, height);

        // 尝试不同的压缩质量，直到文件大小合适
        const tryCompress = (quality: number): string => {
          const dataUrl = canvas.toDataURL('image/jpeg', quality);
          const size = Math.round((dataUrl.length * 3) / 4); // base64大小估算

          // 如果大小合适或质量已经很低，返回结果
          if (size <= 200 * 1024 || quality <= 0.3) {
            return dataUrl;
          }

          // 否则降低质量继续压缩
          return tryCompress(quality - 0.1);
        };

        try {
          const result = tryCompress(0.8); // 从80%质量开始
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('图片加载失败'));
      };

      // 读取文件
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target?.result as string;
      };
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      reader.readAsDataURL(file);
    });
  };

  const goToSlideBySlider = (value: number) => {
    goToSlide(value);
  };

  const toggleAutoPlay = () => {
    isAutoPlaying.value = !isAutoPlaying.value;
    globalThreeStore.setPPTPlaying(isAutoPlaying.value);

    if (isAutoPlaying.value) {
      startAutoPlay();
    } else {
      stopAutoPlay();
    }
  };

  const startAutoPlay = () => {
    stopAutoPlay(); // 清除现有定时器
    autoPlayTimer = window.setInterval(() => {
      if (currentSlide.value < totalSlides.value - 1) {
        nextSlide();
      } else {
        // 到达最后一页，停止自动播放
        toggleAutoPlay();
      }
    }, autoPlayInterval.value);
  };

  const stopAutoPlay = () => {
    if (autoPlayTimer) {
      clearInterval(autoPlayTimer);
      autoPlayTimer = null;
    }
  };

  const triggerViewChange = (slideIndex: number) => {
    const binding = globalThreeStore.getPPTViewBinding(slideIndex);
    if (binding) {
      const cameraController = CameraController.getInstance();
      if (cameraController) {
        cameraController.moveToPosition(
          binding.cameraPosition,
          binding.cameraTarget,
          1000 // 1秒过渡时间
        );
      }
    }
  };

  const bindCurrentView = () => {
    const cameraController = CameraController.getInstance();
    if (cameraController) {
      const position = cameraController.getCameraPosition();
      const target = cameraController.currentTarget || { x: 0, y: 0, z: 0 };

      const imageName = currentImage.value ? currentImage.value.name : `图片${currentSlide.value + 1}`;

      const binding: PPTViewBinding = {
        slideIndex: currentSlide.value,
        cameraPosition: { x: position.x, y: position.y, z: position.z },
        cameraTarget: { x: target.x, y: target.y, z: target.z },
        name: `${imageName}视角`,
      };

      globalThreeStore.addPPTViewBinding(binding);
      message.success('视角绑定成功');
    }
  };

  const handleViewBinding = (binding: PPTViewBinding) => {
    globalThreeStore.addPPTViewBinding(binding);
  };

  const handleRemoveBinding = (slideIndex: number) => {
    globalThreeStore.removePPTViewBinding(slideIndex);
  };

  // 监听自动播放间隔变化
  watch(autoPlayInterval, () => {
    if (isAutoPlaying.value) {
      startAutoPlay(); // 重新启动定时器
    }
  });

  // 监听图片数组变化
  watch(
    images,
    () => {
      updateTotalSlides();
    },
    { deep: true }
  );

  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowLeft':
      case 'PageUp':
        event.preventDefault();
        previousSlide();
        break;
      case 'ArrowRight':
      case 'PageDown':
        event.preventDefault();
        nextSlide();
        break;
      case ' ':
        event.preventDefault();
        toggleAutoPlay();
        break;
    }
  };

  // localStorage变化监听
  const handleStorageChange = (e: StorageEvent) => {
    if (e.key === STORAGE_KEY) {
      console.log('检测到localStorage变化，重新加载图片数据');
      loadImagesFromStorage();
      updateTotalSlides();
    }
  };

  // 生命周期
  onMounted(() => {
    initializePPT();
    jumpToSlide.value = currentSlide.value + 1;

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyDown);

    // 添加localStorage变化监听
    window.addEventListener('storage', handleStorageChange);
  });

  onUnmounted(() => {
    stopAutoPlay();

    // 移除键盘事件监听
    document.removeEventListener('keydown', handleKeyDown);

    // 移除localStorage变化监听
    window.removeEventListener('storage', handleStorageChange);
  });
</script>

<style scoped>
  .simple-ppt-player {
    background: linear-gradient(135deg, #0a0f1c 0%, #1a2332 100%);
  }
</style>
