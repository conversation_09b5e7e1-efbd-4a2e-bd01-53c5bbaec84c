import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { SceneManager } from '../SceneManager';

/**
 * 物理对象类型
 */
export enum PhysicsObjectType {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  KINEMATIC = 'kinematic',
}

/**
 * 物理对象形状类型
 */
export enum PhysicsShapeType {
  BOX = 'box',
  SPHERE = 'sphere',
  CYLINDER = 'cylinder',
  CAPSULE = 'capsule',
  PLANE = 'plane',
  TRIMESH = 'trimesh',
}

/**
 * 物理对象接口
 */
export interface PhysicsObject {
  body: CANNON.Body;
  mesh?: THREE.Object3D;
  type: PhysicsObjectType;
  shape: PhysicsShapeType;
  uuid: string;
}

/**
 * 物理引擎管理器
 * 负责管理物理世界和物理对象
 */
export class PhysicsManager {
  private static instance: PhysicsManager;
  private world: CANNON.World;
  private physicsObjects: Map<string, PhysicsObject> = new Map();
  private scene: THREE.Scene;
  private isRunning: boolean = false;
  private fixedTimeStep: number = 1 / 60; // 物理更新频率
  private maxSubSteps: number = 3; // 最大子步数
  private lastCallTime: number = 0;
  private debugMeshes: Map<string, THREE.Mesh> = new Map();
  private showDebug: boolean = false;
  private updateCallback: ((deltaTime: number) => void) | null = null;

  /**
   * 获取单例实例
   */
  public static getInstance(): PhysicsManager {
    if (!PhysicsManager.instance) {
      PhysicsManager.instance = new PhysicsManager();
    }
    return PhysicsManager.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    try {
      const sceneManager = SceneManager.getInstance();
      this.scene = sceneManager.scene;

      // 创建物理世界
      this.world = new CANNON.World({
        gravity: new CANNON.Vec3(0, -9.82, 0), // 重力加速度
      });

      // 设置物理世界参数 - 极度性能优化
      this.world.allowSleep = true; // 允许物体休眠以提高性能
      this.world.sleepSpeedLimit = 0.8; // 进一步提高休眠速度阈值，让物体更快进入休眠状态
      this.world.sleepTimeLimit = 0.3; // 进一步降低休眠时间阈值，让物体更快进入休眠状态
      this.world.defaultContactMaterial.friction = 0.1; // 默认摩擦系数
      this.world.defaultContactMaterial.restitution = 0.2; // 降低恢复系数，减少弹跳计算

      // 使用最高效的碰撞检测算法 - NaiveBroadphase在物体数量少时性能更好
      this.world.broadphase = new CANNON.NaiveBroadphase();

      // 极度降低求解器迭代次数，大幅提高性能
      this.world.solver.iterations = 4; // 从默认10降低到4，大幅提高性能
      this.world.solver.tolerance = 0.1; // 从默认0.01增加到0.1，大幅提高性能

      // 创建地面碰撞材质
      const groundMaterial = new CANNON.Material('ground');
      const playerMaterial = new CANNON.Material('player');

      // 创建地面和玩家之间的接触材质
      const groundPlayerContactMaterial = new CANNON.ContactMaterial(groundMaterial, playerMaterial, {
        friction: 0.1, // 摩擦系数
        restitution: 0.3, // 恢复系数
        contactEquationStiffness: 1e6, // 接触方程刚度
        contactEquationRelaxation: 3, // 接触方程松弛
      });

      // 添加接触材质到世界
      this.world.addContactMaterial(groundPlayerContactMaterial);

      console.log('[PhysicsManager] 物理引擎初始化完成');
    } catch (error) {
      console.error('[PhysicsManager] 初始化失败:', error);
    }
  }

  /**
   * 启动物理引擎
   */
  public start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    this.lastCallTime = performance.now() / 1000;

    // 注册更新回调
    this.updateCallback = this._update.bind(this);
    SceneManager.getInstance().addUpdateCallback(this.updateCallback);

    console.log('[PhysicsManager] 物理引擎已启动');
  }

  /**
   * 停止物理引擎
   */
  public stop(): void {
    if (!this.isRunning) return;

    this.isRunning = false;

    // 移除更新回调
    if (this.updateCallback) {
      SceneManager.getInstance().removeUpdateCallback(this.updateCallback);
      this.updateCallback = null;
    }

    console.log('[PhysicsManager] 物理引擎已停止');
  }

  /**
   * 更新物理世界
   * 性能优化：极度优化，减少所有不必要的计算
   * @param deltaTime 时间增量（秒）
   */
  private _update(deltaTime: number): void {
    if (!this.isRunning) return;

    // 性能优化：使用静态计数器，只在每N帧更新一次物理世界
    if (!this._updateCounter) {
      this._updateCounter = 0;
    }

    // 极度性能优化：每4帧更新一次物理世界，大幅提高性能
    this._updateCounter++;
    if (this._updateCounter % 4 !== 0) {
      return;
    }

    // 安全检查：确保deltaTime是有效值，但避免复杂计算
    if (deltaTime <= 0) {
      deltaTime = 0.016; // 使用默认值（约60fps）
    } else if (deltaTime > 0.1) {
      deltaTime = 0.1; // 限制最大时间步长
    }

    // 由于我们每4帧更新一次，需要调整deltaTime
    // 但不直接乘以4，而是乘以2.5，以平衡物理精度和性能
    const adjustedDeltaTime = deltaTime * 2.5;

    // 使用固定时间步长更新物理世界
    // 极度减少物理迭代次数，提高性能
    this.world.step(this.fixedTimeStep, adjustedDeltaTime, 1); // 将maxSubSteps降低到1

    // 只更新活动的物理对象，跳过休眠的对象
    // 性能优化：使用更高效的更新方法
    this._fastUpdateActivePhysicsObjects();

    // 只在调试模式下更新调试网格，且极度降低更新频率
    if (this.showDebug && this._updateCounter % 8 === 0) {
      this._updateDebugMeshes();
    }
  }

  // 性能优化：静态计数器
  private _updateCounter: number = 0;

  /**
   * 快速更新活动的物理对象
   * 性能优化：使用更高效的更新方法，避免创建新对象
   */
  private _fastUpdateActivePhysicsObjects(): void {
    // 使用for...of循环代替forEach，避免回调函数开销
    for (const [_, physicsObject] of this.physicsObjects) {
      const { body, mesh, type } = physicsObject;

      // 只更新动态对象且非休眠的对象
      if (mesh && type !== PhysicsObjectType.STATIC && !body.sleepState) {
        // 直接设置位置和旋转，避免创建新对象
        mesh.position.set(body.position.x, body.position.y, body.position.z);
        mesh.quaternion.set(body.quaternion.x, body.quaternion.y, body.quaternion.z, body.quaternion.w);
      }
    }
  }

  /**
   * 只更新活动的物理对象
   * 性能优化：跳过休眠的对象
   */
  private _updateActivePhysicsObjects(): void {
    this.physicsObjects.forEach((physicsObject) => {
      const { body, mesh, type } = physicsObject;

      // 只更新动态对象且非休眠的对象
      if (mesh && type !== PhysicsObjectType.STATIC && !body.sleepState) {
        // 更新网格位置和旋转
        mesh.position.copy(new THREE.Vector3(body.position.x, body.position.y, body.position.z));

        mesh.quaternion.copy(new THREE.Quaternion(body.quaternion.x, body.quaternion.y, body.quaternion.z, body.quaternion.w));
      }
    });
  }

  /**
   * 更新物理对象的位置和旋转
   */
  private _updatePhysicsObjects(): void {
    this.physicsObjects.forEach((physicsObject) => {
      const { body, mesh, type } = physicsObject;

      if (mesh && type !== PhysicsObjectType.STATIC) {
        // 更新网格位置和旋转
        mesh.position.copy(new THREE.Vector3(body.position.x, body.position.y, body.position.z));

        mesh.quaternion.copy(new THREE.Quaternion(body.quaternion.x, body.quaternion.y, body.quaternion.z, body.quaternion.w));
      }
    });
  }

  /**
   * 更新调试网格
   */
  private _updateDebugMeshes(): void {
    this.physicsObjects.forEach((physicsObject, uuid) => {
      const { body, shape } = physicsObject;

      // 获取或创建调试网格
      let debugMesh = this.debugMeshes.get(uuid);
      if (!debugMesh) {
        debugMesh = this._createDebugMesh(shape, body);
        if (debugMesh) {
          this.scene.add(debugMesh);
          this.debugMeshes.set(uuid, debugMesh);
        }
      }

      // 更新调试网格位置和旋转
      if (debugMesh) {
        debugMesh.position.copy(new THREE.Vector3(body.position.x, body.position.y, body.position.z));

        debugMesh.quaternion.copy(new THREE.Quaternion(body.quaternion.x, body.quaternion.y, body.quaternion.z, body.quaternion.w));
      }
    });
  }

  /**
   * 创建调试网格
   */
  private _createDebugMesh(shape: PhysicsShapeType, body: CANNON.Body): THREE.Mesh | null {
    const material = new THREE.MeshBasicMaterial({
      color: 0x00ff00,
      wireframe: true,
      transparent: true,
      opacity: 0.5,
    });

    let geometry: THREE.BufferGeometry | null = null;

    // 根据形状类型创建几何体
    switch (shape) {
      case PhysicsShapeType.BOX:
        if (body.shapes[0] instanceof CANNON.Box) {
          const cannonShape = body.shapes[0] as CANNON.Box;
          geometry = new THREE.BoxGeometry(cannonShape.halfExtents.x * 2, cannonShape.halfExtents.y * 2, cannonShape.halfExtents.z * 2);
        }
        break;
      case PhysicsShapeType.SPHERE:
        if (body.shapes[0] instanceof CANNON.Sphere) {
          const cannonShape = body.shapes[0] as CANNON.Sphere;
          geometry = new THREE.SphereGeometry(cannonShape.radius, 16, 16);
        }
        break;
      case PhysicsShapeType.CYLINDER:
        if (body.shapes[0] instanceof CANNON.Cylinder) {
          const cannonShape = body.shapes[0] as CANNON.Cylinder;
          geometry = new THREE.CylinderGeometry(cannonShape.radiusTop, cannonShape.radiusBottom, cannonShape.height, 16);
        }
        break;
      case PhysicsShapeType.PLANE:
        if (body.shapes[0] instanceof CANNON.Plane) {
          geometry = new THREE.PlaneGeometry(10, 10);
        }
        break;
      default:
        return null;
    }

    if (geometry) {
      return new THREE.Mesh(geometry, material);
    }

    return null;
  }

  /**
   * 添加物理对象
   */
  public addPhysicsObject(mesh: THREE.Object3D, type: PhysicsObjectType, shape: PhysicsShapeType, options: any = {}): PhysicsObject | null {
    try {
      // 创建物理形状
      const cannonShape = this._createShape(mesh, shape, options);
      if (!cannonShape) {
        console.error('[PhysicsManager] 创建物理形状失败:', mesh.name);
        return null;
      }

      // 创建物理刚体
      const body = new CANNON.Body({
        mass: type === PhysicsObjectType.STATIC ? 0 : options.mass || 1,
        shape: cannonShape,
        position: new CANNON.Vec3(mesh.position.x, mesh.position.y, mesh.position.z),
        quaternion: new CANNON.Quaternion(mesh.quaternion.x, mesh.quaternion.y, mesh.quaternion.z, mesh.quaternion.w),
        material: options.material || undefined,
        type: this._getBodyType(type),
      });

      // 添加到物理世界
      this.world.addBody(body);

      // 创建物理对象
      const physicsObject: PhysicsObject = {
        body,
        mesh,
        type,
        shape,
        uuid: mesh.uuid,
      };

      // 添加到物理对象集合
      this.physicsObjects.set(mesh.uuid, physicsObject);

      return physicsObject;
    } catch (error) {
      console.error('[PhysicsManager] 添加物理对象失败:', error);
      return null;
    }
  }

  /**
   * 创建物理形状
   * 性能优化：支持简化的碰撞形状
   */
  private _createShape(mesh: THREE.Object3D, shapeType: PhysicsShapeType, options: any = {}): CANNON.Shape | null {
    try {
      // 是否使用简化的碰撞形状
      const simplify = options.simplify === true;

      switch (shapeType) {
        case PhysicsShapeType.BOX: {
          // 计算包围盒
          const bbox = new THREE.Box3().setFromObject(mesh);
          const size = new THREE.Vector3();
          bbox.getSize(size);

          // 如果启用简化，则减少盒体的精度
          if (simplify) {
            // 稍微缩小碰撞盒，避免边缘碰撞问题
            size.x *= 0.95;
            size.z *= 0.95;
          }

          // 创建盒体形状
          return new CANNON.Box(new CANNON.Vec3(size.x / 2, size.y / 2, size.z / 2));
        }
        case PhysicsShapeType.SPHERE: {
          // 计算包围球
          const bbox = new THREE.Box3().setFromObject(mesh);
          const size = new THREE.Vector3();
          bbox.getSize(size);

          // 使用最大尺寸的一半作为半径
          let radius = Math.max(size.x, size.y, size.z) / 2;

          // 如果启用简化，则减少球体的精度
          if (simplify) {
            // 稍微缩小半径，避免边缘碰撞问题
            radius *= 0.95;
          }

          // 创建球体形状
          return new CANNON.Sphere(radius);
        }
        case PhysicsShapeType.CYLINDER: {
          // 计算包围盒
          const bbox = new THREE.Box3().setFromObject(mesh);
          const size = new THREE.Vector3();
          bbox.getSize(size);

          // 如果启用简化，则减少圆柱体的精度
          let segments = simplify ? 8 : 16; // 减少分段数

          // 创建圆柱体形状
          return new CANNON.Cylinder(size.x / 2, size.x / 2, size.y, segments);
        }
        case PhysicsShapeType.CAPSULE: {
          // 计算包围盒
          const bbox = new THREE.Box3().setFromObject(mesh);
          const size = new THREE.Vector3();
          bbox.getSize(size);

          // 如果启用简化，则减少胶囊体的精度
          let segments = simplify ? 8 : 16; // 减少分段数

          // 创建胶囊体形状（使用圆柱体和两个半球）
          const radius = Math.max(size.x, size.z) / 2;
          const height = size.y - radius * 2;

          // 创建复合形状
          const shape = new CANNON.Cylinder(radius, radius, height, segments);
          return shape;
        }
        case PhysicsShapeType.PLANE: {
          // 创建平面形状
          return new CANNON.Plane();
        }
        case PhysicsShapeType.TRIMESH: {
          // 只支持THREE.Mesh
          if (!(mesh instanceof THREE.Mesh)) {
            console.error('[PhysicsManager] TRIMESH形状只支持THREE.Mesh');
            return null;
          }

          // 如果启用简化，则使用盒体代替三角网格
          if (simplify) {
            const bbox = new THREE.Box3().setFromObject(mesh);
            const size = new THREE.Vector3();
            bbox.getSize(size);

            // 创建盒体形状
            return new CANNON.Box(new CANNON.Vec3(size.x / 2, size.y / 2, size.z / 2));
          }

          // 获取几何体
          const geometry = (mesh as THREE.Mesh).geometry;

          // 创建三角网格形状
          if (geometry instanceof THREE.BufferGeometry) {
            // 性能优化：如果顶点数量过多，进行简化
            const vertices = geometry.attributes.position.array;
            const vertexCount = vertices.length / 3;

            // 如果顶点数量过多，使用简化的盒体碰撞器
            if (vertexCount > 1000) {
              console.log(`[PhysicsManager] 顶点数量过多(${vertexCount})，使用盒体碰撞器代替三角网格`);

              const bbox = new THREE.Box3().setFromObject(mesh);
              const size = new THREE.Vector3();
              bbox.getSize(size);

              // 创建盒体形状
              return new CANNON.Box(new CANNON.Vec3(size.x / 2, size.y / 2, size.z / 2));
            }

            const indices = geometry.index ? geometry.index.array : null;

            if (indices) {
              // 使用索引创建三角网格
              return new CANNON.Trimesh(vertices as unknown as number[], indices as unknown as number[]);
            } else {
              // 没有索引，直接使用顶点创建三角网格
              return new CANNON.Trimesh(vertices as unknown as number[], []);
            }
          }

          console.error('[PhysicsManager] 不支持的几何体类型');
          return null;
        }
        default:
          console.error('[PhysicsManager] 不支持的形状类型:', shapeType);
          return null;
      }
    } catch (error) {
      console.error('[PhysicsManager] 创建物理形状失败:', error);

      // 出错时尝试创建一个简单的盒体形状
      try {
        const bbox = new THREE.Box3().setFromObject(mesh);
        const size = new THREE.Vector3();
        bbox.getSize(size);

        // 创建盒体形状
        return new CANNON.Box(new CANNON.Vec3(size.x / 2, size.y / 2, size.z / 2));
      } catch (fallbackError) {
        console.error('[PhysicsManager] 创建备用物理形状也失败:', fallbackError);
        return null;
      }
    }
  }

  /**
   * 获取物理刚体类型
   */
  private _getBodyType(type: PhysicsObjectType): CANNON.BodyType {
    switch (type) {
      case PhysicsObjectType.STATIC:
        return CANNON.BODY_TYPES.STATIC;
      case PhysicsObjectType.DYNAMIC:
        return CANNON.BODY_TYPES.DYNAMIC;
      case PhysicsObjectType.KINEMATIC:
        return CANNON.BODY_TYPES.KINEMATIC;
      default:
        return CANNON.BODY_TYPES.STATIC;
    }
  }

  /**
   * 移除物理对象
   */
  public removePhysicsObject(uuid: string): void {
    const physicsObject = this.physicsObjects.get(uuid);
    if (physicsObject) {
      // 从物理世界中移除刚体
      this.world.removeBody(physicsObject.body);

      // 从物理对象集合中移除
      this.physicsObjects.delete(uuid);

      // 移除调试网格
      const debugMesh = this.debugMeshes.get(uuid);
      if (debugMesh) {
        this.scene.remove(debugMesh);
        this.debugMeshes.delete(uuid);
      }
    }
  }

  /**
   * 清空所有物理对象
   */
  public clear(): void {
    // 从物理世界中移除所有刚体
    this.physicsObjects.forEach((physicsObject) => {
      this.world.removeBody(physicsObject.body);
    });

    // 清空物理对象集合
    this.physicsObjects.clear();

    // 移除所有调试网格
    this.debugMeshes.forEach((debugMesh) => {
      this.scene.remove(debugMesh);
    });

    // 清空调试网格集合
    this.debugMeshes.clear();
  }

  /**
   * 获取物理对象
   */
  public getPhysicsObject(uuid: string): PhysicsObject | undefined {
    return this.physicsObjects.get(uuid);
  }

  /**
   * 设置调试模式
   */
  public setDebugMode(enabled: boolean): void {
    this.showDebug = enabled;

    if (!enabled) {
      // 移除所有调试网格
      this.debugMeshes.forEach((debugMesh) => {
        this.scene.remove(debugMesh);
      });

      // 清空调试网格集合
      this.debugMeshes.clear();
    }
  }
}
