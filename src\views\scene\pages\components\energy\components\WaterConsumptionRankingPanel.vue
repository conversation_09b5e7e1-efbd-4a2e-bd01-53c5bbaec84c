<template>
  <div class="w-full h-full" ref="chartRef"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import * as echarts from 'echarts/core';

  const chartRef = ref(null);
  let chart = null;

  // 生成设备耗水量排行数据 - Top 10
  const generateDeviceConsumptionData = () => {
    // 设备名称和基础耗水量 - 使用合理的数值范围
    const devices = [
      { name: '冷却塔1#', baseValue: 850 },
      { name: '冷却塔2#', baseValue: 780 },
      { name: '中央空调冷凝系统', baseValue: 720 },
      { name: '消防水系统', baseValue: 650 },
      { name: '卫生间1#', baseValue: 580 },
      { name: '卫生间2#', baseValue: 520 },
      { name: '绿化灌溉系统', baseValue: 480 },
      { name: '水处理设备', baseValue: 420 },
      { name: '冷却塔3#', baseValue: 380 },
      { name: '实验室用水', baseValue: 320 },
    ];

    // 添加随机波动并排序
    return devices
      .map((device) => ({
        name: device.name,
        // 使用更小的随机波动范围，保持数值在合理区间
        value: Math.round(device.baseValue + (Math.random() * 40 - 20)), // 添加±20的随机波动
      }))
      .sort((a, b) => b.value - a.value); // 按耗水量从高到低排序
  };

  onMounted(() => {
    const deviceData = generateDeviceConsumptionData();

    // 提取设备名称和耗水量数据
    const deviceNames = deviceData.map((item) => item.name);
    const consumptionValues = deviceData.map((item) => item.value);

    chart = echarts.init(chartRef.value);
    const option = {
      tooltip: {
        trigger: 'axis',
        confine: true,
        appendToBody: true,
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params) {
          const data = params[0];
          return `${data.name}<br/>${data.value} t`;
        },
        extraCssText:
          'background: rgba(0,0,0,0.7); border-radius: 4px; border: none; box-shadow: 0 0 3px rgba(0, 0, 0, 0.2); color: #fff; font-size: 12px;',
        textStyle: {
          color: '#fff',
          fontSize: 10,
        },
      },
      grid: {
        top: '20%',
        left: '3%',
        right: '15%', // 增加右侧边距，为标签提供更多空间
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        name: 't',
        nameTextStyle: { color: '#fff', fontSize: 10 },
        axisLabel: { color: '#fff', fontSize: 10 },
        splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
      },
      yAxis: {
        type: 'category',
        data: deviceNames,
        axisLabel: {
          color: '#fff',
          fontSize: 10,
          width: 80,
          overflow: 'truncate',
        },
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
      },
      series: [
        {
          name: '耗水量',
          type: 'bar',
          data: consumptionValues,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#06B6D4' },
              { offset: 1, color: '#0EA5E9' },
            ]),
          },
          barWidth: '60%',
          label: {
            show: true,
            position: 'right',
            color: '#fff',
            fontSize: 9, // 减小字体大小
            distance: 5, // 减少标签与柱状图的距离
            formatter: function (params) {
              // 使用更紧凑的格式，移除小数点
              return Math.round(params.value) + 't';
            },
            overflow: 'truncate', // 防止文本溢出
          },
        },
      ],
    };

    chart.setOption(option);
    window.addEventListener('resize', handleResize);
  });

  const handleResize = () => {
    chart?.resize();
  };

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    chart?.dispose();
    chart = null;
  });
</script>
