<template>
  <div class="h-full flex flex-col">
    <!-- 操作工具栏 -->
    <div class="flex justify-between items-center mb-[1vw]">
      <div class="flex items-center space-x-[0.8vw]">
        <button
          class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
          @click="showMaintenanceDialog = true"
        >
          <ToolOutlined class="mr-[0.2vw]" />
          新建维护
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
          @click="showPlanDialog = true"
        >
          <ScheduleOutlined class="mr-[0.2vw]" />
          维护计划
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors"
          @click="showContractDialog = true"
        >
          <FileTextOutlined class="mr-[0.2vw]" />
          维保合同
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-purple-500 text-white text-[0.6vw] rounded hover:bg-purple-600 transition-colors"
          @click="exportMaintenance"
        >
          <DownloadOutlined class="mr-[0.2vw]" />
          导出记录
        </button>
      </div>

      <div class="flex items-center space-x-[0.6vw]">
        <select v-model="statusFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部状态</option>
          <option value="planned">计划中</option>
          <option value="in_progress">进行中</option>
          <option value="completed">已完成</option>
          <option value="cancelled">已取消</option>
        </select>
        <select v-model="typeFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部类型</option>
          <option value="preventive">预防性维护</option>
          <option value="corrective">纠正性维护</option>
          <option value="emergency">紧急维护</option>
          <option value="upgrade">升级维护</option>
        </select>
        <input
          v-model="searchQuery"
          placeholder="搜索维护单号、资产名称..."
          class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none w-[15vw]"
        />
        <button
          class="px-[0.6vw] py-[0.3vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors"
          @click="searchMaintenance"
        >
          <SearchOutlined />
        </button>
      </div>
    </div>

    <!-- 维护统计卡片 -->
    <div class="grid grid-cols-5 gap-[0.8vw] mb-[1vw]">
      <div v-for="stat in maintenanceStats" :key="stat.label" class="bg-black/20 rounded border border-white/10 p-[0.6vw]">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-[0.5vw] text-gray-400">{{ stat.label }}</div>
            <div :class="['text-[0.8vw] font-semibold', stat.valueClass]">{{ stat.value }}</div>
          </div>
          <div :class="['text-[1vw]', stat.iconClass]">{{ stat.icon }}</div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-black/20 rounded border border-white/10 overflow-hidden">
      <div class="overflow-auto h-full">
        <table class="w-full text-[0.6vw]">
          <thead class="bg-blue-500/20 sticky top-0">
            <tr>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">维护单号</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">资产名称</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">维护类型</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">负责人</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">计划时间</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">实际时间</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">费用</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">状态</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="maintenance in paginatedMaintenance" :key="maintenance.id" class="hover:bg-white/5 transition-colors">
              <td class="p-[0.6vw] text-white border-b border-white/5">{{ maintenance.maintenanceCode }}</td>
              <td class="p-[0.6vw] text-white border-b border-white/5">{{ maintenance.assetName }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ getTypeText(maintenance.type) }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ maintenance.assignee }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ maintenance.plannedDate }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ maintenance.actualDate || '-' }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">¥{{ maintenance.cost }}</td>
              <td class="p-[0.6vw] border-b border-white/5">
                <span
                  :class="[
                    'px-[0.4vw] py-[0.1vw] rounded text-[0.5vw]',
                    maintenance.status === 'planned'
                      ? 'bg-blue-500/20 text-blue-400'
                      : maintenance.status === 'in_progress'
                        ? 'bg-yellow-500/20 text-yellow-400'
                        : maintenance.status === 'completed'
                          ? 'bg-green-500/20 text-green-400'
                          : 'bg-gray-500/20 text-gray-400',
                  ]"
                >
                  {{ getStatusText(maintenance.status) }}
                </span>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex space-x-[0.4vw]">
                  <button
                    v-if="maintenance.status === 'planned'"
                    class="text-green-400 hover:text-green-300 text-[0.5vw] bg-transparent"
                    @click="startMaintenance(maintenance)"
                  >
                    开始
                  </button>
                  <button
                    v-if="maintenance.status === 'in_progress'"
                    class="text-blue-400 hover:text-blue-300 text-[0.5vw] bg-transparent"
                    @click="completeMaintenance(maintenance)"
                  >
                    完成
                  </button>
                  <button class="text-orange-400 hover:text-orange-300 text-[0.5vw] bg-transparent" @click="viewMaintenanceDetail(maintenance)">
                    详情
                  </button>
                  <button class="text-purple-400 hover:text-purple-300 text-[0.5vw] bg-transparent" @click="editMaintenance(maintenance)">
                    编辑
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-between items-center mt-[0.8vw] text-[0.6vw] text-gray-400">
      <div>共 {{ filteredMaintenance.length }} 条记录</div>
      <div class="flex items-center space-x-[0.4vw]">
        <button
          :disabled="currentPage === 1"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="text-white">{{ currentPage }} / {{ totalPages }}</span>
        <button
          :disabled="currentPage === totalPages"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage++"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 新建维护弹窗 -->
    <ModalDialog
      v-model:visible="showMaintenanceDialog"
      title="新建维护任务"
      width="60vw"
      :show-footer="true"
      @confirm="confirmMaintenance"
      @cancel="showMaintenanceDialog = false"
    >
      <AssetMaintenanceForm v-model:form-data="maintenanceForm" />
    </ModalDialog>

    <!-- 维护计划弹窗 -->
    <ModalDialog
      v-model:visible="showPlanDialog"
      title="维护计划管理"
      width="80vw"
      height="70vh"
      :show-footer="false"
      @cancel="showPlanDialog = false"
    >
      <MaintenancePlanView :maintenance-data="maintenanceRecords" />
    </ModalDialog>

    <!-- 维保合同弹窗 -->
    <ModalDialog
      v-model:visible="showContractDialog"
      title="维保合同管理"
      width="70vw"
      :show-footer="true"
      @confirm="saveContracts"
      @cancel="showContractDialog = false"
    >
      <MaintenanceContractManager v-model:contracts="contracts" />
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { ToolOutlined, ScheduleOutlined, FileTextOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import AssetMaintenanceForm from './forms/AssetMaintenanceForm.vue';
  import MaintenancePlanView from './forms/MaintenancePlanView.vue';
  import MaintenanceContractManager from './forms/MaintenanceContractManager.vue';

  // 响应式数据
  const maintenanceRecords = ref([]);
  const contracts = ref([]);
  const searchQuery = ref('');
  const statusFilter = ref('');
  const typeFilter = ref('');
  const currentPage = ref(1);
  const pageSize = ref(20);
  const showMaintenanceDialog = ref(false);
  const showPlanDialog = ref(false);
  const showContractDialog = ref(false);
  const maintenanceForm = ref({});

  // 维护统计
  const maintenanceStats = ref([
    { label: '本月维护', value: '18', valueClass: 'text-white', icon: '🔧', iconClass: 'text-blue-400' },
    { label: '计划中', value: '6', valueClass: 'text-blue-400', icon: '📅', iconClass: 'text-blue-400' },
    { label: '进行中', value: '3', valueClass: 'text-yellow-400', icon: '⚙️', iconClass: 'text-yellow-400' },
    { label: '已完成', value: '9', valueClass: 'text-green-400', icon: '✅', iconClass: 'text-green-400' },
    { label: '平均费用', value: '¥2.5K', valueClass: 'text-purple-400', icon: '💰', iconClass: 'text-purple-400' },
  ]);

  // 计算属性
  const filteredMaintenance = computed(() => {
    let result = maintenanceRecords.value;

    if (statusFilter.value) {
      result = result.filter((maintenance) => maintenance.status === statusFilter.value);
    }

    if (typeFilter.value) {
      result = result.filter((maintenance) => maintenance.type === typeFilter.value);
    }

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(
        (maintenance) => maintenance.maintenanceCode.toLowerCase().includes(query) || maintenance.assetName.toLowerCase().includes(query)
      );
    }

    return result;
  });

  const totalPages = computed(() => Math.ceil(filteredMaintenance.value.length / pageSize.value));

  const paginatedMaintenance = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return filteredMaintenance.value.slice(start, end);
  });

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      planned: '计划中',
      in_progress: '进行中',
      completed: '已完成',
      cancelled: '已取消',
    };
    return statusMap[status] || status;
  };

  const getTypeText = (type) => {
    const typeMap = {
      preventive: '预防性维护',
      corrective: '纠正性维护',
      emergency: '紧急维护',
      upgrade: '升级维护',
    };
    return typeMap[type] || type;
  };

  const searchMaintenance = () => {
    currentPage.value = 1;
  };

  const startMaintenance = (maintenance) => {
    maintenance.status = 'in_progress';
    maintenance.actualStartDate = new Date().toISOString().split('T')[0];
  };

  const completeMaintenance = (maintenance) => {
    maintenance.status = 'completed';
    maintenance.actualDate = new Date().toISOString().split('T')[0];
  };

  const viewMaintenanceDetail = (maintenance) => {
    alert(
      `维护详情：\n\n维护单号：${maintenance.maintenanceCode}\n资产名称：${maintenance.assetName}\n维护类型：${getTypeText(maintenance.type)}\n负责人：${maintenance.assignee}\n计划时间：${maintenance.plannedDate}\n实际时间：${maintenance.actualDate || '未完成'}\n费用：¥${maintenance.cost}\n状态：${getStatusText(maintenance.status)}`
    );
  };

  const editMaintenance = (maintenance) => {
    console.log('编辑维护:', maintenance);
  };

  const exportMaintenance = () => {
    const exportData = filteredMaintenance.value.map((maintenance) => ({
      维护单号: maintenance.maintenanceCode,
      资产名称: maintenance.assetName,
      维护类型: getTypeText(maintenance.type),
      负责人: maintenance.assignee,
      计划时间: maintenance.plannedDate,
      实际时间: maintenance.actualDate || '-',
      费用: `¥${maintenance.cost}`,
      状态: getStatusText(maintenance.status),
    }));

    const headers = Object.keys(exportData[0] || {});
    const csvContent = [headers.join(','), ...exportData.map((row) => headers.map((header) => `"${row[header] || ''}"`).join(','))].join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `维护数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert('维护数据导出成功！');
  };

  const confirmMaintenance = () => {
    if (!maintenanceForm.value.assetName || !maintenanceForm.value.type || !maintenanceForm.value.assignee) {
      alert('请填写必填字段：资产名称、维护类型、负责人');
      return;
    }

    const newMaintenance = {
      id: Date.now(),
      maintenanceCode: `MNT-${new Date().getFullYear()}-${String(maintenanceRecords.value.length + 1).padStart(3, '0')}`,
      assetName: maintenanceForm.value.assetName,
      type: maintenanceForm.value.type,
      assignee: maintenanceForm.value.assignee,
      plannedDate: maintenanceForm.value.plannedDate,
      actualDate: null,
      cost: maintenanceForm.value.cost || 0,
      status: 'planned',
    };

    maintenanceRecords.value.unshift(newMaintenance);
    maintenanceForm.value = {};
    showMaintenanceDialog.value = false;

    alert('维护任务创建成功！');
  };

  const saveContracts = () => {
    alert('维保合同保存成功！');
    showContractDialog.value = false;
  };

  // 初始化数据
  onMounted(() => {
    maintenanceRecords.value = [
      {
        id: 1,
        maintenanceCode: 'MNT-2024-001',
        assetName: 'Web服务器-01',
        type: 'preventive',
        assignee: '张三',
        plannedDate: '2024-01-20',
        actualDate: null,
        cost: 2500,
        status: 'planned',
      },
      {
        id: 2,
        maintenanceCode: 'MNT-2024-002',
        assetName: '核心交换机-01',
        type: 'corrective',
        assignee: '李四',
        plannedDate: '2024-01-18',
        actualDate: '2024-01-18',
        cost: 1800,
        status: 'completed',
      },
    ];

    contracts.value = [
      {
        id: 1,
        contractCode: 'CON-2024-001',
        supplier: '戴尔科技',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        cost: 50000,
        status: 'active',
      },
    ];
  });
</script>
