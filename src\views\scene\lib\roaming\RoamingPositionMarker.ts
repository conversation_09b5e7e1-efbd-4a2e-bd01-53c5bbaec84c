import * as THREE from 'three';
import { SceneManager } from '../SceneManager';
import { message } from 'ant-design-vue';

/**
 * 漫游位置标记工具
 * 用于在场景中标记漫游起始位置
 */
export class RoamingPositionMarker {
  private static instance: RoamingPositionMarker | null = null;

  // 基础组件
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private raycaster: THREE.Raycaster;
  private mouse: THREE.Vector2;

  // 标记状态
  private isMarkingActive: boolean = false;
  private markedPosition: THREE.Vector3 | null = null;
  private positionMarker: THREE.Mesh | null = null;

  // 标记材质和几何体
  private markerGeometry: THREE.SphereGeometry;
  private markerMaterial: THREE.MeshBasicMaterial;

  // 消息防抖
  private lastMessageTime: number = 0;
  private messageDebounceTime: number = 2000; // 2秒

  /**
   * 获取单例实例
   */
  public static getInstance(): RoamingPositionMarker {
    try {
      if (!RoamingPositionMarker.instance) {
        // 确保场景管理器已初始化
        const sceneManager = SceneManager.getInstance();
        if (!sceneManager || !sceneManager.scene) {
          console.error('[RoamingPositionMarker] 场景管理器未初始化，无法创建实例');
          throw new Error('场景管理器未初始化');
        }

        RoamingPositionMarker.instance = new RoamingPositionMarker();
      }
      return RoamingPositionMarker.instance;
    } catch (error) {
      console.error('[RoamingPositionMarker] 获取实例失败:', error);
      throw error;
    }
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    try {
      // 获取场景管理器
      const sceneManager = SceneManager.getInstance();

      // 检查场景管理器是否初始化完成
      if (!sceneManager) {
        throw new Error('场景管理器未初始化');
      }

      // 初始化基础组件
      this.scene = sceneManager.scene;

      if (!this.scene) {
        throw new Error('场景未初始化');
      }

      this.camera = sceneManager.camera as THREE.PerspectiveCamera;
      this.renderer = sceneManager.renderer;

      // 初始化射线检测器
      this.raycaster = new THREE.Raycaster();
      this.mouse = new THREE.Vector2();

      // 创建标记几何体和材质
      this.markerGeometry = new THREE.SphereGeometry(0.3, 16, 16);
      this.markerMaterial = new THREE.MeshBasicMaterial({
        color: 0x3b8ee6, // 蓝色
        transparent: true,
        opacity: 0.8,
      });

      console.log('[RoamingPositionMarker] 初始化完成');
    } catch (error) {
      console.error('[RoamingPositionMarker] 初始化失败:', error);
    }
  }

  /**
   * 开始标记位置模式
   */
  public startMarking(): void {
    if (this.isMarkingActive) {
      console.warn('[RoamingPositionMarker] 已经处于标记模式');
      return;
    }

    try {
      // 重新获取场景管理器和组件，确保使用最新的实例
      const sceneManager = SceneManager.getInstance();
      if (!sceneManager || !sceneManager.scene) {
        throw new Error('场景管理器未初始化');
      }

      // 更新场景、相机和渲染器引用
      this.scene = sceneManager.scene;
      this.camera = sceneManager.camera as THREE.PerspectiveCamera;
      this.renderer = sceneManager.renderer;

      // 检查渲染器和相机是否已初始化
      if (!this.renderer || !this.renderer.domElement || !this.camera) {
        throw new Error('渲染器或相机未初始化');
      }

      // 设置标记模式为活跃
      this.isMarkingActive = true;

      // 添加点击事件监听器
      window.addEventListener('click', this.handleClick);

      // 添加鼠标移动事件监听器（用于更新鼠标样式）
      window.addEventListener('mousemove', this.handleMouseMove);

      // 显示提示信息
      message.info('请点击地板位置来标记漫游起始点');

      console.log('[RoamingPositionMarker] 开始标记位置模式');
    } catch (error) {
      console.error('[RoamingPositionMarker] 开始标记位置失败:', error);
      message.error('无法开始标记，请刷新页面后重试');
      this.isMarkingActive = false;
    }
  }

  /**
   * 停止标记位置模式
   */
  public stopMarking(): void {
    if (!this.isMarkingActive) {
      return;
    }

    // 设置标记模式为非活跃
    this.isMarkingActive = false;

    // 移除事件监听器
    window.removeEventListener('click', this.handleClick);
    window.removeEventListener('mousemove', this.handleMouseMove);

    // 恢复鼠标样式
    document.body.style.cursor = 'default';

    console.log('[RoamingPositionMarker] 停止标记位置模式');
  }

  /**
   * 清除标记
   */
  public clearMarker(): void {
    try {
      // 重新获取场景管理器和场景，确保使用最新的实例
      const sceneManager = SceneManager.getInstance();
      if (!sceneManager || !sceneManager.scene) {
        throw new Error('场景管理器或场景未初始化');
      }

      // 更新场景引用
      this.scene = sceneManager.scene;

      if (this.positionMarker) {
        this.scene.remove(this.positionMarker);
        this.positionMarker = null;
      }
      this.markedPosition = null;

      // 请求渲染
      sceneManager.needsRender = true;

      console.log('[RoamingPositionMarker] 清除标记');
    } catch (error) {
      console.error('[RoamingPositionMarker] 清除标记失败:', error);
      // 确保状态被重置
      this.positionMarker = null;
      this.markedPosition = null;
    }
  }

  /**
   * 获取标记的位置
   */
  public getMarkedPosition(): THREE.Vector3 | null {
    return this.markedPosition ? this.markedPosition.clone() : null;
  }

  /**
   * 是否已标记位置
   */
  public hasMarkedPosition(): boolean {
    return this.markedPosition !== null;
  }

  /**
   * 处理点击事件
   */
  private handleClick = (event: MouseEvent): void => {
    if (!this.isMarkingActive) return;

    // 执行射线检测
    const hitPoint = this.performRaycast(event);

    if (hitPoint) {
      // 标记位置
      this.markPosition(hitPoint);

      // 停止标记模式
      this.stopMarking();

      // 显示成功消息
      message.success('已成功标记漫游起始位置');

      // 触发标记完成事件
      window.dispatchEvent(new CustomEvent('roaming-position-marked'));
    }
  };

  /**
   * 处理鼠标移动事件
   */
  private handleMouseMove = (event: MouseEvent): void => {
    if (!this.isMarkingActive) return;

    // 执行射线检测
    const hitPoint = this.performRaycast(event);

    // 更新鼠标样式
    document.body.style.cursor = hitPoint ? 'crosshair' : 'not-allowed';

    // 如果没有命中地板，显示提示（带防抖）
    if (!hitPoint) {
      const now = performance.now();
      if (now - this.lastMessageTime > this.messageDebounceTime) {
        message.warning('只能在地板区域标记位置');
        this.lastMessageTime = now;
      }
    }
  };

  /**
   * 执行射线检测
   */
  private performRaycast(event: MouseEvent): THREE.Vector3 | null {
    try {
      // 重新获取场景管理器和组件，确保使用最新的实例
      const sceneManager = SceneManager.getInstance();
      if (!sceneManager) {
        throw new Error('场景管理器未初始化');
      }

      // 更新相机和渲染器引用
      this.camera = sceneManager.camera as THREE.PerspectiveCamera;
      this.renderer = sceneManager.renderer;

      // 检查渲染器和DOM元素是否存在
      if (!this.renderer || !this.renderer.domElement) {
        throw new Error('渲染器或DOM元素不存在');
      }

      // 检查相机是否存在
      if (!this.camera) {
        throw new Error('相机不存在');
      }

      // 计算鼠标在归一化设备坐标中的位置
      const rect = this.renderer.domElement.getBoundingClientRect();
      this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      // 设置射线
      this.raycaster.setFromCamera(this.mouse, this.camera);

      // 获取场景中的地面对象
      const floorObjects = this.getFloorObjects();

      // 执行射线检测
      const intersects = this.raycaster.intersectObjects(floorObjects, true);

      // 如果有交点，返回第一个交点位置
      if (intersects.length > 0) {
        return intersects[0].point;
      }
    } catch (error) {
      console.error('[RoamingPositionMarker] 射线检测失败:', error);
    }

    // 如果没有交点或发生错误，返回null
    return null;
  }

  /**
   * 标记位置
   */
  private markPosition(position: THREE.Vector3): void {
    try {
      // 重新获取场景管理器和场景，确保使用最新的实例
      const sceneManager = SceneManager.getInstance();
      if (!sceneManager || !sceneManager.scene) {
        throw new Error('场景管理器或场景未初始化');
      }

      // 更新场景引用
      this.scene = sceneManager.scene;

      // 清除之前的标记
      this.clearMarker();

      // 保存标记位置
      this.markedPosition = position.clone();

      // 创建标记物体
      this.positionMarker = new THREE.Mesh(this.markerGeometry, this.markerMaterial);
      this.positionMarker.position.copy(position);

      // 稍微抬高标记，确保可见
      this.positionMarker.position.y += 0.1;

      // 添加到场景
      this.scene.add(this.positionMarker);

      // 请求渲染
      sceneManager.needsRender = true;

      console.log(`[RoamingPositionMarker] 标记位置: (${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)})`);
    } catch (error) {
      console.error('[RoamingPositionMarker] 标记位置失败:', error);
      message.error('标记位置失败');
    }
  }

  /**
   * 获取地面对象
   */
  private getFloorObjects(): THREE.Object3D[] {
    try {
      // 重新获取场景管理器和场景，确保使用最新的实例
      const sceneManager = SceneManager.getInstance();
      if (!sceneManager || !sceneManager.scene) {
        throw new Error('场景管理器或场景未初始化');
      }

      // 更新场景引用
      this.scene = sceneManager.scene;

      const floorObjects: THREE.Object3D[] = [];

      // 遍历场景中的所有对象
      this.scene.traverse((object) => {
        if (!object.visible || !(object instanceof THREE.Mesh)) return;

        const name = object.name.toLowerCase();
        // 检测地板对象 - 扩展检测条件
        if (name.includes('floor') || name.includes('地板') || name.includes('地面') || name.includes('ground')) {
          floorObjects.push(object);
        }
      });

      // 如果没有找到地板对象，尝试使用更宽松的检测条件
      if (floorObjects.length === 0) {
        console.warn('[RoamingPositionMarker] 未找到明确的地板对象，尝试使用更宽松的检测条件');

        this.scene.traverse((object) => {
          if (!object.visible || !(object instanceof THREE.Mesh)) return;

          // 检查对象的位置和法线方向，如果是水平的且在场景底部，可能是地板
          const position = object.position;
          const geometry = object.geometry;

          // 如果对象在场景底部且是水平的，可能是地板
          if (geometry && position.y < 0.5) {
            // 检查是否有法线向上的面
            let hasUpFacingNormal = false;

            if (geometry.attributes && geometry.attributes.normal) {
              const normals = geometry.attributes.normal.array;
              for (let i = 0; i < normals.length; i += 3) {
                // 检查是否有向上的法线 (y > 0.8)
                if (normals[i + 1] > 0.8) {
                  hasUpFacingNormal = true;
                  break;
                }
              }
            }

            if (hasUpFacingNormal) {
              floorObjects.push(object);
            }
          }
        });
      }

      return floorObjects;
    } catch (error) {
      console.error('[RoamingPositionMarker] 获取地板对象失败:', error);
      return [];
    }
  }
}
