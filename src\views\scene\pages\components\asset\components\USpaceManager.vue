<template>
  <div class="p-[1.2vw] h-full flex gap-[1.2vw] bg-gradient-to-br from-slate-900/50 to-slate-800/50">
    <!-- 左侧：U位管理面板 -->
    <div class="flex-1 flex flex-col">
      <!-- 统计信息 - 优化版本 -->
      <div
        class="bg-gradient-to-r from-blue-500/15 to-purple-500/15 backdrop-blur-sm p-[1vw] rounded-xl mb-[1.2vw] border border-blue-500/20 shadow-lg"
      >
        <div class="text-[0.9vw] text-white mb-[0.8vw] flex items-center">
          <div class="bg-blue-500/20 p-[0.3vw] rounded-lg mr-[0.5vw]">
            <BoxPlotOutlined class="text-[1.2vw] text-blue-400" />
          </div>
          <span class="font-semibold">U位空间统计</span>
        </div>
        <div class="grid grid-cols-4 gap-[0.8vw]">
          <div class="bg-black/30 backdrop-blur-sm p-[0.8vw] rounded-lg text-center border border-white/10 hover:border-white/20 transition-colors">
            <div class="text-[1.4vw] text-white font-bold mb-[0.2vw]">{{ cabinet.totalU }}</div>
            <div class="text-[0.7vw] text-gray-300">总U位数</div>
          </div>
          <div
            class="bg-black/30 backdrop-blur-sm p-[0.8vw] rounded-lg text-center border border-white/10 hover:border-blue-400/30 transition-colors"
          >
            <div class="text-[1.4vw] text-blue-400 font-bold mb-[0.2vw]">{{ cabinet.usedU }}</div>
            <div class="text-[0.7vw] text-gray-300">已用U位</div>
          </div>
          <div
            class="bg-black/30 backdrop-blur-sm p-[0.8vw] rounded-lg text-center border border-white/10 hover:border-green-400/30 transition-colors"
          >
            <div class="text-[1.4vw] text-green-400 font-bold mb-[0.2vw]">{{ cabinet.totalU - cabinet.usedU - reservedUCount }}</div>
            <div class="text-[0.7vw] text-gray-300">可用U位</div>
          </div>
          <div
            class="bg-black/30 backdrop-blur-sm p-[0.8vw] rounded-lg text-center border border-white/10 hover:border-yellow-400/30 transition-colors"
          >
            <div class="text-[1.4vw] text-yellow-400 font-bold mb-[0.2vw]">{{ reservedUCount }}</div>
            <div class="text-[0.7vw] text-gray-300">预留U位</div>
          </div>
        </div>
      </div>

      <!-- 操作面板 - 紧凑优化版本 -->
      <div
        class="bg-gradient-to-r from-green-500/15 to-blue-500/15 backdrop-blur-sm p-[0.6vw] rounded-lg mb-[0.8vw] border border-green-500/20 shadow-lg"
      >
        <div class="text-[0.75vw] text-white mb-[0.5vw] font-semibold">快速操作</div>
        <div class="flex gap-[0.4vw] flex-wrap">
          <button
            class="px-[0.6vw] py-[0.25vw] bg-gradient-to-r from-green-500 to-green-600 text-white text-[0.65vw] rounded-md hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 font-medium flex items-center"
            @click="showQuickAddDevice"
          >
            <BoxPlotOutlined class="mr-[0.2vw] text-[0.7vw]" />
            上架
          </button>
          <button
            class="px-[0.6vw] py-[0.25vw] bg-gradient-to-r from-blue-500 to-blue-600 text-white text-[0.65vw] rounded-md hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 font-medium flex items-center"
            @click="exportSpaceReport"
          >
            <FileTextOutlined class="mr-[0.2vw] text-[0.7vw]" />
            导出
          </button>
        </div>
      </div>

      <!-- U位详细列表 - 优化版本 -->
      <div class="flex-1 bg-gradient-to-r from-slate-500/15 to-gray-500/15 backdrop-blur-sm p-[1vw] rounded-xl border border-slate-500/20 shadow-lg">
        <div class="text-[0.9vw] text-white mb-[0.8vw] font-semibold">U位详细信息</div>
        <div
          class="h-[calc(100%-2.5vw)] overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-white/20 hover:scrollbar-thumb-white/40 pr-[0.5vw]"
        >
          <table class="w-full border-collapse">
            <thead class="sticky top-0 bg-gradient-to-r from-slate-700/80 to-gray-700/80 backdrop-blur-sm z-10">
              <tr class="text-[0.75vw] text-gray-200 border-b border-white/20">
                <th class="text-left p-[0.6vw] font-semibold">U位</th>
                <th class="text-left p-[0.6vw] font-semibold">状态</th>
                <th class="text-left p-[0.6vw] font-semibold">设备名称</th>
                <th class="text-left p-[0.6vw] font-semibold">设备类型</th>
                <th class="text-left p-[0.6vw] font-semibold">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="uSlot in uSlotDetails"
                :key="uSlot.position"
                class="text-[0.75vw] border-t border-white/10 hover:bg-white/10 transition-all duration-200"
              >
                <td class="p-[0.6vw] text-white font-mono font-bold">U{{ uSlot.position }}</td>
                <td class="p-[0.6vw]">
                  <span class="px-[0.5vw] py-[0.2vw] rounded-md text-[0.65vw] font-medium" :class="getStatusBadgeClass(uSlot.status)">
                    {{ uSlot.status }}
                  </span>
                </td>
                <td class="p-[0.6vw] text-gray-200 font-medium">{{ uSlot.deviceName || '-' }}</td>
                <td class="p-[0.6vw] text-gray-200">{{ uSlot.deviceType || '-' }}</td>
                <td class="p-[0.4vw]">
                  <div class="flex gap-[0.2vw]">
                    <button
                      v-if="uSlot.status === '空闲'"
                      class="px-[0.4vw] py-[0.15vw] bg-gradient-to-r from-green-500 to-green-600 text-white text-[0.6vw] rounded hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
                      @click="quickAddToSlot(uSlot.position)"
                    >
                      上架
                    </button>
                    <button
                      v-if="uSlot.status === '空闲'"
                      class="px-[0.4vw] py-[0.15vw] bg-gradient-to-r from-yellow-500 to-yellow-600 text-white text-[0.6vw] rounded hover:from-yellow-600 hover:to-yellow-700 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
                      @click="reserveSlot(uSlot.position)"
                    >
                      预留
                    </button>
                    <button
                      v-if="uSlot.status === '预留'"
                      class="px-[0.4vw] py-[0.15vw] bg-gradient-to-r from-gray-500 to-gray-600 text-white text-[0.6vw] rounded hover:from-gray-600 hover:to-gray-700 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
                      @click="unreserveSlot(uSlot.position)"
                    >
                      取消
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 右侧：2D机柜视图 -->
    <div
      class="w-[380px] bg-gradient-to-r from-blue-500/15 to-indigo-500/15 backdrop-blur-sm p-[1vw] rounded-xl border border-blue-500/20 shadow-lg shrink-0"
    >
      <div class="text-[0.9vw] text-white mb-[0.8vw] font-semibold flex items-center">
        <div class="bg-blue-500/20 p-[0.2vw] rounded-lg mr-[0.4vw]">
          <BoxPlotOutlined class="text-[1vw] text-blue-400" />
        </div>
        机柜布局视图
      </div>

      <!-- 视图控制 -->
      <div class="flex gap-[0.5vw] mb-[0.8vw]">
        <button
          v-for="view in viewModes"
          :key="view.key"
          class="px-[0.8vw] py-[0.3vw] text-[0.65vw] rounded-lg transition-all duration-200 font-medium"
          :class="
            currentView === view.key
              ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md'
              : 'bg-black/30 text-gray-300 hover:bg-black/40 hover:text-white'
          "
          @click="currentView = view.key"
        >
          {{ view.label }}
        </button>
      </div>

      <!-- 状态图例 -->
      <div class="flex justify-between items-center mb-[0.6vw] text-[0.6vw]">
        <div class="flex items-center">
          <div class="w-[0.5vw] h-[0.5vw] bg-blue-500 rounded mr-[0.2vw]"></div>
          <span class="text-gray-300">已占用</span>
        </div>
        <div class="flex items-center">
          <div class="w-[0.5vw] h-[0.5vw] bg-yellow-500 rounded mr-[0.2vw]"></div>
          <span class="text-gray-300">预留</span>
        </div>
        <div class="flex items-center">
          <div class="w-[0.5vw] h-[0.5vw] bg-green-500/50 rounded mr-[0.2vw]"></div>
          <span class="text-gray-300">空闲</span>
        </div>
      </div>

      <!-- 2D机柜视图 -->
      <div
        class="relative bg-gradient-to-br from-black/40 to-black/20 rounded-xl h-[calc(100%-6vw)] overflow-hidden border border-white/10 shadow-inner"
      >
        <div class="absolute inset-[0.8vw] h-[calc(100%-1.6vw)]">
          <div
            class="relative w-full h-full border border-blue-400/40 rounded-lg bg-gradient-to-b from-black/30 to-black/10 backdrop-blur-sm overflow-y-auto"
          >
            <!-- U位网格 -->
            <div class="flex flex-col min-h-full">
              <div
                v-for="n in cabinet.totalU"
                :key="n"
                class="flex-shrink-0 h-[1.2vw] border-b border-blue-400/20 relative cursor-pointer hover:bg-white/10 transition-colors duration-200 flex items-center"
                @click="selectUPosition(cabinet.totalU - n + 1)"
                :title="`U位 ${cabinet.totalU - n + 1}: ${getUSlotStatus(cabinet.totalU - n + 1)}`"
              >
                <!-- U位标签 -->
                <div class="absolute left-[0.3vw] text-[0.55vw] text-gray-300 font-mono font-bold"> U{{ cabinet.totalU - n + 1 }} </div>
                <!-- U位状态指示器 -->
                <div
                  class="absolute right-[0.3vw] w-[0.5vw] h-[0.5vw] rounded-full shadow-sm transition-all duration-200"
                  :class="getUPositionIndicator(cabinet.totalU - n + 1)"
                ></div>
                <!-- U位状态背景 -->
                <div class="absolute inset-0 transition-all duration-200" :class="getUSlotClass(cabinet.totalU - n + 1)"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 快速上架弹窗 -->
  <ModalDialog
    v-model:visible="quickAddDialogVisible"
    title="快速上架设备"
    width="40vw"
    :show-footer="true"
    @confirm="confirmQuickAdd"
    @cancel="quickAddDialogVisible = false"
  >
    <div class="p-[1vw] space-y-[0.8vw]">
      <div>
        <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">设备名称</div>
        <input
          v-model="quickAddForm.name"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
          placeholder="请输入设备名称"
        />
      </div>
      <div class="grid grid-cols-2 gap-[0.8vw]">
        <div>
          <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">起始U位</div>
          <input
            v-model.number="quickAddForm.startU"
            type="number"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
          />
        </div>
        <div>
          <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">设备大小(U)</div>
          <input
            v-model.number="quickAddForm.uSize"
            type="number"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
          />
        </div>
      </div>
      <div>
        <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">设备类型</div>
        <select
          v-model="quickAddForm.type"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
        >
          <option value="server">服务器</option>
          <option value="network">网络设备</option>
          <option value="storage">存储设备</option>
          <option value="ups">UPS设备</option>
          <option value="other">其他设备</option>
        </select>
      </div>
    </div>
  </ModalDialog>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { BoxPlotOutlined, FileTextOutlined } from '@ant-design/icons-vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';

  const props = defineProps({
    cabinet: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:cabinet']);

  // 状态管理
  const currentView = ref('front');
  const quickAddDialogVisible = ref(false);
  const selectedUPosition = ref(null);

  // 视图模式
  const viewModes = [
    { key: 'front', label: '正面' },
    { key: 'back', label: '背面' },
    { key: 'side', label: '侧面' },
  ];

  // 快速上架表单
  const quickAddForm = ref({
    name: '',
    startU: 1,
    uSize: 1,
    type: 'server',
  });

  // 预留U位数量
  const reservedUCount = computed(() => {
    return (props.cabinet.reservedSlots || []).length;
  });

  // U位详细信息
  const uSlotDetails = computed(() => {
    const slots = [];
    for (let i = 1; i <= props.cabinet.totalU; i++) {
      const device = props.cabinet.devices.find((d) => i >= d.startU && i <= d.endU);

      let status = '空闲';
      let deviceName = '';
      let deviceType = '';

      if (device) {
        status = '已占用';
        deviceName = device.name;
        deviceType = device.type || '未知';
      } else if ((props.cabinet.reservedSlots || []).includes(i)) {
        status = '预留';
      }

      slots.push({
        position: i,
        status,
        deviceName,
        deviceType,
      });
    }
    return slots.reverse(); // 从高U位到低U位显示
  });

  // 获取状态徽章样式
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case '已占用':
        return 'bg-blue-500/20 text-blue-400 border border-blue-500/40';
      case '预留':
        return 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/40';
      case '空闲':
        return 'bg-green-500/20 text-green-400 border border-green-500/40';
      default:
        return 'bg-gray-500/20 text-gray-400 border border-gray-500/40';
    }
  };

  // 获取U位指示器样式
  const getUPositionIndicator = (position) => {
    const slot = uSlotDetails.value.find((s) => s.position === position);
    if (!slot) return 'bg-gray-500/30';

    switch (slot.status) {
      case '已占用':
        return 'bg-blue-500';
      case '预留':
        return 'bg-yellow-500';
      case '空闲':
        return 'bg-green-500/50';
      default:
        return 'bg-gray-500/30';
    }
  };

  // 功能函数
  const showQuickAddDevice = () => {
    quickAddForm.value = {
      name: '',
      startU: 1,
      uSize: 1,
      type: 'server',
    };
    quickAddDialogVisible.value = true;
  };

  const exportSpaceReport = () => {
    console.log('导出空间使用报告');
  };

  const quickAddToSlot = (position) => {
    quickAddForm.value.startU = position;
    quickAddDialogVisible.value = true;
  };

  const reserveSlot = (position) => {
    const updatedCabinet = {
      ...props.cabinet,
      reservedSlots: [...(props.cabinet.reservedSlots || []), position],
    };
    emit('update:cabinet', updatedCabinet);
  };

  const unreserveSlot = (position) => {
    const updatedCabinet = {
      ...props.cabinet,
      reservedSlots: (props.cabinet.reservedSlots || []).filter((slot) => slot !== position),
    };
    emit('update:cabinet', updatedCabinet);
  };

  const selectUPosition = (position) => {
    selectedUPosition.value = position;
    console.log(`选择了U位: ${position}`);
  };

  // 获取U位状态
  const getUSlotStatus = (uPosition) => {
    const device = props.cabinet.devices.find((d) => uPosition >= d.startU && uPosition <= d.endU);
    if (device) return `已占用 (${device.name})`;

    const reservedSlots = props.cabinet.reservedSlots || [];
    if (reservedSlots.includes(uPosition)) return '预留';

    return '空闲';
  };

  // 获取U位样式类
  const getUSlotClass = (uPosition) => {
    const status = getUSlotStatus(uPosition);
    if (status.includes('已占用')) return 'bg-blue-500/10';
    if (status === '预留') return 'bg-yellow-500/10';
    return 'bg-green-500/5';
  };

  const confirmQuickAdd = () => {
    const device = {
      id: Date.now(),
      ...quickAddForm.value,
      endU: quickAddForm.value.startU + quickAddForm.value.uSize - 1,
    };

    const updatedCabinet = {
      ...props.cabinet,
      devices: [...props.cabinet.devices, device],
      usedU: props.cabinet.usedU + device.uSize,
    };

    emit('update:cabinet', updatedCabinet);
    quickAddDialogVisible.value = false;
  };
</script>
