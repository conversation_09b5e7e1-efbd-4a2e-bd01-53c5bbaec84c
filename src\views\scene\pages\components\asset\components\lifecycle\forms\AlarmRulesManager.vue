<template>
  <div class="p-[1vw] space-y-[1vw]">
    <!-- 操作工具栏 -->
    <div class="flex justify-between items-center">
      <h3 class="text-[0.8vw] text-white font-semibold">告警规则管理</h3>
      <button
        class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
        @click="addNewRule"
      >
        <PlusOutlined class="mr-[0.2vw]" />
        新增规则
      </button>
    </div>

    <!-- 规则列表 -->
    <div class="bg-black/20 rounded border border-white/10 overflow-hidden">
      <table class="w-full text-[0.6vw]">
        <thead class="bg-blue-500/20">
          <tr>
            <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">规则名称</th>
            <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">监控指标</th>
            <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">阈值条件</th>
            <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">告警级别</th>
            <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">状态</th>
            <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(rule, index) in rules" :key="rule.id" class="hover:bg-white/5">
            <td class="p-[0.6vw] text-white border-b border-white/5">{{ rule.name }}</td>
            <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ rule.metric }}</td>
            <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ rule.condition }}</td>
            <td class="p-[0.6vw] border-b border-white/5">
              <span
                :class="[
                  'px-[0.4vw] py-[0.1vw] rounded text-[0.5vw]',
                  rule.level === 'critical'
                    ? 'bg-red-500/20 text-red-400'
                    : rule.level === 'warning'
                      ? 'bg-yellow-500/20 text-yellow-400'
                      : 'bg-blue-500/20 text-blue-400',
                ]"
              >
                {{ getLevelText(rule.level) }}
              </span>
            </td>
            <td class="p-[0.6vw] border-b border-white/5">
              <label class="flex items-center">
                <input type="checkbox" v-model="rule.enabled" class="mr-[0.3vw]" />
                <span class="text-[0.5vw]" :class="rule.enabled ? 'text-green-400' : 'text-gray-400'">
                  {{ rule.enabled ? '启用' : '禁用' }}
                </span>
              </label>
            </td>
            <td class="p-[0.6vw] border-b border-white/5">
              <div class="flex space-x-[0.4vw]">
                <button class="text-blue-400 hover:text-blue-300 text-[0.5vw]" @click="editRule(index)"> 编辑 </button>
                <button class="text-red-400 hover:text-red-300 text-[0.5vw]" @click="deleteRule(index)"> 删除 </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 新增/编辑规则表单 -->
    <div v-if="showRuleForm" class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
      <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">
        {{ editingIndex >= 0 ? '编辑规则' : '新增规则' }}
      </h4>

      <div class="grid grid-cols-2 gap-[1vw]">
        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">规则名称 *</label>
          <input
            v-model="ruleForm.name"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入规则名称"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">监控指标 *</label>
          <select
            v-model="ruleForm.metric"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="">请选择监控指标</option>
            <option value="CPU使用率">CPU使用率</option>
            <option value="内存使用率">内存使用率</option>
            <option value="磁盘使用率">磁盘使用率</option>
            <option value="网络流量">网络流量</option>
            <option value="响应时间">响应时间</option>
          </select>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">比较操作符</label>
          <select
            v-model="ruleForm.operator"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value=">">大于 ></option>
            <option value=">=">大于等于 >=</option>
            <option value="<">小于 <</option>
            <option value="<=">小于等于 <=</option>
            <option value="=">等于 =</option>
          </select>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">阈值 *</label>
          <input
            v-model.number="ruleForm.threshold"
            type="number"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入阈值"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">告警级别</label>
          <select
            v-model="ruleForm.level"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="info">信息</option>
            <option value="warning">警告</option>
            <option value="critical">严重</option>
          </select>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">持续时间（分钟）</label>
          <input
            v-model.number="ruleForm.duration"
            type="number"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="触发告警的持续时间"
          />
        </div>
      </div>

      <div class="mt-[0.8vw]">
        <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">告警描述</label>
        <textarea
          v-model="ruleForm.description"
          rows="3"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400 resize-none"
          placeholder="请输入告警描述"
        ></textarea>
      </div>

      <div class="flex justify-end space-x-[0.6vw] mt-[0.8vw]">
        <button class="px-[0.8vw] py-[0.4vw] bg-gray-500 text-white text-[0.6vw] rounded hover:bg-gray-600 transition-colors" @click="cancelEdit">
          取消
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
          @click="saveRule"
        >
          保存
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch, computed } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';

  const props = defineProps({
    rules: { type: Array, default: () => [] },
  });

  const emit = defineEmits(['update:rules']);

  const rules = ref([
    {
      id: 1,
      name: 'CPU使用率过高告警',
      metric: 'CPU使用率',
      operator: '>',
      threshold: 80,
      level: 'warning',
      enabled: true,
      duration: 5,
      description: '当CPU使用率超过80%持续5分钟时触发告警',
    },
    {
      id: 2,
      name: '内存使用率严重告警',
      metric: '内存使用率',
      operator: '>',
      threshold: 90,
      level: 'critical',
      enabled: true,
      duration: 3,
      description: '当内存使用率超过90%持续3分钟时触发严重告警',
    },
    {
      id: 3,
      name: '磁盘空间不足告警',
      metric: '磁盘使用率',
      operator: '>',
      threshold: 85,
      level: 'warning',
      enabled: false,
      duration: 10,
      description: '当磁盘使用率超过85%持续10分钟时触发告警',
    },
    ...props.rules,
  ]);

  const showRuleForm = ref(false);
  const editingIndex = ref(-1);
  const ruleForm = ref({
    name: '',
    metric: '',
    operator: '>',
    threshold: 0,
    level: 'warning',
    enabled: true,
    duration: 5,
    description: '',
  });

  // 计算属性
  const condition = computed(() => {
    if (ruleForm.value.metric && ruleForm.value.operator && ruleForm.value.threshold) {
      return `${ruleForm.value.metric} ${ruleForm.value.operator} ${ruleForm.value.threshold}%`;
    }
    return '';
  });

  watch(
    rules,
    (newValue) => {
      emit('update:rules', newValue);
    },
    { deep: true }
  );

  const getLevelText = (level) => {
    const levelMap = {
      info: '信息',
      warning: '警告',
      critical: '严重',
    };
    return levelMap[level] || level;
  };

  const addNewRule = () => {
    ruleForm.value = {
      name: '',
      metric: '',
      operator: '>',
      threshold: 0,
      level: 'warning',
      enabled: true,
      duration: 5,
      description: '',
    };
    editingIndex.value = -1;
    showRuleForm.value = true;
  };

  const editRule = (index) => {
    ruleForm.value = { ...rules.value[index] };
    editingIndex.value = index;
    showRuleForm.value = true;
  };

  const deleteRule = (index) => {
    if (confirm('确定要删除这条告警规则吗？')) {
      rules.value.splice(index, 1);
    }
  };

  const saveRule = () => {
    if (!ruleForm.value.name || !ruleForm.value.metric || !ruleForm.value.threshold) {
      alert('请填写必填字段：规则名称、监控指标、阈值');
      return;
    }

    const newRule = {
      ...ruleForm.value,
      id: editingIndex.value >= 0 ? rules.value[editingIndex.value].id : Date.now(),
      condition: `${ruleForm.value.metric} ${ruleForm.value.operator} ${ruleForm.value.threshold}%`,
    };

    if (editingIndex.value >= 0) {
      rules.value[editingIndex.value] = newRule;
    } else {
      rules.value.push(newRule);
    }

    cancelEdit();
  };

  const cancelEdit = () => {
    showRuleForm.value = false;
    editingIndex.value = -1;
    ruleForm.value = {
      name: '',
      metric: '',
      operator: '>',
      threshold: 0,
      level: 'warning',
      enabled: true,
      duration: 5,
      description: '',
    };
  };
</script>
