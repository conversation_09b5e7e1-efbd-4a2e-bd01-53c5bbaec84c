<template>
  <a-row>
    <a-col :span="24">
      <div class="relative w-full">
        <div class="flex justify-between items-center mb-4">
          <span class="text-base">电表实时用量</span>
          <a-button type="primary" size="small" @click="handleRefresh" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </div>
        <div ref="chartRef" style="width: 100%; height: 50vh"></div>
      </div>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { debounce } from 'lodash-es';
  import * as echarts from 'echarts';
  import { getElectricityRealtime } from '/@/api/energy/electricity';
  import { ReloadOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  const electricityRealtimeData = ref<any[]>([]);
  const chartRef = ref<HTMLElement | null>(null);
  let chartInstance: echarts.ECharts | null = null;

  const loading = ref(false);

  // 渲染图表
  const renderChart = () => {
    if (!chartInstance || !chartRef.value) return;

    const data = electricityRealtimeData.value;
    if (data.length === 0) {
      chartInstance.clear();
      chartInstance.setOption({
        title: {
          text: '电表实时用量',
          left: 'center',
          textStyle: {
            color: '#2c3e50',
            fontWeight: 'bold',
            fontSize: 16,
          },
        },
        xAxis: { show: false },
        yAxis: { show: false },
        series: [],
        graphic: {
          elements: [
            {
              type: 'text',
              left: 'center',
              top: 'center',
              style: {
                text: '暂无实时数据',
                fontSize: 16,
                fill: '#999',
              },
            },
          ],
        },
      });
      return;
    }

    const categories = data.map((item) => item.name);
    const values = data.map((item) => item.value);
    const minVal = Math.min(...values, 0);
    const maxVal = Math.max(...values, 0);

    chartInstance.setOption({
      title: {
        text: '电表实时用量',
        left: 'center',
        textStyle: {
          color: '#2c3e50',
          fontWeight: 'bold',
          fontSize: 16,
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(50,50,50,0.8)',
        borderColor: '#333',
        textStyle: { color: '#fff' },
        formatter: function (params: any) {
          const param = params[0];
          if (param) {
            return `${param.name}<br/>${param.marker}${param.seriesName}: ${param.value} kWh`;
          }
          return '';
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
        backgroundColor: '#f9f9f9',
        borderColor: '#eee',
        show: true,
      },
      visualMap: {
        show: false,
        min: minVal,
        max: maxVal,
        inRange: {
          color: ['#FAD7A0', '#F39C12', '#B9770E'], // 类似用水量的颜色方案
        },
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          interval: 'auto',
          rotate: 0,
          formatter: function (value: string) {
            return value;
          },
          margin: 10,
          hideOverlap: true,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        name: '用电量(kWh)',
        splitLine: {
          lineStyle: {
            color: '#e0e0e0',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '实时用量',
          type: 'bar',
          data: values,
          barWidth: '50%',
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 8,
            shadowOffsetY: 3,
          },
          label: {
            show: true,
            position: 'top',
            color: '#333',
            fontSize: 11,
            fontWeight: 'bold',
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 12,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              borderColor: '#555',
              borderWidth: 1,
            },
          },
        },
      ],
      dataZoom: [
        {
          type: 'slider',
          xAxisIndex: 0,
          filterMode: 'filter',
          startValue: categories.length > 10 ? categories.length - 10 : 0,
          endValue: categories.length - 1,
          bottom: '1%',
          height: 20,
          dataBackground: {
            lineStyle: { color: '#ddd' },
            areaStyle: { color: '#eee' },
          },
          selectedDataBackground: {
            lineStyle: { color: '#F39C12' },
            areaStyle: { color: '#F39C12', opacity: 0.4 },
          },
          fillerColor: 'rgba(243, 156, 18, 0.2)',
          borderColor: '#ddd',
          handleIcon:
            'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: { color: '#fff', shadowBlur: 3, shadowColor: 'rgba(0, 0, 0, 0.6)', shadowOffsetX: 2, shadowOffsetY: 2 },
          textStyle: { color: '#333' },
        },
        {
          type: 'inside',
          xAxisIndex: 0,
          filterMode: 'filter',
        },
      ],
    });
  };

  const initChart = () => {
    if (chartRef.value) {
      chartInstance = echarts.init(chartRef.value);
      window.addEventListener('resize', handleResize);
    }
  };

  const handleResize = debounce(() => {
    if (chartInstance) {
      chartInstance.resize();
    }
  }, 300);

  const loadData = debounce(async () => {
    try {
      loading.value = true;
      const result = await getElectricityRealtime();
      if (Array.isArray(result)) {
        electricityRealtimeData.value = result.map((item: any) => ({
          name: item.deviceName,
          value: parseFloat(item.valueData) || 0,
        }));
        renderChart();
      } else if (result === null || (Array.isArray(result) && result.length === 0)) {
        electricityRealtimeData.value = [];
        renderChart(); // 调用 renderChart 来显示无数据状态
        message.info('暂无电表实时数据');
      } else {
        throw new Error('数据格式错误');
      }
    } catch (error) {
      console.error('电表实时数据加载错误:', error);
      message.error('加载电表实时数据失败');
      electricityRealtimeData.value = [];
      renderChart(); // 调用 renderChart 来显示错误/无数据状态
    } finally {
      loading.value = false;
    }
  }, 500);

  const handleRefresh = () => {
    if (!loading.value) {
      loadData();
    }
  };

  onMounted(() => {
    initChart();
    loadData();
  });

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
    window.removeEventListener('resize', handleResize);
  });
</script>
