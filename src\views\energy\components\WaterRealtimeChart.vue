<template>
  <a-row>
    <a-col :span="24">
      <div class="relative w-full">
        <div class="flex justify-between items-center mb-4">
          <span class="text-base">水表实时用量</span>
          <a-button type="primary" size="small" @click="handleRefresh" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </div>
        <div ref="chartRef" style="width: 100%; height: 50vh"></div>
      </div>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { debounce } from 'lodash-es';
  import * as echarts from 'echarts';
  import { getWaterMeterList } from '/@/api/energy/water';
  import { ReloadOutlined } from '@ant-design/icons-vue';

  const waterRealtimeData = ref<any[]>([]);
  const chartRef = ref<HTMLElement | null>(null);
  let chartInstance: echarts.ECharts | null = null;

  const loading = ref(false);

  // 自动刷新相关
  const refreshInterval = ref<number | null>(null);
  const AUTO_REFRESH_INTERVAL = 30000; // 30秒刷新一次

  const startAutoRefresh = () => {
    stopAutoRefresh();
    refreshInterval.value = window.setInterval(() => {
      loadData();
    }, AUTO_REFRESH_INTERVAL);
  };

  const stopAutoRefresh = () => {
    if (refreshInterval.value !== null) {
      window.clearInterval(refreshInterval.value);
      refreshInterval.value = null;
    }
  };

  // 渲染图表
  const renderChart = () => {
    if (!chartInstance || !chartRef.value) return;

    const data = waterRealtimeData.value;
    if (data.length === 0) {
      return;
    }

    // 计算最大最小值用于visualMap
    const values = data.map((item) => item.value);
    const min = Math.min(...values, 0);
    const max = Math.max(...values, 0);

    // 设置水表名称和用水量值
    const categories = data.map((item) => item.name);
    const values2 = data.map((item) => item.value);

    chartInstance.setOption({
      title: {
        text: '水表实时用量',
        left: 'center',
        textStyle: {
          color: '#2c3e50',
          fontWeight: 'bold',
          fontSize: 16,
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(50,50,50,0.8)',
        borderColor: '#333',
        textStyle: { color: '#fff' },
        formatter: function (params: any) {
          const param = params[0];
          if (param) {
            return `${param.name}<br/>${param.marker}${param.seriesName}: ${param.value} 吨`;
          }
          return '';
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%', // 调整以容纳 dataZoom
        containLabel: true,
        backgroundColor: '#f9f9f9',
        borderColor: '#eee',
        show: true,
      },
      visualMap: {
        show: false,
        min: min,
        max: max,
        inRange: {
          color: ['#83bff6', '#188df0', '#0052CC'],
        },
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          interval: 'auto',
          rotate: 0,
          formatter: function (value: string) {
            return value; // 显示完整文本
          },
          margin: 10,
          hideOverlap: true,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        name: '用水量(吨)',
        splitLine: {
          lineStyle: {
            color: '#e0e0e0',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '实时用量', // 为系列添加名称
          type: 'bar',
          data: values2,
          barWidth: '50%',
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 8,
            shadowOffsetY: 3,
          },
          label: {
            show: true,
            position: 'top',
            color: '#333',
            fontSize: 11,
            fontWeight: 'bold',
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 12,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              borderColor: '#555',
              borderWidth: 1,
            },
          },
        },
      ],
      dataZoom: [
        {
          type: 'slider',
          xAxisIndex: 0,
          filterMode: 'filter',
          startValue: categories.length > 10 ? categories.length - 10 : 0,
          endValue: categories.length - 1,
          bottom: '1%',
          height: 20,
          dataBackground: {
            lineStyle: { color: '#ddd' },
            areaStyle: { color: '#eee' },
          },
          selectedDataBackground: {
            lineStyle: { color: '#83bff6' },
            areaStyle: { color: '#83bff6', opacity: 0.4 },
          },
          fillerColor: 'rgba(131, 191, 246, 0.2)',
          borderColor: '#ddd',
          handleIcon:
            'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: { color: '#fff', shadowBlur: 3, shadowColor: 'rgba(0, 0, 0, 0.6)', shadowOffsetX: 2, shadowOffsetY: 2 },
          textStyle: { color: '#333' },
        },
        {
          type: 'inside',
          xAxisIndex: 0,
          filterMode: 'filter',
        },
      ],
    });
  };

  // 初始化图表实例
  const initChart = () => {
    if (chartRef.value) {
      chartInstance = echarts.init(chartRef.value);
      window.addEventListener('resize', handleResize);
    }
  };

  // 处理窗口大小变化
  const handleResize = debounce(() => {
    if (chartInstance) {
      chartInstance.resize();
    }
  }, 300);

  // 使用防抖处理数据加载
  const loadData = debounce(async () => {
    try {
      loading.value = true;
      const result = await getWaterMeterList();
      if (Array.isArray(result)) {
        waterRealtimeData.value = result.map((item: any) => ({
          name: item.name || item.meterName || `设备 ${item.id || item.meterId || '未知'}`,
          value: parseFloat(item.valueData) || 0,
        }));
        renderChart();
      } else {
        throw new Error('数据格式错误');
      }
    } catch (error) {
      console.error('水表实时数据加载错误:', error);
      message.error('加载水表实时数据失败');
    } finally {
      loading.value = false;
    }
  }, 500);

  const handleRefresh = () => {
    if (!loading.value) {
      loadData();
    }
  };

  onMounted(() => {
    initChart();
    loadData();
    startAutoRefresh();
  });

  onUnmounted(() => {
    stopAutoRefresh();
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
    window.removeEventListener('resize', handleResize);
  });

  defineExpose({
    loadData,
    refresh: loadData,
    loading,
  });
</script>
