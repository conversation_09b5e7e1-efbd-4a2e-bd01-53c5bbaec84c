<template>
  <div>
    <!-- 巡视按钮 -->
    <a-tooltip placement="right" :title="getTooltip">
      <div
        class="w-[2.2vw] h-[2.2vw] bg-[rgba(23,43,77,0.8)] border border-[rgba(36,108,249,0.3)] rounded-[0.3vw] flex flex-col items-center justify-center cursor-pointer text-white transition-all duration-300 relative overflow-hidden hover:(bg-[rgba(36,108,249,0.2)] border-[rgba(36,108,249,0.5)] scale-105) before:content-empty before:absolute before:left-[-100%] before:top-[-50%] before:w-[200%] before:h-[200%] before:bg-gradient-to-r before:from-transparent before:via-[rgba(36,108,249,0.3)] before:to-transparent before:rotate-45 hover:before:animate-scan"
        :class="{
          'bg-[rgba(36,108,249,0.3)] border-[rgba(36,108,249,0.8)] shadow-[0_0_10px_rgba(36,108,249,0.3)]': isTouring,
          'opacity-50 cursor-not-allowed': isProcessing || globalThreeStore.isFloorSwitching,
        }"
        @click="handleTourButtonClick"
        data-view-control="tour"
      >
        <div class="flex flex-col items-center justify-center h-full">
          <LoadingOutlined v-if="isProcessing" class="text-[0.8vw]" />
          <template v-else>
            <component :is="getTourIcon" class="text-[0.8vw]" />
            <span class="text-[0.5vw] text-white/80 mt-[0.1vw]">{{ getTourText }}</span>
          </template>
        </div>
      </div>
    </a-tooltip>

    <!-- 巡视控制面板 -->
    <div
      v-if="isTouring"
      class="fixed bottom-[2vw] left-1/2 -translate-x-1/2 bg-black/70 text-white px-[1vw] py-[0.5vw] rounded-full text-[0.8vw] z-50 flex items-center"
    >
      <component :is="getTourIcon" class="text-[0.8vw] mr-[0.5vw] text-blue-400" />
      <span>
        正在沿预定义路径巡视中 ({{ currentWaypoint + 1 }}/{{ totalWaypoints }})
        <a-button-group class="ml-[1vw]">
          <a-button v-if="isPaused" type="primary" size="small" @click="resumeTour" :disabled="isProcessing">
            <template #icon><PlayCircleOutlined /></template>
            继续
          </a-button>
          <a-button v-else type="primary" size="small" @click="pauseTour" :disabled="isProcessing">
            <template #icon><PauseCircleOutlined /></template>
            暂停
          </a-button>
          <a-button type="primary" danger size="small" @click="stopTour" :disabled="isProcessing">
            <template #icon><StopOutlined /></template>
            停止
          </a-button>
        </a-button-group>
        <span class="ml-[0.5vw] text-[0.7vw] text-white/60">按下<span class="mx-[0.3vw] px-[0.3vw] bg-white/20 rounded">ESC</span>退出</span>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
  import { PlayCircleOutlined, PauseCircleOutlined, StopOutlined, LoadingOutlined, RobotOutlined } from '@ant-design/icons-vue';
  import { TourController, TourState, TourWaypoint } from '../lib/tour/TourController';
  import { useGlobalThreeStore } from '../store/globalThreeStore';
  import { ControlManager } from '../lib/control/ControlManager';
  import { SceneManager } from '../lib/SceneManager';

  // 状态定义
  const isTouring = ref(false);
  const isPaused = ref(false);
  const isProcessing = ref(false);
  const currentWaypoint = ref(0);
  const totalWaypoints = ref(0);
  const currentWaypointName = ref('');
  const globalThreeStore = useGlobalThreeStore();

  // 计算属性
  const getTourIcon = computed(() => {
    if (isTouring.value) {
      return isPaused.value ? PlayCircleOutlined : PauseCircleOutlined;
    }
    return RobotOutlined;
  });

  const getTourText = computed(() => {
    if (isTouring.value) {
      return isPaused.value ? '继续' : '暂停';
    }
    return '巡视';
  });

  const getTooltip = computed(() => {
    if (globalThreeStore.isFloorSwitching) {
      return '楼层切换中，暂时无法使用巡视功能';
    }
    if (isProcessing.value) {
      return '正在处理巡视请求，请稍候...';
    }
    if (isTouring.value) {
      return isPaused.value ? '继续自动巡视' : '暂停自动巡视';
    }
    return '开始自动巡视（沿预定义路径点行走）';
  });

  // 监听ESC键
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && isTouring.value) {
      stopTour();
    }
  };

  // 处理巡视按钮点击
  const handleTourButtonClick = async () => {
    // 检查是否可以执行操作
    if (isProcessing.value || globalThreeStore.isFloorSwitching) {
      if (globalThreeStore.isFloorSwitching) {
        showMessage('楼层切换中，请等待切换完成后再使用巡视功能', 'warning');
        return;
      }
      return;
    }

    if (!globalThreeStore.canUserInteract) {
      showMessage('系统正在加载中，请稍候再试...', 'warning');
      return;
    }

    // 根据当前状态执行相应操作
    if (isTouring.value) {
      if (isPaused.value) {
        resumeTour();
      } else {
        pauseTour();
      }
    } else {
      startTour();
    }
  };

  // 开始巡视
  const startTour = async () => {
    try {
      isProcessing.value = true;

      // 获取巡视控制器
      const tourController = TourController.getInstance();

      // 自动生成巡视路径
      const waypoints = tourController.generateTourPath();

      if (waypoints.length === 0) {
        showMessage('未找到可巡视的设备，无法开始巡视', 'warning');
        isProcessing.value = false;
        return;
      }

      // 更新状态
      totalWaypoints.value = waypoints.length;
      currentWaypoint.value = 0;

      // 启用透视功能
      await enableTransparency();

      // 设置事件回调
      const events = {
        onStart: () => {
          isTouring.value = true;
          isPaused.value = false;
          isProcessing.value = false;
          showMessage('自动巡视已开始', 'success');
        },
        onPause: () => {
          isPaused.value = true;
          showMessage('自动巡视已暂停', 'info');
        },
        onResume: () => {
          isPaused.value = false;
          showMessage('自动巡视已继续', 'info');
        },
        onStop: () => {
          isTouring.value = false;
          isPaused.value = false;
          showMessage('自动巡视已停止', 'info');
        },
        onWaypointReached: (waypoint: TourWaypoint, index: number) => {
          currentWaypoint.value = index;
          currentWaypointName.value = waypoint.name || '';

          // 显示当前巡视设备信息
          if (waypoint.name) {
            showMessage(`正在巡视: ${waypoint.name}`, 'info');
          }
        },
        onPathComplete: () => {
          stopTour();
          showMessage('巡视路径已完成', 'success');
        },
      };

      // 开始巡视
      tourController.start(waypoints, events);

      // 添加键盘事件监听
      window.addEventListener('keydown', handleKeyDown);
    } catch (error) {
      console.error('开始巡视失败:', error);
      showMessage('开始巡视失败', 'error');
      isProcessing.value = false;
    }
  };

  // 暂停巡视
  const pauseTour = () => {
    const tourController = TourController.getInstance();
    tourController.pause();
  };

  // 继续巡视
  const resumeTour = () => {
    const tourController = TourController.getInstance();
    tourController.resume();
  };

  // 停止巡视
  const stopTour = async () => {
    try {
      isProcessing.value = true;

      // 停止巡视
      const tourController = TourController.getInstance();
      tourController.stop();

      // 移除键盘事件监听
      window.removeEventListener('keydown', handleKeyDown);

      // 恢复透明状态
      await restoreTransparency();

      // 更新状态
      isTouring.value = false;
      isPaused.value = false;
      isProcessing.value = false;
    } catch (error) {
      console.error('停止巡视失败:', error);
      showMessage('停止巡视失败', 'error');
      isProcessing.value = false;
    }
  };

  // 启用透视功能
  const enableTransparency = async (): Promise<void> => {
    return new Promise((resolve) => {
      // 获取透视按钮
      const viewControl = document.querySelector('[data-view-control="transparency"]');
      const isCurrentlyTransparent = viewControl?.classList.contains('active') || false;

      // 如果已经是透明状态，直接返回
      if (isCurrentlyTransparent) {
        resolve();
        return;
      }

      // 创建点击事件
      const event = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
      });

      // 监听透明状态变化
      const checkTransparency = () => {
        const isNowTransparent = viewControl?.classList.contains('active') || false;
        if (isNowTransparent) {
          // 显示提示
          showMessage('已自动启用透视功能，以便更清晰地查看设备', 'info');
          resolve();
        } else {
          // 如果还没变化，继续等待
          setTimeout(checkTransparency, 100);
        }
      };

      // 触发透视功能
      viewControl?.dispatchEvent(event);

      // 开始检查
      checkTransparency();
    });
  };

  // 恢复透明状态
  const restoreTransparency = async (): Promise<void> => {
    return new Promise((resolve) => {
      // 获取透视按钮
      const viewControl = document.querySelector('[data-view-control="transparency"]');
      const isCurrentlyTransparent = viewControl?.classList.contains('active') || false;

      // 如果当前是透明状态，关闭透明
      if (isCurrentlyTransparent) {
        // 创建点击事件
        const event = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window,
        });

        // 监听透明状态变化
        const checkTransparency = () => {
          const isNowTransparent = viewControl?.classList.contains('active') || false;
          if (!isNowTransparent) {
            resolve();
          } else {
            // 如果还没变化，继续等待
            setTimeout(checkTransparency, 100);
          }
        };

        // 触发透视功能关闭
        viewControl?.dispatchEvent(event);

        // 开始检查
        checkTransparency();
      } else {
        // 如果已经是不透明状态，直接返回
        resolve();
      }
    });
  };

  // 显示消息的安全方法
  const showMessage = (text: string, type: string = 'info', options = {}) => {
    try {
      if (window.$message) {
        window.$message[type]?.(text);
      }
    } catch (error) {
      console.log('显示消息失败:', error);
    }
  };

  // 组件卸载时清理
  onBeforeUnmount(() => {
    // 确保停止巡视
    const tourController = TourController.getInstance();
    if (tourController.getState() !== TourState.IDLE) {
      tourController.stop();
    }

    // 移除事件监听
    window.removeEventListener('keydown', handleKeyDown);
  });
</script>
