<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 能源效率概览 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-tachometer-alt mr-[0.4vw] text-blue-400"></i>
        能源效率概览
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in efficiencyStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.status" class="text-[0.5vw] mt-[0.2vw]" :class="stat.statusClass">
            {{ stat.status }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：设备能效分析 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-microchip mr-[0.4vw] text-blue-400"></i>
            设备能效分析
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="category in deviceCategories"
              :key="category.key"
              @click="activeCategory = category.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activeCategory === category.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30',
              ]"
            >
              {{ category.label }}
            </button>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div v-for="device in filteredDevices" :key="device.id" class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all">
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <i :class="device.icon" class="mr-[0.6vw] text-blue-400"></i>
                  <span class="text-[0.65vw] text-white font-medium">{{ device.name }}</span>
                </div>
                <span class="text-[0.6vw]" :class="getEfficiencyClass(device.efficiency)"> {{ device.efficiency }}% </span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400 mb-[0.3vw]">
                <span>功率：{{ device.power }}</span>
                <span>运行时间：{{ device.runtime }}</span>
              </div>
              <div class="flex justify-between text-[0.6vw] mb-[0.3vw]">
                <span class="text-gray-400">能效等级：</span>
                <span :class="getGradeClass(device.grade)">{{ device.grade }}</span>
              </div>
              <div class="mt-[0.3vw]">
                <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                  <div
                    :class="getEfficiencyProgressClass(device.efficiency)"
                    class="h-[0.3vw] rounded-full transition-all"
                    :style="{ width: device.efficiency + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：能效指标和优化建议 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 关键能效指标 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-chart-line mr-[0.4vw] text-blue-400"></i>
            关键能效指标
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="indicator in keyIndicators" :key="indicator.name" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ indicator.name }}</span>
                <span class="text-[0.6vw]" :class="getIndicatorStatusClass(indicator.status)">
                  {{ indicator.value }}
                </span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>标准值：{{ indicator.standard }}</span>
                <span
                  >状态：<span :class="getIndicatorStatusClass(indicator.status)">{{ indicator.statusText }}</span></span
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 能效排行榜 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-trophy mr-[0.4vw] text-blue-400"></i>
            设备能效排行
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="(device, index) in efficiencyRanking" :key="device.name" class="flex items-center justify-between">
              <div class="flex items-center">
                <div
                  class="w-[1.2vw] h-[1.2vw] rounded-full flex items-center justify-center text-[0.5vw] font-bold mr-[0.4vw]"
                  :class="getRankingClass(index)"
                >
                  {{ index + 1 }}
                </div>
                <span class="text-[0.6vw] text-gray-400">{{ device.name }}</span>
              </div>
              <span class="text-[0.6vw] text-white font-medium">{{ device.efficiency }}%</span>
            </div>
          </div>
        </div>

        <!-- 优化建议 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-lightbulb mr-[0.4vw] text-blue-400"></i>
            能效优化建议
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="suggestion in optimizationSuggestions" :key="suggestion.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex items-start">
                <div class="w-[0.6vw] h-[0.6vw] rounded-full bg-blue-400 mt-[0.3vw] mr-[0.4vw] flex-shrink-0"></div>
                <div class="flex-1">
                  <div class="text-[0.6vw] text-white mb-[0.2vw]">{{ suggestion.title }}</div>
                  <div class="text-[0.5vw] text-gray-400 mb-[0.2vw]">{{ suggestion.description }}</div>
                  <div class="flex justify-between text-[0.5vw]">
                    <span class="text-green-400">预期提升：{{ suggestion.expectedImprovement }}</span>
                    <span class="text-yellow-400">优先级：{{ suggestion.priority }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 能源效率概览数据
  const efficiencyStats = ref([
    {
      label: '整体能效',
      value: '87.5%',
      valueClass: 'text-green-400',
      status: '优秀',
      statusClass: 'text-green-400',
    },
    {
      label: 'PUE值',
      value: '1.42',
      valueClass: 'text-blue-400',
      status: '良好',
      statusClass: 'text-blue-400',
    },
    {
      label: '设备利用率',
      value: '92.3%',
      valueClass: 'text-green-400',
      status: '正常',
      statusClass: 'text-green-400',
    },
    {
      label: '能效等级',
      value: 'A级',
      valueClass: 'text-green-400',
      status: '达标',
      statusClass: 'text-green-400',
    },
    {
      label: '优化潜力',
      value: '12.5%',
      valueClass: 'text-yellow-400',
      status: '可提升',
      statusClass: 'text-yellow-400',
    },
  ]);

  // 设备分类
  const deviceCategories = ref([
    { key: 'all', label: '全部' },
    { key: 'cooling', label: '制冷设备' },
    { key: 'power', label: '供电设备' },
    { key: 'lighting', label: '照明设备' },
  ]);

  const activeCategory = ref('all');

  // 设备能效数据
  const devices = ref([
    {
      id: 1,
      name: '精密空调A1',
      category: 'cooling',
      icon: 'fas fa-snowflake',
      efficiency: 95,
      power: '15.2 kW',
      runtime: '24h/天',
      grade: 'A+',
    },
    {
      id: 2,
      name: 'UPS主机B1',
      category: 'power',
      icon: 'fas fa-battery-full',
      efficiency: 92,
      power: '25.8 kW',
      runtime: '24h/天',
      grade: 'A',
    },
    {
      id: 3,
      name: 'LED照明系统',
      category: 'lighting',
      icon: 'fas fa-lightbulb',
      efficiency: 88,
      power: '8.5 kW',
      runtime: '12h/天',
      grade: 'A',
    },
    {
      id: 4,
      name: '精密空调A2',
      category: 'cooling',
      icon: 'fas fa-snowflake',
      efficiency: 90,
      power: '16.1 kW',
      runtime: '24h/天',
      grade: 'A',
    },
    {
      id: 5,
      name: '配电柜C1',
      category: 'power',
      icon: 'fas fa-bolt',
      efficiency: 85,
      power: '12.3 kW',
      runtime: '24h/天',
      grade: 'B+',
    },
    {
      id: 6,
      name: '应急照明',
      category: 'lighting',
      icon: 'fas fa-lightbulb',
      efficiency: 82,
      power: '3.2 kW',
      runtime: '24h/天',
      grade: 'B+',
    },
  ]);

  // 筛选后的设备
  const filteredDevices = computed(() => {
    if (activeCategory.value === 'all') {
      return devices.value;
    }
    return devices.value.filter((device) => device.category === activeCategory.value);
  });

  // 关键能效指标
  const keyIndicators = ref([
    { name: 'PUE值', value: '1.42', standard: '< 1.5', status: 'good', statusText: '良好' },
    { name: 'COP值', value: '3.8', standard: '> 3.5', status: 'good', statusText: '优秀' },
    { name: '负载率', value: '78.5%', standard: '70-85%', status: 'good', statusText: '正常' },
    { name: '功率因数', value: '0.95', standard: '> 0.9', status: 'good', statusText: '优秀' },
    { name: '谐波畸变率', value: '3.2%', standard: '< 5%', status: 'good', statusText: '合格' },
  ]);

  // 能效排行榜
  const efficiencyRanking = ref([
    { name: '精密空调A1', efficiency: 95 },
    { name: 'UPS主机B1', efficiency: 92 },
    { name: '精密空调A2', efficiency: 90 },
    { name: 'LED照明系统', efficiency: 88 },
    { name: '配电柜C1', efficiency: 85 },
  ]);

  // 优化建议
  const optimizationSuggestions = ref([
    {
      id: 1,
      title: '空调系统负载优化',
      description: '根据实际负载调整空调运行策略，避免过度制冷',
      expectedImprovement: '5-8%',
      priority: '高',
    },
    {
      id: 2,
      title: '设备定期维护',
      description: '建立设备维护计划，保持设备最佳运行状态',
      expectedImprovement: '3-5%',
      priority: '中',
    },
    {
      id: 3,
      title: '智能控制系统升级',
      description: '升级设备控制系统，实现更精确的能耗控制',
      expectedImprovement: '8-12%',
      priority: '高',
    },
    {
      id: 4,
      title: '能效监控系统完善',
      description: '完善能效监控体系，实时掌握设备运行状态',
      expectedImprovement: '2-4%',
      priority: '中',
    },
  ]);

  // 获取效率样式
  const getEfficiencyClass = (efficiency) => {
    if (efficiency >= 90) return 'text-green-400';
    if (efficiency >= 80) return 'text-yellow-400';
    return 'text-red-400';
  };

  // 获取等级样式
  const getGradeClass = (grade) => {
    if (grade.includes('A')) return 'text-green-400';
    if (grade.includes('B')) return 'text-yellow-400';
    return 'text-red-400';
  };

  // 获取效率进度条样式
  const getEfficiencyProgressClass = (efficiency) => {
    if (efficiency >= 90) return 'bg-green-400';
    if (efficiency >= 80) return 'bg-yellow-400';
    return 'bg-red-400';
  };

  // 获取指标状态样式
  const getIndicatorStatusClass = (status) => {
    switch (status) {
      case 'good':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'danger':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取排行样式
  const getRankingClass = (index) => {
    switch (index) {
      case 0:
        return 'bg-yellow-400 text-black';
      case 1:
        return 'bg-gray-400 text-black';
      case 2:
        return 'bg-orange-400 text-black';
      default:
        return 'bg-blue-400/30 text-blue-400';
    }
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
