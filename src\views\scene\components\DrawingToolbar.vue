<template>
  <div
    v-if="isVisible"
    class="fixed bottom-[2vw] left-1/2 -translate-x-1/2 bg-black/70 text-white px-[1vw] py-[0.5vw] rounded-[0.5vw] text-[0.8vw] z-50 flex flex-col items-center"
  >
    <div class="flex items-center">
      <component :is="EditOutlined" class="text-[0.8vw] mr-[0.5vw] text-blue-400" />
      <span>
        {{ getToolbarTitle }}
        <a-button-group class="ml-[1vw]">
          <template v-if="isDrawing">
            <a-button type="primary" size="small" @click="completeDrawing" :disabled="!canComplete">
              <template #icon><CheckOutlined /></template>
              完成
            </a-button>
            <a-button type="primary" danger size="small" @click="cancelDrawing">
              <template #icon><CloseOutlined /></template>
              取消
            </a-button>
          </template>
          <template v-else-if="isPatrolling">
            <a-button v-if="isPaused" type="primary" size="small" @click="resumePatrol" :disabled="isProcessing">
              <template #icon><PlayCircleOutlined /></template>
              继续
            </a-button>
            <a-button v-else type="primary" size="small" @click="pausePatrol" :disabled="isProcessing">
              <template #icon><PauseCircleOutlined /></template>
              暂停
            </a-button>
            <a-button type="primary" danger size="small" @click="stopPatrol" :disabled="isProcessing">
              <template #icon><StopOutlined /></template>
              停止
            </a-button>
          </template>
          <template v-else>
            <a-button type="primary" size="small" @click="startDrawing" :disabled="isProcessing">
              <template #icon><EditOutlined /></template>
              绘制路径
            </a-button>
          </template>
        </a-button-group>
      </span>
    </div>

    <!-- 使用说明 -->
    <div v-if="isDrawing" class="mt-[0.5vw] text-[0.7vw] text-white/80 px-[0.5vw] py-[0.3vw] bg-black/50 rounded-[0.3vw] max-w-[40vw]">
      <div class="font-bold mb-[0.2vw] text-blue-300">使用说明：</div>
      <ul class="list-disc pl-[1vw] space-y-[0.2vw]">
        <li>只能在<span class="text-green-400 font-bold">地板(Floor)</span>区域上绘制路径点</li>
        <li>绿色高亮区域表示可绘制区域，鼠标变为十字形时可点击</li>
        <li>点击或拖拽鼠标在地板上添加路径点</li>
        <li>至少需要2个点才能完成路径</li>
        <li
          >按<span class="px-[0.3vw] bg-white/20 rounded">Enter</span>键完成绘制，按<span class="px-[0.3vw] bg-white/20 rounded">ESC</span>键取消</li
        >
      </ul>
    </div>

    <!-- 巡检设置 -->
    <div
      v-if="!isDrawing && !isPatrolling"
      class="mt-[0.5vw] text-[0.7vw] text-white/80 px-[0.5vw] py-[0.3vw] bg-black/50 rounded-[0.3vw] max-w-[40vw]"
    >
      <div class="font-bold mb-[0.2vw] text-blue-300">巡检设置：</div>

      <!-- 终点处理方式 -->
      <div class="flex items-center mb-[0.3vw]">
        <span class="mr-[0.5vw]">终点处理方式：</span>
        <a-radio-group v-model:value="endpointMode" size="small">
          <a-radio-button :value="PatrolEndpointMode.LOOP">循环模式</a-radio-button>
          <a-radio-button :value="PatrolEndpointMode.REVERSE">往返模式</a-radio-button>
        </a-radio-group>
      </div>
      <div class="text-[0.65vw] text-white/60 pl-[0.5vw] mb-[0.5vw]">
        <div v-if="endpointMode === PatrolEndpointMode.LOOP">
          <span class="text-blue-300">循环模式：</span>到达终点后，相机会平滑过渡回起点继续巡检
        </div>
        <div v-else> <span class="text-blue-300">往返模式：</span>到达终点后，相机会原地旋转180度，沿原路返回 </div>
      </div>

      <!-- 巡检速度 -->
      <div class="flex items-center mb-[0.3vw]">
        <span class="mr-[0.5vw]">巡检速度：</span>
        <a-slider v-model:value="patrolSpeed" :min="1" :max="10" :step="0.5" class="w-[10vw]" @change="updatePatrolSpeed" />
        <span class="ml-[0.5vw]">{{ patrolSpeed }} 米/秒</span>
      </div>

      <!-- 高级设置 -->
      <div class="mt-[0.5vw]">
        <a-collapse ghost>
          <a-collapse-panel key="1" header="高级设置">
            <div class="flex flex-col space-y-[0.3vw]">
              <!-- 显示路径标记 -->
              <a-checkbox v-model:checked="showPathMarkers" @change="updatePatrolConfig">显示路径标记</a-checkbox>

              <!-- 动态前视距离 -->
              <a-checkbox v-model:checked="dynamicLookAhead" @change="updatePatrolConfig">动态前视距离</a-checkbox>

              <!-- 平滑转弯 -->
              <a-checkbox v-model:checked="smoothTurning" @change="updatePatrolConfig">平滑转弯</a-checkbox>

              <!-- 自适应相机高度 -->
              <a-checkbox v-model:checked="adaptiveCameraHeight" @change="updatePatrolConfig">自适应相机高度</a-checkbox>

              <!-- 超广角效果 -->
              <a-checkbox v-model:checked="useUltraWideFov" @change="updatePatrolConfig">超广角视场效果</a-checkbox>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>

      <!-- 保存/加载路径 -->
      <div class="flex items-center mt-[0.5vw] justify-between">
        <a-button size="small" type="primary" @click="showSavePathModal" :disabled="!hasDrawnPath"> 保存路径 </a-button>
        <a-button size="small" @click="showLoadPathModal" :disabled="savedPaths.length === 0"> 加载路径 </a-button>
      </div>
    </div>

    <!-- 巡检控制面板 -->
    <div v-if="isPatrolling" class="mt-[0.5vw] text-[0.7vw] text-white/80 px-[0.5vw] py-[0.3vw] bg-black/50 rounded-[0.3vw] max-w-[40vw]">
      <!-- 巡检进度 -->
      <div class="flex items-center mb-[0.3vw]">
        <span class="mr-[0.5vw]">巡检进度：</span>
        <a-progress :percent="Math.round(patrolProgress * 100)" size="small" :show-info="false" class="w-[10vw]" />
        <span class="ml-[0.5vw]">{{ Math.round(patrolProgress * 100) }}%</span>
      </div>

      <!-- 巡检方向 -->
      <div class="flex items-center mb-[0.3vw]">
        <span class="mr-[0.5vw]">当前方向：</span>
        <span class="text-blue-300">{{ patrolDirection === PatrolDirection.FORWARD ? '前进' : '返回' }}</span>
      </div>

      <!-- 巡检速度调节 -->
      <div class="flex items-center mb-[0.3vw]">
        <span class="mr-[0.5vw]">巡检速度：</span>
        <a-slider v-model:value="patrolSpeed" :min="1" :max="10" :step="0.5" class="w-[10vw]" @change="updatePatrolSpeed" />
        <span class="ml-[0.5vw]">{{ patrolSpeed }} 米/秒</span>
      </div>
    </div>
  </div>

  <!-- 保存路径对话框 -->
  <a-modal v-model:visible="showSaveDialog" title="保存巡检路径" @ok="saveCurrentPath" okText="保存" cancelText="取消" :maskClosable="false">
    <div class="mb-[1vw]">
      <p class="mb-[0.5vw]">请输入路径名称：</p>
      <a-input v-model:value="pathNameInput" placeholder="请输入路径名称" />
    </div>
  </a-modal>

  <!-- 加载路径对话框 -->
  <a-modal v-model:visible="showLoadDialog" title="加载巡检路径" @ok="loadSelectedPath" okText="加载" cancelText="取消" :maskClosable="false">
    <div class="mb-[1vw]">
      <p class="mb-[0.5vw]">请选择要加载的路径：</p>
      <a-list bordered :data-source="savedPaths">
        <template #renderItem="{ item, index }">
          <a-list-item :class="{ 'bg-blue-100': selectedPathIndex === index }" @click="selectedPathIndex = index">
            <div class="flex justify-between w-full">
              <span>{{ item.name }}</span>
              <a-button type="link" danger @click.stop="deleteSelectedPath(index)">删除</a-button>
            </div>
          </a-list-item>
        </template>
        <template #empty>
          <div class="text-center py-[1vw]">暂无保存的路径</div>
        </template>
      </a-list>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, computed, onBeforeUnmount, onMounted } from 'vue';
  import { EditOutlined, CheckOutlined, CloseOutlined, PlayCircleOutlined, PauseCircleOutlined, StopOutlined } from '@ant-design/icons-vue';
  import * as THREE from 'three';
  import { PathDrawingTool, PathPoint } from '../lib/patrol/PathDrawingTool';
  import { CustomPatrolController, PatrolDirection, PatrolEndpointMode } from '../lib/patrol/CustomPatrolController';
  import { useGlobalThreeStore } from '../store/globalThreeStore';

  // 状态定义
  const isVisible = ref(false);
  const isDrawing = ref(false);
  const isPatrolling = ref(false);
  const isPaused = ref(false);
  const isProcessing = ref(false);
  const pathPointCount = ref(0);
  const patrolProgress = ref(0);
  const patrolDirection = ref<PatrolDirection>(PatrolDirection.FORWARD);
  const endpointMode = ref<PatrolEndpointMode>(PatrolEndpointMode.LOOP); // 默认使用循环模式
  const globalThreeStore = useGlobalThreeStore();

  // 巡检配置
  const patrolSpeed = ref(3.0); // 默认速度3.0米/秒
  const showPathMarkers = ref(true);
  const dynamicLookAhead = ref(true);
  const smoothTurning = ref(true);
  const adaptiveCameraHeight = ref(false);
  const useUltraWideFov = ref(true); // 默认启用超广角效果

  // 路径保存/加载
  const hasDrawnPath = ref(false);
  const savedPaths = ref<Array<{ name: string; points: THREE.Vector3[]; config: any }>>([]);
  const pathNameInput = ref('');
  const showSaveDialog = ref(false);
  const showLoadDialog = ref(false);
  const selectedPathIndex = ref(-1);

  // 2D视图状态
  let is2DMode = false;

  // 切换到2D视图
  const switchTo2DView = () => {
    try {
      // 查找2D/3D切换按钮
      const viewControlElement = document.querySelector('[data-view-control="2d3d"]') as HTMLElement;
      if (!viewControlElement) {
        console.warn('[DrawingToolbar] 无法找到2D/3D切换按钮');
        return false;
      }

      // 获取当前2D/3D模式状态
      is2DMode = viewControlElement.classList.contains('active');

      // 如果当前不是2D模式，则点击切换按钮
      if (!is2DMode) {
        console.log('[DrawingToolbar] 自动切换到2D视图');
        viewControlElement.click();
        return true;
      }

      return true; // 已经是2D模式
    } catch (error) {
      console.error('[DrawingToolbar] 切换到2D视图失败:', error);
      return false;
    }
  };

  // 复位视角
  const resetView = () => {
    try {
      // 查找复位按钮
      const resetViewElement = document.querySelector('[data-view-control="reset"]') as HTMLElement;
      if (!resetViewElement) {
        console.warn('[DrawingToolbar] 无法找到复位按钮');
        return false;
      }

      console.log('[DrawingToolbar] 自动复位视角');
      resetViewElement.click();
      return true;
    } catch (error) {
      console.error('[DrawingToolbar] 复位视角失败:', error);
      return false;
    }
  };

  // 计算属性
  const getToolbarTitle = computed(() => {
    if (isDrawing.value) {
      return `正在绘制巡检路径`;
    } else if (isPatrolling.value) {
      return `巡检模式`;
    }
    return '巡检路径绘制工具';
  });

  const canComplete = computed(() => {
    return pathPointCount.value >= 2;
  });

  // 显示工具栏
  const showToolbar = () => {
    isVisible.value = true;
  };

  // 隐藏工具栏
  const hideToolbar = () => {
    isVisible.value = false;
  };

  // 显示消息 - 禁用以保持干净的界面
  const showMessage = (content: string) => {
    // Message notifications disabled for clean interface during patrol
    console.log(`[DrawingToolbar] ${content}`);
  };

  // 开始绘制
  const startDrawing = async () => {
    try {
      isProcessing.value = true;

      // 检查是否可以执行操作
      if (globalThreeStore.isFloorSwitching) {
        showMessage('楼层切换中，请等待切换完成后再使用巡检功能');
        isProcessing.value = false;
        return;
      }

      if (!globalThreeStore.canUserInteract) {
        showMessage('系统正在加载中，请稍候再试...');
        isProcessing.value = false;
        return;
      }

      // 注意：不再自动启用透明效果
      // 如果需要透明效果，用户可以手动点击透明按钮

      // 获取绘制工具
      const drawingTool = PathDrawingTool.getInstance();

      // 设置事件回调
      const events = {
        onStart: () => {
          isDrawing.value = true;
          isProcessing.value = false;
          pathPointCount.value = 0;
          showMessage('开始绘制巡检路径，点击场景添加路径点');
          // 触发自定义事件通知父组件绘制已开始
          window.dispatchEvent(new CustomEvent('patrol-drawing-started'));
        },
        onPointAdded: (_point: PathPoint, index: number) => {
          pathPointCount.value = index + 1;
        },
        onComplete: (points: PathPoint[]) => {
          isDrawing.value = false;
          showMessage(`路径绘制完成，共 ${points.length} 个点`);

          // 触发自定义事件通知父组件绘制已完成
          window.dispatchEvent(new CustomEvent('patrol-drawing-completed'));

          // 开始巡检
          startPatrol(points);
        },
        onCancel: () => {
          isDrawing.value = false;
          // 不再自动恢复透明效果
          showMessage('已取消路径绘制');

          // 触发自定义事件通知父组件绘制已取消
          window.dispatchEvent(new CustomEvent('patrol-drawing-cancelled'));

          // 隐藏工具栏
          hideToolbar();
        },
      };

      // 开始绘制
      drawingTool.startDrawing(events);
    } catch (error) {
      console.error('开始绘制失败:', error);
      showMessage('开始绘制失败');
      isProcessing.value = false;
    }
  };

  // 完成绘制
  const completeDrawing = () => {
    if (!canComplete.value) {
      showMessage('至少需要2个点才能完成路径');
      return;
    }

    const drawingTool = PathDrawingTool.getInstance();
    drawingTool.completeDrawing();
  };

  // 取消绘制
  const cancelDrawing = () => {
    const drawingTool = PathDrawingTool.getInstance();
    drawingTool.cancelDrawing();
  };

  // 开始巡检
  const startPatrol = async (points: PathPoint[]) => {
    try {
      isProcessing.value = true;

      // 自动切换到2D视图
      switchTo2DView();

      // 获取巡检控制器
      const patrolController = CustomPatrolController.getInstance();

      // 设置路径点
      patrolController.setPathPoints(points);

      // 设置巡检配置
      patrolController.setConfig({
        endpointMode: endpointMode.value,
        speed: patrolSpeed.value,
        showPathMarkers: showPathMarkers.value,
        dynamicLookAhead: dynamicLookAhead.value,
        smoothTurning: smoothTurning.value,
        adaptiveCameraHeight: adaptiveCameraHeight.value,
        useUltraWideFov: useUltraWideFov.value, // 使用变量控制是否启用超广角效果
        cameraFov: 110, // 设置超广角FOV值为110度
      });

      // 设置hasDrawnPath为true，表示已有路径
      hasDrawnPath.value = true;

      // 设置事件回调
      const events = {
        onStart: () => {
          isPatrolling.value = true;
          isPaused.value = false;
          isProcessing.value = false;
          patrolDirection.value = PatrolDirection.FORWARD;
          patrolProgress.value = 0;
          showMessage('自动巡检已开始');
        },
        onPause: () => {
          isPaused.value = true;
          showMessage('自动巡检已暂停');
        },
        onResume: () => {
          isPaused.value = false;
          showMessage('自动巡检已继续');
        },
        onStop: () => {
          isPatrolling.value = false;
          isPaused.value = false;
          hideToolbar();
          showMessage('自动巡检已停止');
        },
        onDirectionChange: (direction: PatrolDirection) => {
          patrolDirection.value = direction;
          const directionText = direction === PatrolDirection.FORWARD ? '前进' : '返回';
          showMessage(`巡检方向已改变: ${directionText}`);
        },
        onPositionUpdate: (_position: THREE.Vector3, progress: number) => {
          patrolProgress.value = progress;
        },
      };

      // 开始巡检
      patrolController.start(events);
    } catch (error) {
      console.error('开始巡检失败:', error);
      showMessage('开始巡检失败');
      isProcessing.value = false;
    }
  };

  // 暂停巡检
  const pausePatrol = () => {
    const patrolController = CustomPatrolController.getInstance();
    patrolController.pause();
  };

  // 继续巡检
  const resumePatrol = () => {
    const patrolController = CustomPatrolController.getInstance();
    patrolController.resume();
  };

  // 停止巡检
  const stopPatrol = async () => {
    try {
      isProcessing.value = true;

      // 停止巡检
      const patrolController = CustomPatrolController.getInstance();
      patrolController.stop();

      // 不再自动恢复透明状态

      // 更新状态
      isPatrolling.value = false;
      isPaused.value = false;
      isProcessing.value = false;
      hideToolbar();

      // 自动切换回2D视图
      switchTo2DView();

      // 自动执行复位操作
      resetView();

      // 触发自定义事件通知父组件巡检已停止
      window.dispatchEvent(new CustomEvent('patrol-stopped'));
    } catch (error) {
      console.error('停止巡检失败:', error);
      showMessage('停止巡检失败');
      isProcessing.value = false;
    }
  };

  // 监听ESC键
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      if (isDrawing.value) {
        cancelDrawing();
      } else if (isPatrolling.value) {
        stopPatrol();
      }
    }
  };

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown);

  // 组件卸载前清理
  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);

    // 如果正在绘制或巡检，停止
    if (isDrawing.value) {
      cancelDrawing();
    }

    if (isPatrolling.value) {
      stopPatrol();
    }
  });

  // 更新巡检速度
  const updatePatrolSpeed = (value: number) => {
    const patrolController = CustomPatrolController.getInstance();
    patrolController.setConfig({
      speed: value,
    });
    console.log(`[DrawingToolbar] 巡检速度已更新: ${value} 米/秒`);
  };

  // 更新巡检配置
  const updatePatrolConfig = () => {
    const patrolController = CustomPatrolController.getInstance();
    patrolController.setConfig({
      showPathMarkers: showPathMarkers.value,
      dynamicLookAhead: dynamicLookAhead.value,
      smoothTurning: smoothTurning.value,
      adaptiveCameraHeight: adaptiveCameraHeight.value,
      useUltraWideFov: useUltraWideFov.value, // 使用变量控制是否启用超广角效果
      cameraFov: 110, // 设置超广角FOV值为110度
    });
    console.log('[DrawingToolbar] 巡检配置已更新');
  };

  // 显示保存路径对话框
  const showSavePathModal = () => {
    pathNameInput.value = `巡检路径_${new Date().toLocaleString().replace(/[\/\s:]/g, '_')}`;
    showSaveDialog.value = true;
  };

  // 保存当前路径
  const saveCurrentPath = () => {
    if (!pathNameInput.value.trim()) {
      console.warn('[DrawingToolbar] 路径名称不能为空');
      return;
    }

    const patrolController = CustomPatrolController.getInstance();
    const pathData = patrolController.savePatrolPath(pathNameInput.value);

    // 保存到本地存储
    savedPaths.value.push(pathData);
    localStorage.setItem('savedPatrolPaths', JSON.stringify(savedPaths.value));

    showSaveDialog.value = false;
    console.log(`[DrawingToolbar] 路径已保存: ${pathNameInput.value}`);
  };

  // 显示加载路径对话框
  const showLoadPathModal = () => {
    selectedPathIndex.value = -1;
    showLoadDialog.value = true;
  };

  // 加载选中的路径
  const loadSelectedPath = () => {
    if (selectedPathIndex.value < 0 || selectedPathIndex.value >= savedPaths.value.length) {
      console.warn('[DrawingToolbar] 未选择有效的路径');
      return;
    }

    const pathData = savedPaths.value[selectedPathIndex.value];
    const patrolController = CustomPatrolController.getInstance();

    // 加载路径
    patrolController.loadPatrolPath(pathData);

    // 更新UI状态
    showLoadDialog.value = false;
    hasDrawnPath.value = true;

    // 更新配置控件
    patrolSpeed.value = pathData.config.speed;
    showPathMarkers.value = pathData.config.showPathMarkers;
    dynamicLookAhead.value = pathData.config.dynamicLookAhead;
    smoothTurning.value = pathData.config.smoothTurning;
    adaptiveCameraHeight.value = pathData.config.adaptiveCameraHeight;

    console.log(`[DrawingToolbar] 已加载路径: ${pathData.name}`);
  };

  // 删除选中的路径
  const deleteSelectedPath = (index: number) => {
    if (index < 0 || index >= savedPaths.value.length) {
      console.warn('[DrawingToolbar] 未选择有效的路径');
      return;
    }

    const pathName = savedPaths.value[index].name;
    savedPaths.value.splice(index, 1);

    // 更新本地存储
    localStorage.setItem('savedPatrolPaths', JSON.stringify(savedPaths.value));

    selectedPathIndex.value = -1;
    console.log(`[DrawingToolbar] 已删除路径: ${pathName}`);
  };

  // 加载保存的路径
  onMounted(() => {
    try {
      const savedPathsJson = localStorage.getItem('savedPatrolPaths');
      if (savedPathsJson) {
        const paths = JSON.parse(savedPathsJson);
        if (Array.isArray(paths)) {
          savedPaths.value = paths;
          console.log(`[DrawingToolbar] 已加载${paths.length}条保存的路径`);
        }
      }
    } catch (error) {
      console.error('[DrawingToolbar] 加载保存的路径失败:', error);
    }
  });

  // 导出方法供父组件调用
  defineExpose({
    showToolbar,
    hideToolbar,
    startDrawing,
    stopPatrol,
  });
</script>

<style scoped>
  .drawing-toolbar {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
  }
</style>
