<template>
  <div class="p-4">
    <BasicTable @register="registerTable"> </BasicTable>

    <Modal v-model:visible="historyModalVisible" title="历史告警数据" :width="800">
      <div v-if="historyModalVisible">
        <BasicTable @register="registerHistoryTable" />
      </div>
      <template #footer>
        <div class="flex justify-end">
          <button
            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-primary rounded-md"
            @click="historyModalVisible = false"
          >
            确定
          </button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script lang="ts" setup name="ba-sensor-alarm">
  import { ref, h } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getBAAlarmList, getBAAlarmHistory, BAAlarmItem } from '/@/api/scene';
  import { debounce } from 'lodash-es';
  import { Modal } from 'ant-design-vue';

  const { createMessage } = useMessage();
  const historyModalVisible = ref(false);
  const historyLoading = ref(false);
  const currentRecord = ref<Recordable | null>(null);
  const historyDataSource = ref<BAAlarmItem[]>([]);

  // 定义表格列
  const columns = [
    {
      title: '名称',
      dataIndex: 'remark',
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'valueData',
      width: 100,
      customRender: ({ record }: { record: Recordable }) => {
        const isAlarm = record.valueData == '1' || record.valueData == 1;
        return h('span', { class: isAlarm ? 'text-red-500' : 'text-green-500' }, isAlarm ? '告警' : '正常');
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
      fixed: true,
      align: 'center' as any,
      customRender: ({ record }: { record: Recordable }) => {
        return h('div', { class: 'flex justify-center' }, [
          h(
            'button',
            {
              class: 'inline-flex items-center text-primary cursor-pointer',
              onClick: () => handleViewHistory(record),
            },
            [h('i', { class: 'i-ant-design:history-outlined mr-1' }), '历史告警']
          ),
        ]);
      },
    },
  ];

  // 搜索表单配置
  const searchFormSchema = [
    {
      field: 'remark',
      label: '名称',
      component: 'Input',
      colProps: { span: 8 },
    },
  ];

  // 注册表格
  const [registerTable, { reload }] = useTable({
    api: async (params: Recordable) => {
      try {
        const response = await getBAAlarmList(params);

        // 检查标准响应格式
        if (response && typeof response === 'object' && 'success' in response && response.success) {
          if (Array.isArray(response.result)) {
            return {
              records: response.result,
              total: response.result.length,
            };
          }
        }

        // 处理其他可能的数据结构
        if (Array.isArray(response)) {
          return {
            records: response,
            total: response.length,
          };
        } else if (typeof response === 'object' && response !== null) {
          // 使用类型断言来避免类型错误
          const resp = response as any;
          if (Array.isArray(resp.records)) {
            return { records: resp.records, total: resp.total || resp.records.length };
          } else if (Array.isArray(resp.items)) {
            return {
              records: resp.items,
              total: resp.total || resp.items.length,
            };
          }
        }

        // 默认返回空数据
        return { records: [], total: 0 };
      } catch (error) {
        console.error('获取数据失败:', error);
        return { records: [], total: 0 };
      }
    },
    columns,
    formConfig: {
      schemas: searchFormSchema as any, // 使用类型断言来解决类型不匹配问题
      labelWidth: 80,
    },
    useSearchForm: true,
    pagination: {
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条`,
    },
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,
    // 请求字段配置
    fetchSetting: {
      pageField: 'pageNum',
      sizeField: 'pageSize',
      listField: 'records',
      totalField: 'total',
    },
  });

  // 历史告警表格列配置
  const historyColumns = [
    {
      title: '名称',
      dataIndex: 'remark',
      width: 200,
    },
    {
      title: '数值',
      dataIndex: 'valueData',
      width: 100,
      customRender: ({ record }: { record: Recordable }) => {
        return `${record.valueData}${record.unit || ''}`;
      },
    },
    {
      title: '时间',
      dataIndex: 'dataTime',
      width: 150,
    },
  ];

  // 历史告警表格注册
  const [registerHistoryTable] = useTable({
    columns: historyColumns,
    pagination: {
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条`,
    },
    dataSource: historyDataSource,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,
    canResize: false,
    useSearchForm: false,
  });

  // 防抖处理查询历史按钮点击
  const handleViewHistory = debounce(async (record: Recordable) => {
    if (!record) {
      return;
    }

    currentRecord.value = record;
    historyModalVisible.value = true;
    historyLoading.value = true;

    try {
      const params = {
        pageNum: 1,
        pageSize: 100,
        baDataId: record.id,
      };

      const res = await getBAAlarmHistory(params);
      const response = res as any;
      if (response && Array.isArray(response.records)) {
        historyDataSource.value = response.records;
        if (response.records.length === 0) {
          createMessage.warning('未找到历史告警数据');
        }
      } else {
        historyDataSource.value = [];
        createMessage.warning('未找到历史告警数据');
      }
    } catch (error) {
      console.error('获取历史数据失败:', error);
      historyDataSource.value = [];
    } finally {
      historyLoading.value = false;
    }
  }, 500);

  // 刷新表格
  const handleReload = (): void => {
    reload();
    createMessage.success('数据已刷新');
  };
</script>
