<template>
  <div class="p-4">
    <a-card :bordered="false" style="height: 100%">
      <a-tabs v-model:activeKey="activeKey" animated>
        <!-- 水能耗 -->
        <a-tab-pane key="water" tab="水能耗">
          <a-tabs v-model:activeKey="waterActiveKey" type="card">
            <a-tab-pane key="realtime" tab="实时数据">
              <WaterRealtimeChart />
            </a-tab-pane>
            <a-tab-pane key="history" tab="历史数据">
              <WaterHistoryChart />
            </a-tab-pane>
            <a-tab-pane key="yesterday" tab="昨日用水">
              <WaterYesterdayChart />
            </a-tab-pane>
          </a-tabs>
        </a-tab-pane>

        <!-- 电能耗 -->
        <a-tab-pane key="electricity" tab="电能耗">
          <a-tabs v-model:activeKey="electricityActiveKey" type="card">
            <a-tab-pane key="realtime" tab="实时数据">
              <ElectricityRealtimeChart />
            </a-tab-pane>
            <a-tab-pane key="history" tab="历史数据">
              <ElectricityHistoryChart />
            </a-tab-pane>
          </a-tabs>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import WaterRealtimeChart from './components/WaterRealtimeChart.vue';
  import WaterHistoryChart from './components/WaterHistoryChart.vue';
  import WaterYesterdayChart from './components/WaterYesterdayChart.vue';
  import ElectricityRealtimeChart from './components/ElectricityRealtimeChart.vue';
  import ElectricityHistoryChart from './components/ElectricityHistoryChart.vue';

  const activeKey = ref('water');
  const waterActiveKey = ref('realtime');
  const electricityActiveKey = ref('realtime');
</script>

<style scoped>
  .ant-empty {
    margin: 50px 0;
  }
</style>
