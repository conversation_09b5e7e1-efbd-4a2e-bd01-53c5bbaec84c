<template>
  <a-modal v-model:visible="modalVisible" title="自定义AI模型" :width="500" :mask-closable="false" @ok="handleOk">
    <a-form :model="formState" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }">
      <a-form-item label="模型名称" name="name" :rules="[{ required: true, message: '请输入模型名称!' }]">
        <a-input v-model:value="formState.name" placeholder="请输入模型名称" />
      </a-form-item>
      <a-form-item label="API地址" name="API_URL" :rules="[{ required: true, message: '请输入API地址!' }]">
        <a-input v-model:value="formState.API_URL" placeholder="请输入API地址，例如：https://api.openai.com/v1/chat/completions" />
      </a-form-item>
      <a-form-item label="API密钥" name="API_KEY" :rules="[{ required: true, message: '请输入API密钥!' }]">
        <a-input v-model:value="formState.API_KEY" placeholder="请输入API密钥" />
      </a-form-item>
      <a-form-item label="模型名称" name="MODEL" :rules="[{ required: true, message: '请输入模型标识!' }]">
        <a-input v-model:value="formState.MODEL" placeholder="请输入模型标识，例如：gpt-4" />
      </a-form-item>
      <a-form-item label="模型图标">
        <a-radio-group v-model:value="formState.logo">
          <a-radio value="🔧">🔧</a-radio>
          <a-radio value="🤖">🤖</a-radio>
          <a-radio value="🧠">🧠</a-radio>
          <a-radio value="⚡">⚡</a-radio>
          <a-radio value="🚀">🚀</a-radio>
          <a-radio value="💡">💡</a-radio>
          <a-radio value="🔮">🔮</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="系统提示">
        <a-textarea v-model:value="formState.system_prompt" placeholder="可选的系统提示语" :auto-size="{ minRows: 3, maxRows: 5 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { saveCustomModel, getCustomModel } from '/@/enums/httpEnum';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:visible', 'save']);

  // 创建本地响应式变量来控制模态框显示
  const modalVisible = ref(false);

  // 监听props.visible的变化来更新本地状态
  watch(
    () => props.visible,
    (val) => {
      modalVisible.value = val;
    },
    { immediate: true }
  );

  // 监听本地状态变化来更新父组件
  watch(
    () => modalVisible.value,
    (val) => {
      emit('update:visible', val);
    }
  );

  const formState = reactive({
    name: '自定义模型',
    API_URL: '',
    API_KEY: '',
    MODEL: '',
    logo: '🔧',
    system_prompt: '',
  });

  onMounted(() => {
    const customModel = getCustomModel();
    if (customModel) {
      Object.assign(formState, customModel);
    }
  });

  const handleOk = () => {
    if (!formState.name || !formState.API_URL || !formState.API_KEY || !formState.MODEL) {
      message.error('请填写必填项！');
      return;
    }

    // 保存自定义模型配置
    const modelConfig = {
      name: formState.name,
      API_URL: formState.API_URL,
      API_KEY: formState.API_KEY,
      MODEL: formState.MODEL,
      logo: formState.logo,
      options: {
        system_prompt: formState.system_prompt,
      },
    };

    saveCustomModel(modelConfig);
    message.success('保存成功！');
    emit('update:visible', false);
    emit('save', 'CUSTOM');
  };
</script>

<style lang="less" scoped>
  // 样式可以根据需要调整
</style>
