<template>
  <div class="custom-select relative" :style="style">
    <button ref="selectRef" :class="selectClasses" :disabled="disabled" @click="toggleDropdown" @blur="handleBlur">
      <span class="flex-1 text-left">{{ selectedLabel }}</span>
      <DownOutlined :class="arrowClasses" />
    </button>

    <!-- 下拉选项 -->
    <Teleport to="body">
      <div v-if="isOpen" ref="dropdownRef" :class="dropdownClasses" :style="dropdownStyle">
        <div v-for="option in options" :key="option.value" :class="optionClasses(option)" @click="selectOption(option)">
          {{ option.label }}
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue';
  import { DownOutlined } from '@ant-design/icons-vue';

  // 选项接口
  interface SelectOption {
    value: any;
    label: string;
  }

  // Props
  interface Props {
    value?: any;
    options: SelectOption[];
    disabled?: boolean;
    placeholder?: string;
    style?: Record<string, any>;
  }

  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    placeholder: '请选择',
  });

  // Emits
  const emit = defineEmits<{
    'update:value': [value: any];
    change: [value: any];
  }>();

  // 响应式数据
  const isOpen = ref(false);
  const dropdownRef = ref<HTMLElement>();
  const selectRef = ref<HTMLElement>();
  const dropdownStyle = ref<Record<string, any>>({});

  // 计算属性
  const selectedOption = computed(() => {
    return props.options.find((option) => option.value === props.value);
  });

  const selectedLabel = computed(() => {
    return selectedOption.value?.label || props.placeholder;
  });

  const selectClasses = computed(() => {
    const baseClasses = [
      'w-full',
      'px-3',
      'py-1.5',
      'text-xs',
      'min-h-[28px]',
      'bg-[#2a3441]',
      'border',
      'border-[#3a4551]',
      'rounded',
      'text-gray-300',
      'flex',
      'items-center',
      'justify-between',
      'transition-all',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-[#3B8EE6]/50',
    ];

    if (props.disabled) {
      baseClasses.push('bg-gray-600', 'border-gray-600', 'text-gray-400', 'cursor-not-allowed');
    } else {
      baseClasses.push('hover:bg-[#3a4551]', 'hover:border-[#4a5561]', 'hover:text-white', 'cursor-pointer');
    }

    if (isOpen.value) {
      baseClasses.push('border-[#3B8EE6]', 'bg-[#3a4551]');
    }

    return baseClasses;
  });

  const arrowClasses = computed(() => {
    const classes = ['text-xs', 'transition-transform', 'duration-200'];
    if (isOpen.value) {
      classes.push('rotate-180');
    }
    return classes;
  });

  const dropdownClasses = computed(() => [
    'absolute',
    'z-50',
    'min-w-[100px]',
    'bg-[#2a3441]',
    'border',
    'border-[#3a4551]',
    'rounded',
    'shadow-lg',
    'py-1',
    'max-h-60',
    'overflow-y-auto',
    'custom-scrollbar',
  ]);

  const optionClasses = (option: SelectOption) => {
    const classes = ['px-3', 'py-2', 'text-xs', 'text-gray-300', 'cursor-pointer', 'transition-colors', 'duration-150'];

    if (option.value === props.value) {
      classes.push('bg-[#3B8EE6]', 'text-white');
    } else {
      classes.push('hover:bg-[#3a4551]', 'hover:text-white');
    }

    return classes;
  };

  // 方法
  const toggleDropdown = () => {
    if (props.disabled) return;

    if (isOpen.value) {
      closeDropdown();
    } else {
      openDropdown();
    }
  };

  const openDropdown = async () => {
    isOpen.value = true;
    await nextTick();
    updateDropdownPosition();
  };

  const closeDropdown = () => {
    isOpen.value = false;
  };

  const updateDropdownPosition = () => {
    if (!selectRef.value || !dropdownRef.value) return;

    const selectRect = selectRef.value.getBoundingClientRect();
    const dropdownHeight = dropdownRef.value.offsetHeight;
    const viewportHeight = window.innerHeight;

    let top = selectRect.bottom + 4;

    // 如果下方空间不够，显示在上方
    if (top + dropdownHeight > viewportHeight && selectRect.top > dropdownHeight) {
      top = selectRect.top - dropdownHeight - 4;
    }

    dropdownStyle.value = {
      position: 'fixed',
      top: `${top}px`,
      left: `${selectRect.left}px`,
      width: `${selectRect.width}px`,
    };
  };

  const selectOption = (option: SelectOption) => {
    emit('update:value', option.value);
    emit('change', option.value);
    closeDropdown();
  };

  const handleBlur = (event: FocusEvent) => {
    // 延迟关闭，允许点击选项
    setTimeout(() => {
      if (!dropdownRef.value?.contains(event.relatedTarget as Node)) {
        closeDropdown();
      }
    }, 150);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      selectRef.value &&
      !selectRef.value.contains(event.target as Node) &&
      dropdownRef.value &&
      !dropdownRef.value.contains(event.target as Node)
    ) {
      closeDropdown();
    }
  };

  // 生命周期
  onMounted(() => {
    document.addEventListener('click', handleClickOutside);
    window.addEventListener('resize', updateDropdownPosition);
    window.addEventListener('scroll', updateDropdownPosition);
  });

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
    window.removeEventListener('resize', updateDropdownPosition);
    window.removeEventListener('scroll', updateDropdownPosition);
  });
</script>

<style scoped>
  .custom-select {
    position: relative;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(59, 142, 230, 0.6);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 142, 230, 0.8);
  }
</style>
