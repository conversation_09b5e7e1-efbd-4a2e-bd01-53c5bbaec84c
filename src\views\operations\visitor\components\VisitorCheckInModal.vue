<template>
  <a-modal v-model:visible="visible" title="访客签到" width="500px" @ok="handleSubmit" @cancel="handleCancel">
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-form-item label="访客信息" name="visitorId">
        <a-select v-model:value="formData.visitorId" placeholder="请选择访客" show-search :filter-option="filterOption" @change="handleVisitorChange">
          <a-select-option v-for="visitor in approvedVisitors" :key="visitor.id" :value="visitor.id">
            {{ visitor.name }} - {{ visitor.phone }} ({{ visitor.company }})
          </a-select-option>
        </a-select>
      </a-form-item>

      <div v-if="selectedVisitor" class="mb-4 p-4 bg-gray-50 rounded">
        <h4 class="mb-2">访客详情</h4>
        <div class="grid grid-cols-2 gap-2 text-sm">
          <div><strong>姓名:</strong> {{ selectedVisitor.name }}</div>
          <div><strong>电话:</strong> {{ selectedVisitor.phone }}</div>
          <div><strong>公司:</strong> {{ selectedVisitor.company }}</div>
          <div><strong>被访人:</strong> {{ selectedVisitor.visitee }}</div>
          <div><strong>访问目的:</strong> {{ selectedVisitor.visitPurpose }}</div>
          <div><strong>预约时间:</strong> {{ selectedVisitor.visitTime }}</div>
        </div>
      </div>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="体温检测" name="temperature">
            <a-input-number
              v-model:value="formData.temperature"
              :min="35"
              :max="42"
              :step="0.1"
              :precision="1"
              placeholder="体温"
              style="width: 100%"
            >
              <template #addonAfter>°C</template>
            </a-input-number>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="健康码状态" name="healthCode">
            <a-select v-model:value="formData.healthCode" placeholder="健康码状态">
              <a-select-option value="green">
                <span class="text-green-500">绿码</span>
              </a-select-option>
              <a-select-option value="yellow">
                <span class="text-yellow-500">黄码</span>
              </a-select-option>
              <a-select-option value="red">
                <span class="text-red-500">红码</span>
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="签到照片">
        <a-upload
          v-model:file-list="fileList"
          name="photo"
          list-type="picture-card"
          :show-upload-list="false"
          :before-upload="beforeUpload"
          @change="handlePhotoChange"
        >
          <div v-if="formData.checkInPhoto">
            <img :src="formData.checkInPhoto" alt="签到照片" style="width: 100%; height: 100%; object-fit: cover" />
          </div>
          <div v-else>
            <CameraOutlined />
            <div style="margin-top: 8px">拍照签到</div>
          </div>
        </a-upload>
      </a-form-item>

      <a-form-item label="备注">
        <a-textarea v-model:value="formData.remark" placeholder="签到备注信息" :rows="3" />
      </a-form-item>

      <!-- 健康检查提醒 -->
      <a-alert v-if="formData.temperature > 37.3 || formData.healthCode !== 'green'" type="warning" show-icon class="mb-4">
        <template #message>
          <div>
            <div v-if="formData.temperature > 37.3">体温异常，建议进一步检查</div>
            <div v-if="formData.healthCode !== 'green'">健康码异常，请确认健康状况</div>
          </div>
        </template>
      </a-alert>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed, watch } from 'vue';
  import { Modal } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
  import { CameraOutlined } from '@ant-design/icons-vue';
  import { getVisitorList, visitorCheckIn, type VisitorInfo } from '/@/api/operations/visitor';

  // Props
  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    'update:visible': [value: boolean];
    success: [];
  }>();

  // 响应式数据
  const formRef = ref();
  const fileList = ref([]);
  const loading = ref(false);
  const approvedVisitors = ref<VisitorInfo[]>([]);

  const formData = reactive({
    visitorId: null as number | null,
    temperature: 36.5,
    healthCode: 'green',
    checkInPhoto: '',
    remark: '',
  });

  // 表单验证规则
  const rules = {
    visitorId: [{ required: true, message: '请选择访客', trigger: 'change' }],
    temperature: [
      { required: true, message: '请输入体温', trigger: 'blur' },
      { type: 'number', min: 35, max: 42, message: '体温范围应在35-42°C之间', trigger: 'blur' },
    ],
    healthCode: [{ required: true, message: '请选择健康码状态', trigger: 'change' }],
  };

  // 计算属性
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const selectedVisitor = computed(() => {
    return approvedVisitors.value.find((v) => v.id === formData.visitorId);
  });

  // 方法
  const loadApprovedVisitors = async () => {
    try {
      const response = await getVisitorList({
        status: 'approved',
        pageSize: 100, // 获取所有已审批的访客
      });
      approvedVisitors.value = response.records;
    } catch (error) {
      message.error('获取访客列表失败');
    }
  };

  const filterOption = (input: string, option: any) => {
    const visitor = approvedVisitors.value.find((v) => v.id === option.value);
    if (!visitor) return false;

    const searchText = input.toLowerCase();
    return (
      visitor.name.toLowerCase().includes(searchText) || visitor.phone.includes(searchText) || visitor.company.toLowerCase().includes(searchText)
    );
  };

  const handleVisitorChange = (visitorId: number) => {
    const visitor = approvedVisitors.value.find((v) => v.id === visitorId);
    if (visitor) {
      // 可以根据访客信息预填一些数据
      console.log('选择的访客:', visitor);
    }
  };

  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传 JPG/PNG 格式的图片!');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB!');
      return false;
    }
    return false; // 阻止自动上传
  };

  const handlePhotoChange = (info: any) => {
    if (info.file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        formData.checkInPhoto = e.target?.result as string;
      };
      reader.readAsDataURL(info.file);
    }
  };

  const resetForm = () => {
    Object.assign(formData, {
      visitorId: null,
      temperature: 36.5,
      healthCode: 'green',
      checkInPhoto: '',
      remark: '',
    });
    fileList.value = [];
    formRef.value?.resetFields();
  };

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();

      // 健康检查警告
      if (formData.temperature > 37.3 || formData.healthCode !== 'green') {
        const confirmed = await new Promise((resolve) => {
          Modal.confirm({
            title: '健康状况异常',
            content: '访客健康状况异常，是否确认签到？',
            onOk: () => resolve(true),
            onCancel: () => resolve(false),
          });
        });

        if (!confirmed) {
          return;
        }
      }

      loading.value = true;

      await visitorCheckIn(formData.visitorId!, formData.temperature, formData.healthCode);

      message.success('访客签到成功');
      emit('success');
      resetForm();
    } catch (error) {
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('签到失败，请重试');
    } finally {
      loading.value = false;
    }
  };

  const handleCancel = () => {
    resetForm();
    visible.value = false;
  };

  // 生命周期
  onMounted(() => {
    loadApprovedVisitors();
  });

  // 监听弹窗打开，刷新数据
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        loadApprovedVisitors();
      } else {
        resetForm();
      }
    }
  );
</script>

<style scoped>
  :deep(.ant-upload-select-picture-card) {
    width: 100px;
    height: 100px;
  }
</style>
