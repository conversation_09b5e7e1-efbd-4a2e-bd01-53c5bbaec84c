<template>
  <div class="h-full flex flex-col">
    <!-- 操作工具栏 -->
    <div class="flex justify-between items-center mb-[1vw]">
      <div class="flex items-center space-x-[0.8vw]">
        <button
          class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
          @click="showTransferDialog = true"
        >
          <SwapOutlined class="mr-[0.2vw]" />
          新建调拨
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
          @click="batchTransfer"
          :disabled="selectedTransfers.length === 0"
        >
          <CheckOutlined class="mr-[0.2vw]" />
          批量确认
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors"
          @click="exportTransfers"
        >
          <DownloadOutlined class="mr-[0.2vw]" />
          导出记录
        </button>
      </div>

      <div class="flex items-center space-x-[0.6vw]">
        <select v-model="statusFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部状态</option>
          <option value="pending">待确认</option>
          <option value="in_transit">调拨中</option>
          <option value="completed">已完成</option>
          <option value="cancelled">已取消</option>
        </select>
        <select v-model="typeFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部类型</option>
          <option value="department">部门调拨</option>
          <option value="location">位置调拨</option>
          <option value="person">人员调拨</option>
        </select>
        <input
          v-model="searchQuery"
          placeholder="搜索调拨单号、资产名称..."
          class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none w-[15vw]"
        />
        <button
          class="px-[0.6vw] py-[0.3vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors"
          @click="searchTransfers"
        >
          <SearchOutlined />
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-black/20 rounded border border-white/10 overflow-hidden">
      <div class="overflow-auto h-full">
        <table class="w-full text-[0.6vw]">
          <thead class="bg-blue-500/20 sticky top-0">
            <tr>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">
                <input type="checkbox" @change="toggleSelectAll" class="mr-[0.4vw]" />
                调拨单号
              </th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">资产名称</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">调拨类型</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">调出方</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">调入方</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">申请时间</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">状态</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="transfer in paginatedTransfers" :key="transfer.id" class="hover:bg-white/5 transition-colors">
              <td class="p-[0.6vw] text-white border-b border-white/5">
                <input
                  type="checkbox"
                  :checked="selectedTransfers.includes(transfer.id)"
                  @change="toggleSelectTransfer(transfer.id)"
                  class="mr-[0.4vw]"
                />
                {{ transfer.transferCode }}
              </td>
              <td class="p-[0.6vw] text-white border-b border-white/5">{{ transfer.assetName }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ getTypeText(transfer.type) }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ transfer.fromLocation }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ transfer.toLocation }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ transfer.requestDate }}</td>
              <td class="p-[0.6vw] border-b border-white/5">
                <span
                  :class="[
                    'px-[0.4vw] py-[0.1vw] rounded text-[0.5vw]',
                    transfer.status === 'pending'
                      ? 'bg-yellow-500/20 text-yellow-400'
                      : transfer.status === 'in_transit'
                        ? 'bg-blue-500/20 text-blue-400'
                        : transfer.status === 'completed'
                          ? 'bg-green-500/20 text-green-400'
                          : 'bg-red-500/20 text-red-400',
                  ]"
                >
                  {{ getStatusText(transfer.status) }}
                </span>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex space-x-[0.4vw]">
                  <button
                    v-if="transfer.status === 'pending'"
                    class="text-green-400 hover:text-green-300 text-[0.5vw] bg-transparent"
                    @click="confirmTransfer(transfer)"
                  >
                    确认
                  </button>
                  <button
                    v-if="transfer.status === 'in_transit'"
                    class="text-blue-400 hover:text-blue-300 text-[0.5vw] bg-transparent"
                    @click="completeTransfer(transfer)"
                  >
                    完成
                  </button>
                  <button class="text-orange-400 hover:text-orange-300 text-[0.5vw] bg-transparent" @click="viewTransferDetail(transfer)">
                    详情
                  </button>
                  <button
                    v-if="transfer.status === 'pending'"
                    class="text-red-400 hover:text-red-300 text-[0.5vw] bg-transparent"
                    @click="cancelTransfer(transfer)"
                  >
                    取消
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-between items-center mt-[0.8vw] text-[0.6vw] text-gray-400">
      <div>共 {{ filteredTransfers.length }} 条记录</div>
      <div class="flex items-center space-x-[0.4vw]">
        <button
          :disabled="currentPage === 1"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="text-white">{{ currentPage }} / {{ totalPages }}</span>
        <button
          :disabled="currentPage === totalPages"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage++"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 新建调拨弹窗 -->
    <ModalDialog
      v-model:visible="showTransferDialog"
      title="新建资产调拨"
      width="60vw"
      :show-footer="true"
      @confirm="confirmNewTransfer"
      @cancel="showTransferDialog = false"
    >
      <AssetTransferForm v-model:form-data="transferForm" />
    </ModalDialog>

    <!-- 调拨详情弹窗 -->
    <ModalDialog v-model:visible="showDetailDialog" title="调拨详情" width="70vw" :show-footer="false" @cancel="showDetailDialog = false">
      <TransferDetailView :transfer="selectedTransfer" />
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { SwapOutlined, CheckOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import AssetTransferForm from './forms/AssetTransferForm.vue';
  import TransferDetailView from './forms/TransferDetailView.vue';

  // 响应式数据
  const transfers = ref([]);
  const selectedTransfers = ref([]);
  const selectedTransfer = ref(null);
  const searchQuery = ref('');
  const statusFilter = ref('');
  const typeFilter = ref('');
  const currentPage = ref(1);
  const pageSize = ref(20);
  const showTransferDialog = ref(false);
  const showDetailDialog = ref(false);
  const transferForm = ref({});

  // 计算属性
  const filteredTransfers = computed(() => {
    let result = transfers.value;

    if (statusFilter.value) {
      result = result.filter((transfer) => transfer.status === statusFilter.value);
    }

    if (typeFilter.value) {
      result = result.filter((transfer) => transfer.type === typeFilter.value);
    }

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(
        (transfer) =>
          transfer.transferCode.toLowerCase().includes(query) ||
          transfer.assetName.toLowerCase().includes(query) ||
          transfer.fromLocation.toLowerCase().includes(query) ||
          transfer.toLocation.toLowerCase().includes(query)
      );
    }

    return result;
  });

  const totalPages = computed(() => Math.ceil(filteredTransfers.value.length / pageSize.value));

  const paginatedTransfers = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return filteredTransfers.value.slice(start, end);
  });

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      pending: '待确认',
      in_transit: '调拨中',
      completed: '已完成',
      cancelled: '已取消',
    };
    return statusMap[status] || status;
  };

  const getTypeText = (type) => {
    const typeMap = {
      department: '部门调拨',
      location: '位置调拨',
      person: '人员调拨',
    };
    return typeMap[type] || type;
  };

  const toggleSelectAll = (event) => {
    if (event.target.checked) {
      selectedTransfers.value = paginatedTransfers.value.map((transfer) => transfer.id);
    } else {
      selectedTransfers.value = [];
    }
  };

  const toggleSelectTransfer = (transferId) => {
    const index = selectedTransfers.value.indexOf(transferId);
    if (index > -1) {
      selectedTransfers.value.splice(index, 1);
    } else {
      selectedTransfers.value.push(transferId);
    }
  };

  const searchTransfers = () => {
    currentPage.value = 1;
  };

  const confirmTransfer = (transfer) => {
    transfer.status = 'in_transit';
    transfer.confirmDate = new Date().toISOString().split('T')[0];
  };

  const completeTransfer = (transfer) => {
    transfer.status = 'completed';
    transfer.completeDate = new Date().toISOString().split('T')[0];
  };

  const cancelTransfer = (transfer) => {
    transfer.status = 'cancelled';
    transfer.cancelDate = new Date().toISOString().split('T')[0];
  };

  const viewTransferDetail = (transfer) => {
    selectedTransfer.value = transfer;
    showDetailDialog.value = true;
  };

  const batchTransfer = () => {
    if (selectedTransfers.value.length === 0) {
      alert('请选择要批量确认的调拨记录');
      return;
    }

    const pendingTransfers = transfers.value.filter((transfer) => selectedTransfers.value.includes(transfer.id) && transfer.status === 'pending');

    if (pendingTransfers.length === 0) {
      alert('所选记录中没有待确认的调拨');
      return;
    }

    pendingTransfers.forEach((transfer) => {
      transfer.status = 'in_transit';
      transfer.confirmDate = new Date().toISOString().split('T')[0];
    });

    selectedTransfers.value = [];
    alert(`批量确认成功！共确认 ${pendingTransfers.length} 条调拨记录`);
  };

  const exportTransfers = () => {
    const exportData = filteredTransfers.value.map((transfer) => ({
      调拨单号: transfer.transferCode,
      资产名称: transfer.assetName,
      调拨类型: getTypeText(transfer.type),
      调出方: transfer.fromLocation,
      调入方: transfer.toLocation,
      申请时间: transfer.requestDate,
      状态: getStatusText(transfer.status),
    }));

    const headers = Object.keys(exportData[0] || {});
    const csvContent = [headers.join(','), ...exportData.map((row) => headers.map((header) => `"${row[header] || ''}"`).join(','))].join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `资产调拨数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert('调拨数据导出成功！');
  };

  const confirmNewTransfer = () => {
    // 表单验证
    if (!transferForm.value.assetName || !transferForm.value.fromLocation || !transferForm.value.toLocation || !transferForm.value.reason) {
      alert('请填写必填字段：资产名称、调出方、调入方、调拨原因');
      return;
    }

    const newTransfer = {
      id: Date.now(),
      transferCode: `TRF-${new Date().getFullYear()}-${String(transfers.value.length + 1).padStart(3, '0')}`,
      assetName: transferForm.value.assetName,
      type: transferForm.value.type || 'location',
      fromLocation: transferForm.value.fromLocation,
      toLocation: transferForm.value.toLocation,
      requestDate: new Date().toISOString().split('T')[0],
      status: 'pending',
      requester: transferForm.value.requester || '当前用户',
      reason: transferForm.value.reason,
    };

    transfers.value.unshift(newTransfer);
    transferForm.value = {};
    showTransferDialog.value = false;

    alert('调拨申请提交成功！');
  };

  // 初始化数据
  onMounted(() => {
    transfers.value = [
      {
        id: 1,
        transferCode: 'TRF-2024-001',
        assetName: 'Dell PowerEdge R740服务器',
        type: 'department',
        fromLocation: 'IT部门-机房A',
        toLocation: '运营部-机房B',
        requestDate: '2024-01-15',
        status: 'pending',
        requester: '张三',
        reason: '部门重组需要',
      },
      {
        id: 2,
        transferCode: 'TRF-2024-002',
        assetName: 'HP ProLiant DL380服务器',
        type: 'location',
        fromLocation: '1楼机房',
        toLocation: '2楼机房',
        requestDate: '2024-01-16',
        status: 'in_transit',
        requester: '李四',
        reason: '机房迁移',
      },
    ];
  });
</script>
