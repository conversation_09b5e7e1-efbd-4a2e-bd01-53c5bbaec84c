import * as THREE from 'three';
import { SceneManager } from '../SceneManager';

/**
 * 路径点接口
 */
export interface PathNode {
  position: THREE.Vector3;
  isValid: boolean; // 是否是有效路径点
  isDetour: boolean; // 是否是绕行点
}

/**
 * 墙壁检测器
 * 用于检测路径是否穿墙，并提供绕行路径计算
 */
export class WallDetector {
  private static instance: WallDetector | null = null;

  // 基础组件
  private scene: THREE.Scene;
  private raycaster: THREE.Raycaster;

  // 墙壁和地板对象缓存
  private wallObjects: THREE.Object3D[] = [];
  private floorObjects: THREE.Object3D[] = [];
  private allObjects: THREE.Object3D[] = [];
  private cacheValid: boolean = false;

  // 射线检测结果缓存
  private raycastCache: Map<string, boolean> = new Map();
  private maxCacheSize: number = 1000; // 最大缓存条目数

  // 路径缓存
  private pathCache: Map<string, THREE.Vector3[]> = new Map();
  private maxPathCacheSize: number = 50; // 最大路径缓存条目数

  // 配置
  private config = {
    maxDetourPoints: 3, // 最大绕行点数量（减少）
    detourStepSize: 1.0, // 绕行步长（米）- 增大步长减少计算量
    maxDetourDistance: 10.0, // 最大绕行距离 - 减少最大距离
    wallDetectionPrecision: 0.5, // 墙壁检测精度（射线间隔，米）- 降低精度提高性能
    maxPathfindingIterations: 50, // 最大寻路迭代次数 - 减少迭代次数
    gridResolution: 0.8, // 网格分辨率（米）- 降低网格精度提高性能
    wallLayerMask: ['Wall', 'wall', 'WALL'], // 墙壁层标识 - 只保留最关键的标识
    floorLayerMask: ['Floor', 'floor'], // 地板层标识 - 只保留最关键的标识
    obstacleDetectionThreshold: 0.2, // 障碍物检测阈值（米）- 提高阈值减少检测
    multiRayDetection: false, // 禁用多射线检测以提高性能
    rayCount: 1, // 只使用单射线检测
    useSimplifiedAlgorithm: true, // 使用简化算法
    enableCaching: true, // 启用缓存
    throttleDetection: true, // 启用检测节流
    throttleTime: 100, // 节流时间（毫秒）
    skipFloorValidation: true, // 跳过地板验证以提高性能
    maxObstacleCount: 100, // 最大障碍物数量限制
  };

  /**
   * 获取单例实例
   */
  public static getInstance(): WallDetector {
    if (!WallDetector.instance) {
      WallDetector.instance = new WallDetector();
    }
    return WallDetector.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    // 获取必要组件
    this.scene = SceneManager.getInstance().scene;
    this.raycaster = new THREE.Raycaster();

    // 初始化对象缓存
    this.updateObjectCache();
  }

  /**
   * 更新对象缓存
   * 将场景中的墙壁和地板对象分类缓存，提高检测性能
   */
  public updateObjectCache(): void {
    this.wallObjects = [];
    this.floorObjects = [];
    this.allObjects = [];

    // 清除射线检测缓存和路径缓存
    this.raycastCache.clear();
    this.pathCache.clear();

    // 计数器，用于限制处理的对象数量
    let wallCount = 0;
    let floorCount = 0;

    // 遍历场景中的所有对象
    this.scene.traverse((object) => {
      if (!object.visible || !(object instanceof THREE.Mesh)) return;

      // 限制处理的对象数量，避免过度计算
      if (wallCount >= this.config.maxObstacleCount && floorCount > 5) return;

      // 检查对象名称，分类为墙壁或地板
      const name = object.name.toLowerCase();

      // 快速检查 - 直接检查是否包含关键字，避免使用some方法
      const isWall = name.includes('wall');
      const isFloor = name.includes('floor');

      if (isWall && wallCount < this.config.maxObstacleCount) {
        this.wallObjects.push(object);
        this.allObjects.push(object);
        wallCount++;
        // 减少日志输出，提高性能
        if (wallCount <= 5) {
          console.log(`[WallDetector] 识别到墙壁对象: ${object.name}`);
        }
      } else if (isFloor && floorCount < 10) {
        // 限制地板对象数量
        this.floorObjects.push(object);
        this.allObjects.push(object);
        floorCount++;
      }
    });

    this.cacheValid = true;
    console.log(`[WallDetector] 对象缓存已更新: 墙壁 ${this.wallObjects.length} 个, 地板 ${this.floorObjects.length} 个`);
  }

  /**
   * 检查对象是否有足够大的体积被视为障碍物
   * @param object 要检查的对象
   * @returns 是否是有效障碍物
   */
  private _hasSignificantVolume(object: THREE.Mesh): boolean {
    try {
      // 确保几何体存在
      if (!object.geometry) return false;

      // 计算包围盒
      object.geometry.computeBoundingBox();
      const boundingBox = object.geometry.boundingBox;
      if (!boundingBox) return false;

      // 获取对象的世界变换
      object.updateMatrixWorld(true);
      const worldMatrix = object.matrixWorld;

      // 创建一个世界空间的包围盒
      const worldBoundingBox = boundingBox.clone().applyMatrix4(worldMatrix);

      // 计算尺寸
      const size = new THREE.Vector3();
      worldBoundingBox.getSize(size);

      // 计算体积
      const volume = size.x * size.y * size.z;

      // 检查高度和体积
      const minHeight = 0.5; // 最小高度（米）
      const minVolume = 0.1; // 最小体积（立方米）

      return size.y > minHeight && volume > minVolume;
    } catch (error) {
      console.error('[WallDetector] 检查对象体积时出错:', error);
      return false;
    }
  }

  /**
   * 检测两点之间是否有墙壁
   * @param start 起点
   * @param end 终点
   * @returns 是否有墙壁阻挡
   */
  public hasWallBetween(start: THREE.Vector3, end: THREE.Vector3): boolean {
    if (!this.cacheValid) {
      this.updateObjectCache();
    }

    // 如果墙壁对象为空，返回false
    if (this.wallObjects.length === 0) {
      return false;
    }

    // 计算距离
    const distance = start.distanceTo(end);

    // 如果距离太短，直接返回false（避免误判）
    if (distance < 0.2) {
      return false;
    }

    // 检查缓存
    if (this.config.enableCaching) {
      // 创建缓存键（四舍五入到一位小数以增加缓存命中率）
      const cacheKey = `${Math.round(start.x * 10) / 10},${Math.round(start.y * 10) / 10},${Math.round(start.z * 10) / 10}_${Math.round(end.x * 10) / 10},${Math.round(end.y * 10) / 10},${Math.round(end.z * 10) / 10}`;

      // 检查缓存中是否有结果
      if (this.raycastCache.has(cacheKey)) {
        return this.raycastCache.get(cacheKey) as boolean;
      }

      // 如果缓存过大，清除一半
      if (this.raycastCache.size > this.maxCacheSize) {
        // 清除一半的缓存
        const keys = Array.from(this.raycastCache.keys());
        for (let i = 0; i < keys.length / 2; i++) {
          this.raycastCache.delete(keys[i]);
        }
      }
    }

    // 计算主方向
    const mainDirection = new THREE.Vector3().subVectors(end, start).normalize();

    // 执行射线检测
    this.raycaster.set(start, mainDirection);
    const intersects = this.raycaster.intersectObjects(this.wallObjects, true);
    const result = intersects.length > 0 && intersects[0].distance < distance;

    // 缓存结果
    if (this.config.enableCaching) {
      const cacheKey = `${Math.round(start.x * 10) / 10},${Math.round(start.y * 10) / 10},${Math.round(start.z * 10) / 10}_${Math.round(end.x * 10) / 10},${Math.round(end.y * 10) / 10},${Math.round(end.z * 10) / 10}`;
      this.raycastCache.set(cacheKey, result);
    }

    return result;
  }

  /**
   * 多射线检测 - 从不同角度发射多条射线以提高检测准确性
   * @param start 起点
   * @param end 终点
   * @param mainDirection 主方向
   * @param distance 距离
   * @returns 是否有墙壁阻挡
   */
  private _multiRayDetection(start: THREE.Vector3, end: THREE.Vector3, mainDirection: THREE.Vector3, distance: number): boolean {
    // 主射线检测
    this.raycaster.set(start, mainDirection);
    let intersects = this.raycaster.intersectObjects(this.wallObjects, true);

    // 如果主射线检测到墙壁，返回true
    if (intersects.length > 0 && intersects[0].distance < distance) {
      console.log(`[WallDetector] 主射线检测到墙壁: ${intersects[0].object.name}, 距离: ${intersects[0].distance.toFixed(2)}米`);
      return true;
    }

    // 如果距离太短，不进行额外射线检测
    if (distance < 1.0) {
      return false;
    }

    // 计算垂直于主方向的向量（用于生成偏移射线）
    const up = new THREE.Vector3(0, 1, 0);
    const right = new THREE.Vector3().crossVectors(mainDirection, up).normalize();
    if (right.length() < 0.1) {
      // 如果主方向接近垂直，使用另一个向量
      right.set(1, 0, 0).crossVectors(mainDirection, right).normalize();
    }

    // 计算上方向
    const trueUp = new THREE.Vector3().crossVectors(right, mainDirection).normalize();

    // 偏移距离（米）
    const offset = 0.2;

    // 生成额外的射线方向
    const rayDirections: THREE.Vector3[] = [];

    // 添加水平偏移射线
    rayDirections.push(
      new THREE.Vector3().copy(mainDirection).addScaledVector(right, offset).normalize(),
      new THREE.Vector3().copy(mainDirection).addScaledVector(right, -offset).normalize()
    );

    // 添加垂直偏移射线
    rayDirections.push(
      new THREE.Vector3().copy(mainDirection).addScaledVector(trueUp, offset).normalize(),
      new THREE.Vector3().copy(mainDirection).addScaledVector(trueUp, -offset).normalize()
    );

    // 检查每个额外射线
    for (let i = 0; i < rayDirections.length; i++) {
      this.raycaster.set(start, rayDirections[i]);
      intersects = this.raycaster.intersectObjects(this.wallObjects, true);

      if (intersects.length > 0 && intersects[0].distance < distance) {
        console.log(`[WallDetector] 辅助射线 ${i + 1} 检测到墙壁: ${intersects[0].object.name}, 距离: ${intersects[0].distance.toFixed(2)}米`);
        return true;
      }
    }

    // 所有射线都没有检测到墙壁
    return false;
  }

  /**
   * 计算绕行路径
   * @param start 起点
   * @param end 终点
   * @returns 绕行路径点数组，如果无法找到有效路径则返回空数组
   */
  public findDetourPath(start: THREE.Vector3, end: THREE.Vector3): PathNode[] {
    // 如果两点之间没有墙壁，直接返回终点
    if (!this.hasWallBetween(start, end)) {
      return [{ position: end, isValid: true, isDetour: false }];
    }

    // 检查路径缓存
    if (this.config.enableCaching) {
      const cacheKey = `${Math.round(start.x * 10) / 10},${Math.round(start.y * 10) / 10},${Math.round(start.z * 10) / 10}_${Math.round(end.x * 10) / 10},${Math.round(end.y * 10) / 10},${Math.round(end.z * 10) / 10}`;

      if (this.pathCache.has(cacheKey)) {
        const cachedPath = this.pathCache.get(cacheKey);
        if (cachedPath && cachedPath.length > 0) {
          return cachedPath.map((pos) => ({
            position: pos.clone(),
            isValid: true,
            isDetour: true,
          }));
        }
      }

      // 如果缓存过大，清除一半
      if (this.pathCache.size > this.maxPathCacheSize) {
        const keys = Array.from(this.pathCache.keys());
        for (let i = 0; i < keys.length / 2; i++) {
          this.pathCache.delete(keys[i]);
        }
      }
    }

    // 使用简化算法或A*算法
    let path: THREE.Vector3[];

    if (this.config.useSimplifiedAlgorithm) {
      // 使用简化的绕行算法（更快但精度较低）
      path = this._findSimpleDetourPath(start, end);
    } else {
      // 使用A*算法（更精确但更慢）
      path = this.findPathAStar(start, end);
    }

    // 如果找到路径，缓存并返回
    if (path.length > 0) {
      // 缓存路径
      if (this.config.enableCaching) {
        const cacheKey = `${Math.round(start.x * 10) / 10},${Math.round(start.y * 10) / 10},${Math.round(start.z * 10) / 10}_${Math.round(end.x * 10) / 10},${Math.round(end.y * 10) / 10},${Math.round(end.z * 10) / 10}`;
        this.pathCache.set(
          cacheKey,
          path.map((p) => p.clone())
        );
      }

      return path.map((pos) => ({
        position: pos,
        isValid: true,
        isDetour: true,
      }));
    }

    // 如果无法找到路径，返回无效的终点
    return [{ position: end, isValid: false, isDetour: false }];
  }

  /**
   * 简化的绕行路径查找算法
   * 使用简单的偏移点尝试绕过障碍物
   * @param start 起点
   * @param end 终点
   * @returns 路径点数组
   */
  private _findSimpleDetourPath(start: THREE.Vector3, end: THREE.Vector3): THREE.Vector3[] {
    // 计算方向和距离
    const direction = new THREE.Vector3().subVectors(end, start).normalize();
    const distance = start.distanceTo(end);

    // 计算垂直于方向的向量
    const up = new THREE.Vector3(0, 1, 0);
    const right = new THREE.Vector3().crossVectors(direction, up).normalize();

    // 尝试的偏移距离（米）
    const offsets = [2, 4, 6]; // 从小到大尝试不同的偏移

    // 尝试不同的偏移点
    for (const offset of offsets) {
      // 尝试右侧偏移
      const rightOffset = new THREE.Vector3().copy(start).addScaledVector(right, offset);

      // 检查到右侧偏移点是否有障碍物
      if (!this.hasWallBetween(start, rightOffset)) {
        // 从右侧偏移点到终点
        if (!this.hasWallBetween(rightOffset, end)) {
          // 找到有效路径
          return [rightOffset, end];
        }
      }

      // 尝试左侧偏移
      const leftOffset = new THREE.Vector3().copy(start).addScaledVector(right, -offset);

      // 检查到左侧偏移点是否有障碍物
      if (!this.hasWallBetween(start, leftOffset)) {
        // 从左侧偏移点到终点
        if (!this.hasWallBetween(leftOffset, end)) {
          // 找到有效路径
          return [leftOffset, end];
        }
      }

      // 尝试两段式路径（先向一侧偏移，然后平行移动，最后回到目标）
      for (const sideOffset of [offset, -offset]) {
        // 侧向偏移点
        const sidePoint = new THREE.Vector3().copy(start).addScaledVector(right, sideOffset);

        // 如果到侧向点有障碍物，跳过
        if (this.hasWallBetween(start, sidePoint)) continue;

        // 计算前向偏移点（在侧向点的基础上向前移动）
        const forwardPoint = new THREE.Vector3().copy(sidePoint).addScaledVector(direction, distance * 0.8);

        // 如果到前向点有障碍物，跳过
        if (this.hasWallBetween(sidePoint, forwardPoint)) continue;

        // 如果从前向点到终点没有障碍物，返回路径
        if (!this.hasWallBetween(forwardPoint, end)) {
          return [sidePoint, forwardPoint, end];
        }
      }
    }

    // 如果简单方法失败，返回空数组
    return [];
  }

  /**
   * 使用A*算法寻找路径
   * @param start 起点
   * @param end 终点
   * @returns 路径点数组
   */
  private findPathAStar(start: THREE.Vector3, end: THREE.Vector3): THREE.Vector3[] {
    // A*算法实现

    // 创建网格
    const gridSize = this.config.gridResolution;
    const maxIterations = this.config.maxPathfindingIterations;

    // 节点结构
    interface AStarNode {
      position: THREE.Vector3;
      g: number; // 从起点到当前点的成本
      h: number; // 从当前点到终点的估计成本（启发式）
      f: number; // g + h
      parent: AStarNode | null;
    }

    // 计算启发式距离（曼哈顿距离）
    const heuristic = (a: THREE.Vector3, b: THREE.Vector3): number => {
      return Math.abs(a.x - b.x) + Math.abs(a.z - b.z);
    };

    // 创建起始节点
    const startNode: AStarNode = {
      position: start.clone(),
      g: 0,
      h: heuristic(start, end),
      f: heuristic(start, end),
      parent: null,
    };

    // 创建开放列表和关闭列表
    const openList: AStarNode[] = [startNode];
    const closedList: AStarNode[] = [];

    // 检查节点是否在列表中
    const isNodeInList = (pos: THREE.Vector3, list: AStarNode[]): AStarNode | null => {
      for (const node of list) {
        if (pos.distanceTo(node.position) < gridSize / 2) {
          return node;
        }
      }
      return null;
    };

    // 迭代寻找路径
    let iterations = 0;
    while (openList.length > 0 && iterations < maxIterations) {
      iterations++;

      // 按f值排序并获取最小的节点
      openList.sort((a, b) => a.f - b.f);
      const currentNode = openList.shift()!;

      // 如果到达终点附近，构建并返回路径
      if (currentNode.position.distanceTo(end) < gridSize * 2) {
        // 构建路径
        const path: THREE.Vector3[] = [];
        let current: AStarNode | null = currentNode;

        while (current) {
          path.unshift(current.position.clone());
          current = current.parent;
        }

        // 添加终点
        if (path[path.length - 1].distanceTo(end) > gridSize / 2) {
          path.push(end.clone());
        }

        // 简化路径（移除共线点）
        return this.simplifyPath(path);
      }

      // 添加到关闭列表
      closedList.push(currentNode);

      // 生成周围的点
      const neighbors = this.generateNeighbors(currentNode.position, gridSize);

      for (const neighborPos of neighbors) {
        // 检查是否已经在关闭列表中
        if (isNodeInList(neighborPos, closedList)) {
          continue;
        }

        // 检查是否有墙壁阻挡
        if (this.hasWallBetween(currentNode.position, neighborPos)) {
          continue;
        }

        // 计算从起点到邻居的成本
        const gScore = currentNode.g + currentNode.position.distanceTo(neighborPos);

        // 检查是否已经在开放列表中
        const existingNode = isNodeInList(neighborPos, openList);

        if (!existingNode) {
          // 创建新节点并添加到开放列表
          const newNode: AStarNode = {
            position: neighborPos,
            g: gScore,
            h: heuristic(neighborPos, end),
            f: gScore + heuristic(neighborPos, end),
            parent: currentNode,
          };
          openList.push(newNode);
        } else if (gScore < existingNode.g) {
          // 更新现有节点
          existingNode.g = gScore;
          existingNode.f = gScore + existingNode.h;
          existingNode.parent = currentNode;
        }
      }
    }

    // 如果无法找到路径，尝试直接连接
    console.log(`[WallDetector] A*算法未找到路径，迭代次数: ${iterations}`);

    // 如果无法找到路径，返回空数组
    return [];
  }

  /**
   * 简化路径，移除共线点
   * @param path 原始路径
   * @returns 简化后的路径
   */
  private simplifyPath(path: THREE.Vector3[]): THREE.Vector3[] {
    if (path.length <= 2) return path;

    const result: THREE.Vector3[] = [path[0]];

    for (let i = 1; i < path.length - 1; i++) {
      const prev = path[i - 1];
      const current = path[i];
      const next = path[i + 1];

      // 计算方向向量
      const dir1 = new THREE.Vector3().subVectors(current, prev).normalize();
      const dir2 = new THREE.Vector3().subVectors(next, current).normalize();

      // 如果方向变化较大，保留该点
      if (dir1.dot(dir2) < 0.95) {
        result.push(current);
      }
    }

    // 添加终点
    result.push(path[path.length - 1]);

    return result;
  }

  /**
   * 生成周围的点
   * @param center 中心点
   * @param gridSize 网格大小
   * @returns 周围的点数组
   */
  private generateNeighbors(center: THREE.Vector3, gridSize: number): THREE.Vector3[] {
    const neighbors: THREE.Vector3[] = [];

    // 生成16个方向的点，提供更多可能的移动方向
    // 首先生成8个基本方向
    for (let x = -1; x <= 1; x++) {
      for (let z = -1; z <= 1; z++) {
        if (x === 0 && z === 0) continue;

        const neighbor = new THREE.Vector3(center.x + x * gridSize, center.y, center.z + z * gridSize);

        neighbors.push(neighbor);
      }
    }

    // 添加8个额外的方向（更远的点，提供更多绕行选择）
    const extendedDirections = [
      { x: -2, z: 0 },
      { x: 2, z: 0 }, // 左右
      { x: 0, z: -2 },
      { x: 0, z: 2 }, // 前后
      { x: -1, z: -2 },
      { x: 1, z: -2 }, // 前左前右
      { x: -1, z: 2 },
      { x: 1, z: 2 }, // 后左后右
    ];

    for (const dir of extendedDirections) {
      const neighbor = new THREE.Vector3(center.x + dir.x * gridSize, center.y, center.z + dir.z * gridSize);

      neighbors.push(neighbor);
    }

    // 检查每个邻居点是否在地板上
    return this._filterValidNeighbors(center, neighbors);
  }

  /**
   * 过滤有效的邻居点（确保点在地板上）
   * @param center 中心点
   * @param neighbors 邻居点数组
   * @returns 有效的邻居点数组
   */
  private _filterValidNeighbors(center: THREE.Vector3, neighbors: THREE.Vector3[]): THREE.Vector3[] {
    // 如果没有地板对象，返回所有邻居点
    if (this.floorObjects.length === 0) {
      return neighbors;
    }

    const validNeighbors: THREE.Vector3[] = [];

    for (const neighbor of neighbors) {
      // 创建一个向下的射线，检查点是否在地板上
      const rayStart = new THREE.Vector3(neighbor.x, neighbor.y + 1, neighbor.z);
      this.raycaster.set(rayStart, new THREE.Vector3(0, -1, 0));

      const intersects = this.raycaster.intersectObjects(this.floorObjects, true);

      // 如果射线与地板相交，将点添加到有效邻居点数组
      if (intersects.length > 0 && intersects[0].distance < 2) {
        // 使用地板上的实际高度
        const floorY = intersects[0].point.y;
        neighbor.y = floorY + 0.1; // 稍微抬高一点，避免穿透地板

        validNeighbors.push(neighbor);
      }
    }

    // 如果没有有效邻居点，返回原始邻居点（避免卡死）
    return validNeighbors.length > 0 ? validNeighbors : neighbors;
  }

  /**
   * 检查点是否在列表中
   * @param point 点
   * @param list 列表
   * @returns 是否在列表中
   */
  private isPointInList(point: THREE.Vector3, list: THREE.Vector3[]): boolean {
    const threshold = this.config.gridResolution / 2;
    return list.some((p) => point.distanceTo(p) < threshold);
  }
}
