<template>
  <div class="relative">
    <!-- 漫游工具栏 -->
    <RoamingToolbar ref="roamingToolbarRef" />

    <!-- 漫游主按钮 -->
    <a-tooltip placement="right" :title="getRoamingTooltip">
      <div
        class="control-btn"
        :class="{
          active: isRoamingActive || isToolbarExpanded,
          disabled:
            isProcessing ||
            globalThreeStore.isFloorSwitching ||
            globalThreeStore.isPatrolActive ||
            globalThreeStore.isPlayActive ||
            globalThreeStore.transparencyMode,
        }"
        @click="handleRoamingButtonClick"
        data-view-control="roaming"
      >
        <div class="flex flex-col items-center justify-center h-full">
          <LoadingOutlined v-if="isProcessing" class="text-[0.8vw]" />
          <template v-else>
            <component :is="getRoamingIcon" class="text-[0.8vw]" />
            <span class="text-[0.5vw] text-white/80 mt-[0.1vw]">{{ getRoamingText }}</span>
          </template>
        </div>
      </div>
    </a-tooltip>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onBeforeUnmount, onMounted } from 'vue';
  import { LoadingOutlined, CompassOutlined } from '@ant-design/icons-vue';
  import { useGlobalThreeStore } from '../store/globalThreeStore';
  import RoamingToolbar from './RoamingToolbar.vue';
  import { RoamingController } from '../lib/roaming/RoamingController';
  import { RenderingPipeline } from '../lib/RenderingPipeline';
  import { SceneManager } from '../lib/SceneManager';

  // 状态定义
  const isProcessing = ref(false);
  const isRoamingActive = ref(false); // 跟踪漫游是否处于活动状态
  const isToolbarExpanded = ref(false); // 工具栏是否展开
  const globalThreeStore = useGlobalThreeStore();
  const roamingToolbarRef = ref<InstanceType<typeof RoamingToolbar> | null>(null);

  // 计算属性
  const getRoamingIcon = computed(() => {
    return CompassOutlined;
  });

  const getRoamingText = computed(() => {
    return '漫游';
  });

  const getRoamingTooltip = computed(() => {
    if (globalThreeStore.isFloorSwitching) {
      return '楼层切换中，请等待切换完成后再使用漫游功能';
    }
    if (globalThreeStore.isPatrolActive) {
      return '巡检功能正在运行中，请先停止巡检再使用漫游功能';
    }
    if (globalThreeStore.isPlayActive) {
      return '播放功能正在运行中，请先停止播放再使用漫游功能';
    }
    if (globalThreeStore.transparencyMode) {
      return '透视模式已启用，请先关闭透视模式再使用漫游功能';
    }
    if (isRoamingActive.value) {
      return '漫游进行中，请使用退出按钮结束漫游';
    }
    return '进入漫游模式';
  });

  // 显示消息的安全方法
  const showMessage = (text: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
    console.log(`[RoamingButton] ${text}`);
    if (window.$message) {
      window.$message[type](text);
    }
  };

  // 处理漫游按钮点击
  const handleRoamingButtonClick = async () => {
    // 检查是否可以执行操作
    if (
      isProcessing.value ||
      globalThreeStore.isFloorSwitching ||
      globalThreeStore.isPatrolActive ||
      globalThreeStore.isPlayActive ||
      globalThreeStore.transparencyMode
    ) {
      if (globalThreeStore.isFloorSwitching) {
        showMessage('楼层切换中，请等待切换完成后再使用漫游功能', 'warning');
        return;
      }
      if (globalThreeStore.isPatrolActive) {
        showMessage('巡检进行中，请先停止巡检再使用漫游功能', 'warning');
        return;
      }
      if (globalThreeStore.isPlayActive) {
        showMessage('播放功能正在运行中，请先停止播放再使用漫游功能', 'warning');
        return;
      }
      if (globalThreeStore.transparencyMode) {
        showMessage('透视模式已启用，请先关闭透视模式再使用漫游功能', 'warning');
        return;
      }
      return;
    }

    if (!globalThreeStore.canUserInteract) {
      showMessage('系统正在加载中，请稍候再试...', 'warning');
      return;
    }

    // 切换工具栏显示状态
    toggleToolbar();
  };

  // 切换工具栏显示状态
  const toggleToolbar = () => {
    if (roamingToolbarRef.value) {
      if (isToolbarExpanded.value) {
        // 收起工具栏时，自动停止所有漫游相关功能
        roamingToolbarRef.value.hideToolbar();
        isToolbarExpanded.value = false;
        console.log('[RoamingButton] 工具栏已收起，漫游相关功能已自动停止');
      } else {
        // 展开工具栏时，允许使用漫游功能
        roamingToolbarRef.value.showToolbar();
        isToolbarExpanded.value = true;
        console.log('[RoamingButton] 工具栏已展开，可以使用漫游功能');
      }
    }
  };

  // 监听漫游状态变化
  const handleRoamingStarted = () => {
    isRoamingActive.value = true;
    globalThreeStore.setRoamingActive(true);

    // 优化：为漫游模式设置渲染和场景优化
    try {
      const renderingPipeline = RenderingPipeline.getInstance();
      if (renderingPipeline) {
        renderingPipeline.setRoamingMode(true);
      }

      const sceneManager = SceneManager.getInstance();
      if (sceneManager) {
        sceneManager.setRoamingMode(true);
      }
    } catch (error) {
      console.error('[RoamingButton] 设置漫游模式优化失败:', error);
    }
  };

  const handleRoamingStopped = () => {
    isRoamingActive.value = false;
    globalThreeStore.setRoamingActive(false);

    // 优化：恢复默认渲染和场景设置
    try {
      const renderingPipeline = RenderingPipeline.getInstance();
      if (renderingPipeline) {
        renderingPipeline.setRoamingMode(false);
      }

      const sceneManager = SceneManager.getInstance();
      if (sceneManager) {
        sceneManager.setRoamingMode(false);
      }
    } catch (error) {
      console.error('[RoamingButton] 恢复默认渲染设置失败:', error);
    }
  };

  // 组件挂载时添加事件监听
  onMounted(() => {
    window.addEventListener('roaming-started', handleRoamingStarted);
    window.addEventListener('roaming-stopped', handleRoamingStopped);
  });

  // 组件卸载前移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('roaming-started', handleRoamingStarted);
    window.removeEventListener('roaming-stopped', handleRoamingStopped);
  });

  // 导出方法供父组件调用
  defineExpose({
    toggleToolbar,
  });
</script>

<style scoped>
  .control-btn {
    @apply w-[2.2vw] h-[2.2vw] bg-[rgba(23,43,77,0.8)] border border-[rgba(36,108,249,0.3)] rounded-[0.3vw] flex flex-col items-center justify-center cursor-pointer text-white transition-all duration-300 relative overflow-hidden;
    @apply hover:(bg-[rgba(36,108,249,0.2)] border-[rgba(36,108,249,0.5)] scale-105);
    @apply before:content-empty before:absolute before:left-[-100%] before:top-[-50%] before:w-[200%] before:h-[200%] before:bg-gradient-to-r before:from-transparent before:via-[rgba(36,108,249,0.3)] before:to-transparent before:rotate-45 hover:before:animate-scan;
  }

  .control-btn.active {
    @apply bg-[rgba(36,108,249,0.3)] border-[rgba(36,108,249,0.8)] shadow-[0_0_10px_rgba(36,108,249,0.3)];
  }

  .control-btn.disabled {
    @apply opacity-50 cursor-not-allowed;
  }
</style>
