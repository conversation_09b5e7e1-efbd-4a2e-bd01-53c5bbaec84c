<template>
  <div class="relative group">
    <div
      class="w-[2vw] h-[2vw] rounded-full bg-[#246CF9] hover:bg-[#246CF9]/80 flex items-center justify-center cursor-pointer transition-colors"
      :class="{ 'opacity-50 cursor-not-allowed': disabled }"
      @click="!disabled && $emit('click')"
    >
      <component :is="icon" class="text-white text-[1vw]" />
    </div>

    <div
      v-if="tooltip"
      class="absolute left-1/2 -translate-x-1/2 bottom-[calc(-100%-0.5vw)] opacity-0 group-hover:opacity-100 transition-opacity bg-black/80 text-white text-[0.7vw] whitespace-nowrap px-[0.6vw] py-[0.3vw] rounded pointer-events-none"
    >
      {{ tooltip }}
    </div>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    icon: any; // 支持任何类型的图标组件
    tooltip?: string;
    disabled?: boolean;
  }

  defineProps<Props>();

  defineEmits<{
    (e: 'click'): void;
  }>();
</script>
