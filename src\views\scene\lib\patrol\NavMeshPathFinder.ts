import * as THREE from 'three';
import { SceneManager } from '../SceneManager';

/**
 * 导航网格路径查找器
 * 使用预计算的导航网格进行高效的路径规划，避免路径穿透墙壁
 */
export class NavMeshPathFinder {
  private static instance: NavMeshPathFinder | null = null;

  // 场景引用
  private scene: THREE.Scene;

  // 导航网格相关
  private navMesh: THREE.Mesh | null = null;
  private navMeshGeometry: THREE.BufferGeometry | null = null;
  private wallObjects: THREE.Object3D[] = [];
  private floorObjects: THREE.Object3D[] = [];

  // 路径查找相关
  private pathNodes: THREE.Vector3[] = [];
  private pathNodeMarkers: THREE.Mesh[] = [];
  private pathLines: THREE.Line | null = null;

  // 调试可视化
  private debugEnabled: boolean = false;
  private debugNavMesh: THREE.Mesh | null = null;

  // 配置
  private config = {
    wallLayerMask: ['Wall', 'wall', 'WALL'],
    floorLayerMask: ['Floor', 'floor', 'FLOOR'],
    nodeHeight: 0.1, // 路径点高于地面的高度
    pathColor: 0x60a5fa, // 路径颜色
    invalidPathColor: 0xef4444, // 无效路径颜色
    nodeSize: 0.2, // 路径节点大小
    maxPathfindingIterations: 100, // 最大寻路迭代次数
    gridResolution: 0.5, // 网格分辨率（米）
  };

  /**
   * 获取单例实例
   */
  public static getInstance(): NavMeshPathFinder {
    if (!NavMeshPathFinder.instance) {
      NavMeshPathFinder.instance = new NavMeshPathFinder();
    }
    return NavMeshPathFinder.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    this.scene = SceneManager.getInstance().scene;
    this.initialize();
  }

  /**
   * 初始化路径查找器
   */
  private initialize(): void {
    // 收集场景中的墙体和地板对象
    this.collectSceneObjects();

    // 生成导航网格
    this.generateNavMesh();
  }

  /**
   * 收集场景中的墙体和地板对象
   */
  private collectSceneObjects(): void {
    this.wallObjects = [];
    this.floorObjects = [];

    this.scene.traverse((object) => {
      if (object.visible && object instanceof THREE.Mesh) {
        const name = object.name.toLowerCase();

        // 检查是否是墙壁对象
        const isWall = this.config.wallLayerMask.some((mask) => name.includes(mask.toLowerCase()));

        // 检查是否是地板对象
        const isFloor = this.config.floorLayerMask.some((mask) => name.includes(mask.toLowerCase()));

        if (isWall) {
          this.wallObjects.push(object);
          console.log(`[NavMeshPathFinder] 识别到墙壁对象: ${object.name}`);
        } else if (isFloor) {
          this.floorObjects.push(object);
        }
      }
    });

    console.log(`[NavMeshPathFinder] 对象收集完成: 墙壁 ${this.wallObjects.length} 个, 地板 ${this.floorObjects.length} 个`);
  }

  /**
   * 生成导航网格
   * 这是一个简化版的导航网格生成，实际项目中应使用Recast.js等专业库
   */
  private generateNavMesh(): void {
    if (this.floorObjects.length === 0) {
      console.warn('[NavMeshPathFinder] 没有找到地板对象，无法生成导航网格');
      return;
    }

    // 创建一个合并的几何体作为导航网格的基础
    const navMeshGeometry = new THREE.BufferGeometry();
    const positions: number[] = [];
    const indices: number[] = [];

    // 从地板对象创建导航网格
    let indexOffset = 0;

    this.floorObjects.forEach((floor) => {
      if (floor instanceof THREE.Mesh) {
        // 获取地板的几何体
        const geometry = floor.geometry.clone();

        // 应用地板的变换
        geometry.applyMatrix4(floor.matrixWorld);

        // 获取顶点和索引
        const positionAttribute = geometry.getAttribute('position');
        const indexAttribute = geometry.getIndex();

        if (positionAttribute && indexAttribute) {
          // 添加顶点
          for (let i = 0; i < positionAttribute.count; i++) {
            positions.push(positionAttribute.getX(i), positionAttribute.getY(i), positionAttribute.getZ(i));
          }

          // 添加索引
          for (let i = 0; i < indexAttribute.count; i++) {
            indices.push(indexAttribute.getX(i) + indexOffset);
          }

          indexOffset += positionAttribute.count;
        }
      }
    });

    // 设置导航网格几何体
    navMeshGeometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
    navMeshGeometry.setIndex(indices);
    navMeshGeometry.computeVertexNormals();

    // 创建导航网格
    const navMeshMaterial = new THREE.MeshBasicMaterial({
      color: 0x3498db,
      transparent: true,
      opacity: 0.2,
      side: THREE.DoubleSide,
      wireframe: false,
    });

    this.navMeshGeometry = navMeshGeometry;
    this.navMesh = new THREE.Mesh(navMeshGeometry, navMeshMaterial);

    // 如果启用了调试，添加导航网格到场景中
    if (this.debugEnabled) {
      this.debugNavMesh = this.navMesh.clone();
      this.scene.add(this.debugNavMesh);
    }

    console.log('[NavMeshPathFinder] 导航网格生成完成');
  }

  /**
   * 查找从起点到终点的路径
   * @param start 起点
   * @param end 终点
   * @returns 路径点数组，如果无法找到路径则返回空数组
   */
  public findPath(start: THREE.Vector3, end: THREE.Vector3): THREE.Vector3[] {
    // 清除之前的路径
    this.clearPath();

    // 检查起点和终点是否在导航网格上
    const startOnNavMesh = this.isPointOnNavMesh(start);
    const endOnNavMesh = this.isPointOnNavMesh(end);

    if (!startOnNavMesh || !endOnNavMesh) {
      console.warn('[NavMeshPathFinder] 起点或终点不在导航网格上');
      return [];
    }

    // 检查直线路径是否有障碍物
    if (!this.hasObstacleBetween(start, end)) {
      // 如果没有障碍物，直接返回起点和终点
      this.pathNodes = [start.clone(), end.clone()];
      this.visualizePath();
      return this.pathNodes;
    }

    // 使用A*算法查找路径
    const path = this.findPathAStar(start, end);

    if (path.length > 0) {
      this.pathNodes = path;
      this.visualizePath();
      return path;
    }

    console.warn('[NavMeshPathFinder] 无法找到有效路径');
    return [];
  }

  /**
   * 检查点是否在导航网格上
   * @param point 要检查的点
   * @returns 是否在导航网格上
   */
  private isPointOnNavMesh(point: THREE.Vector3): boolean {
    // 简化版：检查点是否在任何地板对象上方
    const raycaster = new THREE.Raycaster();
    raycaster.set(new THREE.Vector3(point.x, point.y + 1, point.z), new THREE.Vector3(0, -1, 0));

    const intersects = raycaster.intersectObjects(this.floorObjects, true);
    return intersects.length > 0;
  }

  /**
   * 检查两点之间是否有障碍物
   * @param start 起点
   * @param end 终点
   * @returns 是否有障碍物
   */
  public hasObstacleBetween(start: THREE.Vector3, end: THREE.Vector3): boolean {
    const direction = new THREE.Vector3().subVectors(end, start).normalize();
    const distance = start.distanceTo(end);

    const raycaster = new THREE.Raycaster();
    raycaster.set(start, direction);

    const intersects = raycaster.intersectObjects(this.wallObjects, true);
    return intersects.length > 0 && intersects[0].distance < distance;
  }

  /**
   * 使用A*算法查找路径
   * @param start 起点
   * @param end 终点
   * @returns 路径点数组
   */
  private findPathAStar(start: THREE.Vector3, end: THREE.Vector3): THREE.Vector3[] {
    // 这里是一个简化版的A*算法实现
    // 在实际项目中，应该使用Recast.js等专业库的路径查找功能

    // 创建网格
    const gridSize = this.config.gridResolution;
    const maxIterations = this.config.maxPathfindingIterations;

    // 创建开放列表和关闭列表
    const openList: THREE.Vector3[] = [start.clone()];
    const closedList: THREE.Vector3[] = [];

    // 迭代寻找路径
    let iterations = 0;
    while (openList.length > 0 && iterations < maxIterations) {
      iterations++;

      // 获取当前点（简化版，实际A*应该选择f值最小的点）
      const current = openList.shift()!;
      closedList.push(current);

      // 如果到达终点附近，返回路径
      if (current.distanceTo(end) < gridSize * 2) {
        return [...closedList, end.clone()];
      }

      // 生成周围的点
      const neighbors = this.generateNeighbors(current, gridSize);

      for (const neighbor of neighbors) {
        // 检查是否已经在关闭列表中
        if (this.isPointInList(neighbor, closedList)) {
          continue;
        }

        // 检查是否有墙壁阻挡
        if (this.hasObstacleBetween(current, neighbor)) {
          continue;
        }

        // 检查是否已经在开放列表中
        if (!this.isPointInList(neighbor, openList)) {
          openList.push(neighbor);
        }
      }
    }

    // 如果无法找到路径，返回空数组
    return [];
  }

  /**
   * 生成周围的点
   * @param center 中心点
   * @param gridSize 网格大小
   * @returns 周围的点数组
   */
  private generateNeighbors(center: THREE.Vector3, gridSize: number): THREE.Vector3[] {
    const neighbors: THREE.Vector3[] = [];

    // 生成8个方向的点
    for (let x = -1; x <= 1; x++) {
      for (let z = -1; z <= 1; z++) {
        if (x === 0 && z === 0) continue;

        const neighbor = new THREE.Vector3(center.x + x * gridSize, center.y, center.z + z * gridSize);

        // 检查点是否在导航网格上
        if (this.isPointOnNavMesh(neighbor)) {
          neighbors.push(neighbor);
        }
      }
    }

    return neighbors;
  }

  /**
   * 检查点是否在列表中
   * @param point 点
   * @param list 列表
   * @returns 是否在列表中
   */
  private isPointInList(point: THREE.Vector3, list: THREE.Vector3[]): boolean {
    const threshold = this.config.gridResolution / 2;
    return list.some((p) => point.distanceTo(p) < threshold);
  }

  /**
   * 可视化路径
   */
  private visualizePath(): void {
    if (this.pathNodes.length === 0) return;

    // 创建路径线
    const geometry = new THREE.BufferGeometry().setFromPoints(this.pathNodes);
    const material = new THREE.LineBasicMaterial({ color: this.config.pathColor });
    this.pathLines = new THREE.Line(geometry, material);
    this.scene.add(this.pathLines);

    // 创建路径点标记
    const nodeGeometry = new THREE.SphereGeometry(this.config.nodeSize, 8, 8);
    const nodeMaterial = new THREE.MeshBasicMaterial({ color: this.config.pathColor });

    this.pathNodes.forEach((node) => {
      const marker = new THREE.Mesh(nodeGeometry, nodeMaterial);
      marker.position.copy(node);
      this.scene.add(marker);
      this.pathNodeMarkers.push(marker);
    });

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 清除路径
   */
  public clearPath(): void {
    // 移除路径线
    if (this.pathLines) {
      this.scene.remove(this.pathLines);
      this.pathLines.geometry.dispose();
      (this.pathLines.material as THREE.Material).dispose();
      this.pathLines = null;
    }

    // 移除路径点标记
    this.pathNodeMarkers.forEach((marker) => {
      this.scene.remove(marker);
      marker.geometry.dispose();
      (marker.material as THREE.Material).dispose();
    });
    this.pathNodeMarkers = [];

    // 清空路径点
    this.pathNodes = [];

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 启用/禁用调试模式
   * @param enabled 是否启用
   */
  public setDebugEnabled(enabled: boolean): void {
    this.debugEnabled = enabled;

    if (enabled && this.navMesh && !this.debugNavMesh) {
      this.debugNavMesh = this.navMesh.clone();
      this.scene.add(this.debugNavMesh);
    } else if (!enabled && this.debugNavMesh) {
      this.scene.remove(this.debugNavMesh);
      this.debugNavMesh = null;
    }

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }
}
