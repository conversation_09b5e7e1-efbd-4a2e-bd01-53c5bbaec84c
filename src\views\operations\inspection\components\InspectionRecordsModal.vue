<template>
  <a-modal v-model:visible="visible" title="巡检记录" width="1000px" :footer="null">
    <a-table :columns="columns" :data-source="records" :loading="loading" :pagination="pagination" @change="handleTableChange" size="small">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'result'">
          <a-tag :color="getResultColor(record.result)">
            {{ getResultText(record.result) }}
          </a-tag>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue';
  import { getInspectionRecords, type InspectionRecord } from '/@/api/operations/inspection';

  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{ 'update:visible': [value: boolean] }>();

  const loading = ref(false);
  const records = ref<InspectionRecord[]>([]);

  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const columns = [
    { title: '检查点', dataIndex: 'checkPointName', key: 'checkPointName' },
    { title: '检查项', dataIndex: 'checkItemName', key: 'checkItemName' },
    { title: '巡检员', dataIndex: 'inspector', key: 'inspector' },
    { title: '检查结果', key: 'result' },
    { title: '实际值', dataIndex: 'actualValue', key: 'actualValue' },
    { title: '检查时间', dataIndex: 'checkTime', key: 'checkTime' },
    { title: '位置', dataIndex: 'location', key: 'location' },
  ];

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const loadRecords = async () => {
    loading.value = true;
    try {
      const response = await getInspectionRecords({
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
      });
      records.value = response.records;
      pagination.total = response.total;
    } catch (error) {
      console.error('获取巡检记录失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleTableChange = (pag: any) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    loadRecords();
  };

  const getResultColor = (result: string) => {
    const colors = { pass: 'green', fail: 'red', warning: 'orange' };
    return colors[result] || 'gray';
  };

  const getResultText = (result: string) => {
    const texts = { pass: '通过', fail: '不通过', warning: '警告' };
    return texts[result] || result;
  };

  onMounted(() => {
    if (props.visible) {
      loadRecords();
    }
  });

  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        loadRecords();
      }
    }
  );
</script>
