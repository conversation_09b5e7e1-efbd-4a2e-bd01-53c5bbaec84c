<template>
  <div class="p-[1vw] space-y-[1vw]">
    <div class="grid grid-cols-2 gap-[1vw]">
      <!-- 基本信息 -->
      <div class="space-y-[0.8vw]">
        <h3 class="text-[0.8vw] text-white font-semibold mb-[0.6vw]">基本信息</h3>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">资产名称 *</label>
          <select
            v-model="formData.assetName"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="">请选择要报废的资产</option>
            <option v-for="asset in availableAssets" :key="asset.id" :value="asset.name"> {{ asset.name }} ({{ asset.code }}) </option>
          </select>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">资产编号</label>
          <input
            v-model="formData.assetCode"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="自动生成或手动输入"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">申请人</label>
          <input
            v-model="formData.applicant"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入申请人姓名"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">申请部门</label>
          <select
            v-model="formData.department"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="">请选择部门</option>
            <option value="IT">IT部门</option>
            <option value="HR">人力资源部</option>
            <option value="Finance">财务部</option>
            <option value="Operations">运营部</option>
          </select>
        </div>
      </div>

      <!-- 报废信息 -->
      <div class="space-y-[0.8vw]">
        <h3 class="text-[0.8vw] text-white font-semibold mb-[0.6vw]">报废信息</h3>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">报废原因 *</label>
          <select
            v-model="formData.reason"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="">请选择报废原因</option>
            <option value="damaged">设备损坏</option>
            <option value="obsolete">技术淘汰</option>
            <option value="expired">超期使用</option>
            <option value="upgrade">设备升级</option>
          </select>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">预计报废时间</label>
          <input
            v-model="formData.plannedDate"
            type="date"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">资产价值评估</label>
          <input
            v-model.number="formData.estimatedValue"
            type="number"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入当前估值"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">处置方式</label>
          <select
            v-model="formData.disposalMethod"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="recycle">回收处理</option>
            <option value="sell">对外销售</option>
            <option value="donate">捐赠</option>
            <option value="destroy">销毁处理</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 详细说明 -->
    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">报废说明 *</label>
      <textarea
        v-model="formData.description"
        rows="4"
        class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400 resize-none"
        placeholder="请详细说明报废原因、设备状况等"
      ></textarea>
    </div>

    <!-- 审批流程 -->
    <div>
      <h3 class="text-[0.8vw] text-white font-semibold mb-[0.6vw]">审批流程</h3>
      <div class="bg-black/20 rounded p-[0.8vw]">
        <div class="flex items-center space-x-[1vw]">
          <div class="flex items-center space-x-[0.4vw]">
            <div class="w-[0.8vw] h-[0.8vw] bg-blue-500 rounded-full"></div>
            <span class="text-[0.6vw] text-white">部门主管审批</span>
          </div>
          <div class="flex-1 h-[0.1vw] bg-gray-600"></div>
          <div class="flex items-center space-x-[0.4vw]">
            <div class="w-[0.8vw] h-[0.8vw] bg-gray-600 rounded-full"></div>
            <span class="text-[0.6vw] text-gray-400">资产管理审批</span>
          </div>
          <div class="flex-1 h-[0.1vw] bg-gray-600"></div>
          <div class="flex items-center space-x-[0.4vw]">
            <div class="w-[0.8vw] h-[0.8vw] bg-gray-600 rounded-full"></div>
            <span class="text-[0.6vw] text-gray-400">执行报废</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 附件上传 -->
    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">相关附件</label>
      <div class="border-2 border-dashed border-white/20 rounded p-[1vw] text-center">
        <div class="text-[0.6vw] text-gray-400 mb-[0.4vw]"> 上传相关文件（如损坏照片、评估报告等） </div>
        <div class="text-[0.5vw] text-gray-500"> 支持 PDF、DOC、XLS、JPG、PNG 格式 </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    formData: { type: Object, default: () => ({}) },
  });

  const emit = defineEmits(['update:formData']);

  const formData = ref({
    assetName: '',
    assetCode: '',
    applicant: '',
    department: '',
    reason: '',
    plannedDate: new Date().toISOString().split('T')[0],
    estimatedValue: null,
    disposalMethod: 'recycle',
    description: '',
    attachments: [],
    ...props.formData,
  });

  // 模拟可报废资产数据
  const availableAssets = ref([
    { id: 1, code: 'IT-2020-001', name: '旧服务器设备' },
    { id: 2, code: 'IT-2019-005', name: '老式打印机' },
    { id: 3, code: 'IT-2018-012', name: '淘汰网络设备' },
    { id: 4, code: 'IT-2017-008', name: '过期UPS设备' },
    { id: 5, code: 'IT-2019-020', name: '损坏存储设备' },
  ]);

  watch(
    formData,
    (newValue) => {
      emit('update:formData', newValue);
    },
    { deep: true }
  );
</script>
