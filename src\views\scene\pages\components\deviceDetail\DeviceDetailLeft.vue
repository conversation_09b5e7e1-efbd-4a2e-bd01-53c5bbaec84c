<template>
  <div class="w-full h-full p-[0.8vw] flex flex-col gap-[0.8vw]">
    <!-- 设备基本信息 -->
    <div class="h-[calc((100%-1.6vw)/3)] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">设备基本信息</div>
        <div
          class="absolute right-[1vw] top-[0.25vw] text-[0.6vw] text-blue-400 cursor-pointer hover:text-blue-300 transition-colors"
          @click="closeDeviceDetail"
        >
          返回
        </div>
      </div>
      <div class="flex-1 mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw] overflow-auto custom-scrollbar">
        <div v-if="loading" class="flex justify-center items-center h-full">
          <div class="text-white text-[0.7vw]">加载中...</div>
        </div>
        <div v-else-if="error" class="flex justify-center items-center h-full">
          <div class="text-red-400 text-[0.7vw]">{{ error }}</div>
        </div>
        <div v-else-if="!deviceData || deviceData.length === 0" class="flex justify-center items-center h-full">
          <div class="text-yellow-400 text-[0.7vw]">未找到设备数据</div>
        </div>
        <div v-else-if="deviceData && deviceData.length > 0" class="flex flex-col gap-[0.6vw]">
          <div class="bg-[rgba(59,142,230,0.1)] rounded p-[0.6vw] flex justify-between items-center">
            <div>
              <div class="text-white text-[0.8vw] mb-[0.2vw]">{{ deviceData[0].name }}</div>
              <div class="text-[0.6vw] text-gray-400">设备编码: {{ deviceData[0].code }}</div>
            </div>
            <div class="text-[0.7vw]" :class="getStatusClass(getDeviceStatus())">
              {{ getDeviceStatus() }}
            </div>
          </div>
          <div class="bg-[rgba(59,142,230,0.1)] rounded p-[0.6vw] flex flex-col gap-[0.4vw]">
            <div class="text-[0.6vw] text-gray-400">更新时间: {{ formatDateTime(getLatestDataTime()) }}</div>
            <div class="text-[0.7vw] text-white">参数总数: {{ deviceData.length }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备运行参数 -->
    <div class="h-[calc((100%-1.6vw)*2/3)] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">设备运行参数</div>
      </div>
      <div class="flex-1 mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw] overflow-auto custom-scrollbar">
        <div v-if="loading" class="flex justify-center items-center h-full">
          <div class="text-white text-[0.7vw]">加载中...</div>
        </div>
        <div v-else-if="error" class="flex justify-center items-center h-full">
          <div class="text-red-400 text-[0.7vw]">{{ error }}</div>
        </div>
        <div v-else-if="!deviceData || deviceData.length === 0" class="flex justify-center items-center h-full">
          <div class="text-yellow-400 text-[0.7vw]">未找到设备数据</div>
        </div>
        <div v-else-if="deviceData && deviceData.length > 0">
          <!-- 参数分类标签 -->
          <div class="mb-[0.8vw] flex gap-[0.6vw]">
            <div
              v-for="(category, index) in paramCategories"
              :key="index"
              class="px-[0.6vw] py-[0.3vw] rounded cursor-pointer text-[0.6vw] transition-all"
              :class="activeCategory === index ? 'bg-blue-500 text-white' : 'bg-[rgba(59,142,230,0.1)] text-gray-300 hover:bg-[rgba(59,142,230,0.2)]'"
              @click="activeCategory = index"
            >
              {{ category.name }} ({{ category.count }})
            </div>
          </div>

          <!-- 参数列表 -->
          <div class="grid grid-cols-2 gap-[0.6vw]">
            <div v-for="param in filteredParams" :key="param.id" class="bg-[rgba(59,142,230,0.1)] rounded p-[0.6vw] flex flex-col">
              <div class="text-[0.65vw] text-gray-300 mb-[0.3vw] truncate" :title="param.remark">
                {{ param.remark }}
              </div>
              <div class="flex justify-between items-end">
                <div class="text-[0.8vw] text-white font-medium">{{ param.valueData }}</div>
                <div class="text-[0.55vw] text-gray-400">{{ getParamUnit(param) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';
  import { useGlobalThreeStore } from '@/views/scene/store/globalThreeStore';
  import { useDeviceDetailStore } from './deviceDetailStore';
  import dayjs from 'dayjs';
  import { storeToRefs } from 'pinia';
  import { SELECTION_CONFIG } from '@/views/scene/config';

  const globalThreeStore = useGlobalThreeStore();
  const deviceDetailStore = useDeviceDetailStore();

  // 修改：使用storeToRefs来保持响应性
  const { deviceData, loading, error } = storeToRefs(deviceDetailStore);

  // 当前激活的参数分类
  const activeCategory = ref(0);

  // 参数分类
  const paramCategories = computed(() => {
    if (!deviceData.value || deviceData.value.length === 0) return [];

    // 按类型分类
    const typeMap: Record<number, { name: string; count: number }> = {
      1: { name: '模拟量', count: 0 },
      2: { name: '设定值', count: 0 },
    };

    // 计算每种类型的数量
    deviceData.value.forEach((item) => {
      if (typeMap[item.type]) {
        typeMap[item.type].count++;
      }
    });

    // 转换为数组
    return Object.values(typeMap);
  });

  // 根据当前选择的分类过滤参数
  const filteredParams = computed(() => {
    if (!deviceData.value || deviceData.value.length === 0) return [];

    // 获取当前选择的类型
    const selectedType = activeCategory.value + 1; // 类型从1开始

    // 过滤并排序参数
    return deviceData.value.filter((item) => item.type === selectedType).sort((a, b) => a.addr - b.addr);
  });

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '未知';
    return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
  };

  // 获取最新的数据时间
  const getLatestDataTime = () => {
    if (!deviceData.value || deviceData.value.length === 0) return '';

    // 找出最新的数据时间
    return deviceData.value.reduce((latest, current) => {
      if (!latest) return current.dataTime;
      return dayjs(current.dataTime).isAfter(dayjs(latest)) ? current.dataTime : latest;
    }, '');
  };

  // 获取设备状态
  const getDeviceStatus = () => {
    // 这里可以根据实际需求判断设备状态
    // 例如，可以根据某些关键参数的值来判断
    return '正常';
  };

  // 获取状态样式
  const getStatusClass = (status: string) => {
    if (!status) return 'text-gray-400';

    switch (status.toLowerCase()) {
      case 'normal':
      case '正常':
        return 'text-green-400';
      case 'warning':
      case '警告':
        return 'text-yellow-400';
      case 'error':
      case '错误':
      case '故障':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取参数单位
  const getParamUnit = (param: any) => {
    // 确保参数存在
    if (!param || !param.remark) return '';

    // 根据参数名称或备注判断单位
    const remark = param.remark.toLowerCase();

    if (remark.includes('频率') && remark.includes('hz')) {
      return 'Hz';
    } else if (remark.includes('时间') && remark.includes('h')) {
      return 'h';
    } else if (remark.includes('时间') && remark.includes('s')) {
      return 's';
    }

    return '';
  };

  // 关闭设备详情
  const closeDeviceDetail = () => {
    // 关闭设备详情面板
    globalThreeStore.setDeviceDetailActive(false);
    deviceDetailStore.clearDeviceData();

    // 检查是否处于观察模式，如果是则退出
    if (SELECTION_CONFIG.observeMode.enabled) {
      console.log('通过返回按钮退出观察模式');
      // 触发Escape键事件来退出观察模式
      window.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
    }
  };

  // 调试：监控数据变化
  onMounted(() => {
    console.log('DeviceDetailLeft 组件已挂载，当前设备编码:', globalThreeStore.currentDeviceCode);
    if (globalThreeStore.currentDeviceCode) {
      console.log('组件挂载时尝试加载设备数据');
      deviceDetailStore.loadDeviceData(globalThreeStore.currentDeviceCode);
    }
  });
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.5) rgba(21, 39, 77, 0.3);
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 0.4vw;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(21, 39, 77, 0.3);
    border-radius: 0.2vw;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.5);
    border-radius: 0.2vw;
  }
</style>
