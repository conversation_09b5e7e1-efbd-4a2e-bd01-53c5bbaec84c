<template>
  <div class="w-full h-full flex flex-col">
    <div class="h-[1.6vw] shrink-0 relative">
      <!-- 背景图片 -->
      <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />

      <!-- 标题文字 - 绝对定位确保与背景图片对齐 -->
      <div class="absolute left-[1vw] top-1/2 transform -translate-y-1/2 text-white text-[0.7vw] font-medium z-10"> 智慧巡检系统 </div>

      <!-- 详情管理按钮 - 绝对定位到右侧 -->
      <div
        class="absolute right-[0.5vw] top-1/2 transform -translate-y-1/2 px-[0.6vw] py-[0.25vw] bg-[#3B8EE6]/20 rounded cursor-pointer hover:bg-[#3B8EE6]/30 transition-all text-[0.6vw] text-[#3B8EE6] border border-[#3B8EE6]/30 hover:border-[#3B8EE6]/50 z-10"
        @click="showFullInterface"
      >
        详情管理
      </div>
    </div>
    <div class="flex-1 flex flex-col h-[calc(100%-2vw)] mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw]">
      <!-- 简化的巡检统计 -->
      <div class="grid grid-cols-2 gap-[0.4vw] mb-[0.6vw]">
        <div class="text-center p-[0.4vw] bg-[#4CAF50]/10 rounded">
          <div class="text-[#4CAF50] text-[1.2vw] font-bold">{{ inspectionStats.completedTasks }}/{{ inspectionStats.totalTasks }}</div>
          <div class="text-white/80 text-[0.6vw]">已完成/总任务</div>
        </div>
        <div class="text-center p-[0.4vw] bg-[#9C27B0]/10 rounded">
          <div class="text-[#9C27B0] text-[1.2vw] font-bold">{{ completionRate }}%</div>
          <div class="text-white/80 text-[0.6vw]">完成率</div>
        </div>
      </div>

      <!-- 状态指示器 -->
      <div class="flex items-center justify-between mb-[0.6vw]">
        <div class="flex items-center gap-[0.3vw]">
          <div class="w-[0.4vw] h-[0.4vw] bg-[#FF9800] rounded-full"></div>
          <span class="text-white/80 text-[0.6vw]">进行中: {{ inspectionStats.pendingTasks }}</span>
        </div>
        <div class="flex items-center gap-[0.3vw]">
          <div class="w-[0.4vw] h-[0.4vw] bg-[#FF5252] rounded-full"></div>
          <span class="text-white/80 text-[0.6vw]">超期: {{ inspectionStats.overdueTasks }}</span>
        </div>
      </div>

      <!-- 今日巡检任务 -->
      <div class="flex-1 overflow-hidden">
        <div class="text-white/80 text-[0.6vw] mb-[0.4vw]">今日巡检任务</div>
        <div class="h-[calc(100%-1.5vw)] overflow-y-auto custom-scrollbar">
          <div
            v-for="(task, index) in inspectionTasks"
            :key="index"
            class="flex items-center justify-between p-[0.4vw] mb-[0.3vw] bg-[#3B8EE6]/10 rounded hover:bg-[#3B8EE6]/20 transition-all"
          >
            <div class="flex items-center gap-[0.4vw]">
              <div class="text-white text-[0.6vw]">{{ task.title }}</div>
              <div :class="getTaskTypeClass(task.type)" class="text-[0.5vw]">
                {{ getTaskTypeText(task.type) }}
              </div>
              <div :class="getPriorityClass(task.priority)" class="text-[0.5vw]">
                {{ getPriorityText(task.priority) }}
              </div>
            </div>
            <div class="flex items-center gap-[0.3vw]">
              <div class="text-white/60 text-[0.5vw]">{{ task.assignee }}</div>
              <div :class="getStatusClass(task.status)" class="text-[0.5vw]">
                {{ getStatusText(task.status) }}
              </div>
              <div class="flex gap-[0.2vw]">
                <button
                  v-if="task.status === 'pending'"
                  @click="startTask(task)"
                  class="px-[0.3vw] py-[0.1vw] bg-[#4CAF50]/20 text-[#4CAF50] rounded text-[0.45vw] hover:bg-[#4CAF50]/30 transition-all"
                >
                  开始
                </button>
                <button
                  v-if="task.status === 'in_progress'"
                  @click="completeTask(task)"
                  class="px-[0.3vw] py-[0.1vw] bg-[#3B8EE6]/20 text-[#3B8EE6] rounded text-[0.45vw] hover:bg-[#3B8EE6]/30 transition-all"
                >
                  完成
                </button>
                <button
                  @click="viewTaskDetail(task)"
                  class="px-[0.3vw] py-[0.1vw] bg-[#FF9800]/20 text-[#FF9800] rounded text-[0.45vw] hover:bg-[#FF9800]/30 transition-all"
                >
                  详情
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务管理弹窗 -->
    <ModalDialog
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :iconSrc="dashboardTitle"
      :width="dialogType === 'full' ? '80vw' : '60vw'"
      :height="dialogType === 'full' ? '80vh' : '70vh'"
      :show-footer="false"
      @close="handleDialogClose"
    >
      <div class="p-[1vw]">
        <!-- 创建任务表单 -->
        <div v-if="dialogType === 'create'" class="space-y-[0.8vw]">
          <div class="grid grid-cols-2 gap-[0.8vw]">
            <div>
              <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">任务标题</label>
              <input
                v-model="taskForm.title"
                class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
                placeholder="请输入任务标题"
              />
            </div>
            <div>
              <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">任务类型</label>
              <select v-model="taskForm.type" class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]">
                <option value="routine">例行巡检</option>
                <option value="special">专项巡检</option>
                <option value="emergency">应急巡检</option>
                <option value="maintenance">维护巡检</option>
              </select>
            </div>
          </div>
          <div class="grid grid-cols-3 gap-[0.8vw]">
            <div>
              <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">优先级</label>
              <select v-model="taskForm.priority" class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]">
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
                <option value="urgent">紧急</option>
              </select>
            </div>
            <div>
              <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">执行人</label>
              <select v-model="taskForm.assignee" class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]">
                <option value="张工程师">张工程师</option>
                <option value="李技师">李技师</option>
                <option value="王主管">王主管</option>
                <option value="赵工程师">赵工程师</option>
              </select>
            </div>
            <div>
              <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">位置</label>
              <input
                v-model="taskForm.location"
                class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
                placeholder="巡检位置"
              />
            </div>
          </div>
          <div>
            <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">任务描述</label>
            <textarea
              v-model="taskForm.description"
              class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw] h-[3vw]"
              placeholder="请输入任务描述"
            ></textarea>
          </div>
          <div class="flex justify-end gap-[0.5vw]">
            <button @click="closeDialog" class="px-[1vw] py-[0.4vw] bg-[#666]/20 text-white rounded hover:bg-[#666]/30 transition-all text-[0.6vw]">
              取消
            </button>
            <button
              @click="submitTask"
              class="px-[1vw] py-[0.4vw] bg-[#3B8EE6]/20 text-[#3B8EE6] rounded hover:bg-[#3B8EE6]/30 transition-all text-[0.6vw]"
            >
              创建任务
            </button>
          </div>
        </div>

        <!-- 任务详情 -->
        <div v-else-if="dialogType === 'detail'" class="space-y-[0.6vw]">
          <div v-if="selectedTask" class="grid grid-cols-2 gap-[0.8vw] text-[0.6vw]">
            <div
              ><span class="text-white/60">任务标题：</span><span class="text-white">{{ selectedTask.title }}</span></div
            >
            <div
              ><span class="text-white/60">任务类型：</span
              ><span :class="getTaskTypeClass(selectedTask.type)">{{ getTaskTypeText(selectedTask.type) }}</span></div
            >
            <div
              ><span class="text-white/60">优先级：</span
              ><span :class="getPriorityClass(selectedTask.priority)">{{ getPriorityText(selectedTask.priority) }}</span></div
            >
            <div
              ><span class="text-white/60">执行人：</span><span class="text-white">{{ selectedTask.assignee }}</span></div
            >
            <div
              ><span class="text-white/60">位置：</span><span class="text-white">{{ selectedTask.location }}</span></div
            >
            <div
              ><span class="text-white/60">状态：</span
              ><span :class="getStatusClass(selectedTask.status)">{{ getStatusText(selectedTask.status) }}</span></div
            >
            <div class="col-span-2"
              ><span class="text-white/60">描述：</span><span class="text-white">{{ selectedTask.description }}</span></div
            >
          </div>
        </div>

        <!-- 完整管理界面 -->
        <div v-else-if="dialogType === 'full'" class="space-y-[0.8vw]">
          <!-- 功能导航栏 -->
          <div class="flex items-center gap-[0.5vw] mb-[1vw] p-[0.8vw] bg-[#15274D]/30 rounded">
            <button
              v-for="tab in managementTabs"
              :key="tab.key"
              :class="[
                'px-[0.8vw] py-[0.4vw] rounded transition-all text-[0.6vw]',
                activeTab === tab.key
                  ? 'bg-[#3B8EE6]/30 text-[#3B8EE6] border border-[#3B8EE6]/50'
                  : 'bg-[#3B8EE6]/10 text-white/80 hover:bg-[#3B8EE6]/20',
              ]"
              @click="activeTab = tab.key"
            >
              {{ tab.label }}
            </button>
          </div>

          <!-- 任务管理 -->
          <div v-if="activeTab === 'tasks'" class="space-y-[0.6vw]">
            <div class="flex justify-between items-center">
              <h3 class="text-white text-[0.8vw] font-medium">巡检任务管理</h3>
              <button
                @click="showCreateTask"
                class="px-[0.8vw] py-[0.4vw] bg-[#4CAF50]/20 text-[#4CAF50] rounded hover:bg-[#4CAF50]/30 transition-all text-[0.6vw]"
              >
                创建任务
              </button>
            </div>
            <div class="max-h-[25vw] overflow-y-auto custom-scrollbar">
              <table class="w-full border-collapse text-white">
                <thead>
                  <tr>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">任务标题</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">类型</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">优先级</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">执行人</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">状态</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="task in allInspectionTasks" :key="task.id" class="hover:bg-[#3B8EE6]/10 transition-all border-b border-white/10">
                    <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ task.title }}</td>
                    <td class="p-[0.6vw] text-[0.6vw]">
                      <span :class="getTaskTypeClass(task.type)">{{ getTaskTypeText(task.type) }}</span>
                    </td>
                    <td class="p-[0.6vw] text-[0.6vw]">
                      <span :class="getPriorityClass(task.priority)">{{ getPriorityText(task.priority) }}</span>
                    </td>
                    <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ task.assignee }}</td>
                    <td class="p-[0.6vw] text-[0.6vw]">
                      <span :class="getStatusClass(task.status)">{{ getStatusText(task.status) }}</span>
                    </td>
                    <td class="p-[0.6vw] text-[0.6vw]">
                      <div class="flex gap-[0.2vw]">
                        <button
                          @click="editTask(task)"
                          class="px-[0.3vw] py-[0.1vw] bg-[#FF9800]/20 text-[#FF9800] rounded text-[0.5vw] hover:bg-[#FF9800]/30 transition-all"
                        >
                          编辑
                        </button>
                        <button
                          @click="deleteTask(task)"
                          class="px-[0.3vw] py-[0.1vw] bg-[#FF5252]/20 text-[#FF5252] rounded text-[0.5vw] hover:bg-[#FF5252]/30 transition-all"
                        >
                          删除
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 路线管理 -->
          <div v-else-if="activeTab === 'routes'" class="space-y-[0.6vw]">
            <div class="flex justify-between items-center">
              <h3 class="text-white text-[0.8vw] font-medium">巡检路线管理</h3>
              <button
                @click="createRoute"
                class="px-[0.8vw] py-[0.4vw] bg-[#9C27B0]/20 text-[#9C27B0] rounded hover:bg-[#9C27B0]/30 transition-all text-[0.6vw]"
              >
                创建路线
              </button>
            </div>
            <div class="grid grid-cols-2 gap-[0.8vw]">
              <div v-for="route in inspectionRoutes" :key="route.id" class="p-[0.8vw] bg-[#15274D]/30 rounded">
                <div class="flex justify-between items-start mb-[0.4vw]">
                  <h4 class="text-white text-[0.7vw] font-medium">{{ route.name }}</h4>
                  <span :class="getRouteDifficultyClass(route.difficulty)" class="text-[0.5vw]">{{ route.difficulty }}</span>
                </div>
                <p class="text-white/60 text-[0.55vw] mb-[0.4vw]">{{ route.description }}</p>
                <div class="flex justify-between items-center text-[0.5vw]">
                  <span class="text-white/60">检查点: {{ route.checkPoints.length }}</span>
                  <span class="text-white/60">预计: {{ route.estimatedDuration }}分钟</span>
                </div>
                <div class="flex gap-[0.2vw] mt-[0.4vw]">
                  <button
                    @click="editRoute(route)"
                    class="px-[0.4vw] py-[0.2vw] bg-[#3B8EE6]/20 text-[#3B8EE6] rounded text-[0.5vw] hover:bg-[#3B8EE6]/30 transition-all"
                  >
                    编辑
                  </button>
                  <button
                    @click="deleteRoute(route)"
                    class="px-[0.4vw] py-[0.2vw] bg-[#FF5252]/20 text-[#FF5252] rounded text-[0.5vw] hover:bg-[#FF5252]/30 transition-all"
                  >
                    删除
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 记录查询 -->
          <div v-else-if="activeTab === 'records'" class="space-y-[0.6vw]">
            <div class="flex justify-between items-center">
              <h3 class="text-white text-[0.8vw] font-medium">巡检记录查询</h3>
              <div class="flex gap-[0.5vw]">
                <select
                  v-model="recordFilter.type"
                  class="px-[0.5vw] py-[0.2vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
                >
                  <option value="">全部类型</option>
                  <option value="routine">例行巡检</option>
                  <option value="special">专项巡检</option>
                  <option value="emergency">应急巡检</option>
                  <option value="maintenance">维护巡检</option>
                </select>
                <select
                  v-model="recordFilter.status"
                  class="px-[0.5vw] py-[0.2vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
                >
                  <option value="">全部状态</option>
                  <option value="completed">已完成</option>
                  <option value="in_progress">进行中</option>
                  <option value="pending">待执行</option>
                  <option value="overdue">已超期</option>
                </select>
              </div>
            </div>
            <div class="max-h-[25vw] overflow-y-auto custom-scrollbar">
              <table class="w-full border-collapse text-white">
                <thead>
                  <tr>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">任务</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">执行人</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">开始时间</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">完成时间</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">结果</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="record in filteredRecords" :key="record.id" class="hover:bg-[#3B8EE6]/10 transition-all border-b border-white/10">
                    <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ record.taskTitle }}</td>
                    <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ record.assignee }}</td>
                    <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ formatDateTime(record.startTime) }}</td>
                    <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ record.endTime ? formatDateTime(record.endTime) : '-' }}</td>
                    <td class="p-[0.6vw] text-[0.6vw]">
                      <span :class="getRecordResultClass(record.result)">{{ record.result }}</span>
                    </td>
                    <td class="p-[0.6vw] text-[0.6vw]">
                      <button
                        @click="viewRecordDetail(record)"
                        class="px-[0.3vw] py-[0.1vw] bg-[#3B8EE6]/20 text-[#3B8EE6] rounded text-[0.5vw] hover:bg-[#3B8EE6]/30 transition-all"
                      >
                        详情
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 工单管理 -->
          <div v-else-if="activeTab === 'workorders'">
            <InspectionWorkOrderPanel />
          </div>

          <!-- 二维码管理 -->
          <div v-else-if="activeTab === 'qrcode'" class="space-y-[0.6vw]">
            <div class="flex justify-between items-center">
              <h3 class="text-white text-[0.8vw] font-medium">二维码管理</h3>
              <button
                @click="generateQRCode"
                class="px-[0.8vw] py-[0.4vw] bg-[#FF9800]/20 text-[#FF9800] rounded hover:bg-[#FF9800]/30 transition-all text-[0.6vw]"
              >
                生成二维码
              </button>
            </div>
            <div class="grid grid-cols-3 gap-[0.8vw]">
              <div v-for="qr in qrCodes" :key="qr.id" class="p-[0.8vw] bg-[#15274D]/30 rounded text-center">
                <div class="w-[6vw] h-[6vw] bg-white rounded mx-auto mb-[0.4vw] flex items-center justify-center">
                  <div class="text-black text-[0.4vw]">QR Code</div>
                </div>
                <h4 class="text-white text-[0.6vw] font-medium mb-[0.2vw]">{{ qr.location }}</h4>
                <p class="text-white/60 text-[0.5vw] mb-[0.4vw]">{{ qr.description }}</p>
                <div class="flex gap-[0.2vw] justify-center">
                  <button
                    @click="downloadQRCode(qr)"
                    class="px-[0.4vw] py-[0.2vw] bg-[#4CAF50]/20 text-[#4CAF50] rounded text-[0.5vw] hover:bg-[#4CAF50]/30 transition-all"
                  >
                    下载
                  </button>
                  <button
                    @click="deleteQRCode(qr)"
                    class="px-[0.4vw] py-[0.2vw] bg-[#FF5252]/20 text-[#FF5252] rounded text-[0.5vw] hover:bg-[#FF5252]/30 transition-all"
                  >
                    删除
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 设备监控 -->
          <div v-else-if="activeTab === 'devices'" class="space-y-[0.6vw]">
            <h3 class="text-white text-[0.8vw] font-medium">设备状态监控</h3>
            <div class="grid grid-cols-4 gap-[0.6vw] mb-[0.8vw]">
              <div class="text-center p-[0.6vw] bg-[#4CAF50]/10 rounded">
                <div class="text-[#4CAF50] text-[1.2vw] font-bold">{{ deviceStats.normal }}</div>
                <div class="text-white/80 text-[0.6vw]">正常</div>
              </div>
              <div class="text-center p-[0.6vw] bg-[#FF9800]/10 rounded">
                <div class="text-[#FF9800] text-[1.2vw] font-bold">{{ deviceStats.warning }}</div>
                <div class="text-white/80 text-[0.6vw]">警告</div>
              </div>
              <div class="text-center p-[0.6vw] bg-[#FF5252]/10 rounded">
                <div class="text-[#FF5252] text-[1.2vw] font-bold">{{ deviceStats.error }}</div>
                <div class="text-white/80 text-[0.6vw]">故障</div>
              </div>
              <div class="text-center p-[0.6vw] bg-[#9E9E9E]/10 rounded">
                <div class="text-[#9E9E9E] text-[1.2vw] font-bold">{{ deviceStats.offline }}</div>
                <div class="text-white/80 text-[0.6vw]">离线</div>
              </div>
            </div>
            <div class="max-h-[20vw] overflow-y-auto custom-scrollbar">
              <table class="w-full border-collapse text-white">
                <thead>
                  <tr>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">设备名称</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">位置</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">状态</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">最后检查</th>
                    <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="device in monitoredDevices" :key="device.id" class="hover:bg-[#3B8EE6]/10 transition-all border-b border-white/10">
                    <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ device.name }}</td>
                    <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ device.location }}</td>
                    <td class="p-[0.6vw] text-[0.6vw]">
                      <span :class="getDeviceStatusClass(device.status)">{{ getDeviceStatusText(device.status) }}</span>
                    </td>
                    <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ formatDateTime(device.lastCheck) }}</td>
                    <td class="p-[0.6vw] text-[0.6vw]">
                      <button
                        @click="checkDevice(device)"
                        class="px-[0.3vw] py-[0.1vw] bg-[#3B8EE6]/20 text-[#3B8EE6] rounded text-[0.5vw] hover:bg-[#3B8EE6]/30 transition-all"
                      >
                        检查
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 其他弹窗内容 -->
        <div v-else class="text-white/80 text-[0.6vw] text-center py-[2vw]"> {{ dialogTitle }}功能开发中... </div>
      </div>
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import InspectionWorkOrderPanel from './InspectionWorkOrderPanel.vue';

  // 巡检统计数据
  const inspectionStats = reactive({
    totalTasks: 24,
    completedTasks: 20,
    pendingTasks: 4,
    overdueTasks: 0,
  });

  // 其他统计数据
  const completionRate = ref(83.33);
  const avgDuration = ref(45);
  const deviceCount = ref(156);
  const issueCount = ref(0);

  // 巡检任务列表
  const inspectionTasks = reactive([
    {
      id: 1,
      title: '空调系统巡检',
      type: 'routine',
      priority: 'medium',
      assignee: '张工程师',
      status: 'in_progress',
      location: '1F机房',
      description: '检查空调运行状态',
    },
    {
      id: 2,
      title: '消防设备检查',
      type: 'special',
      priority: 'high',
      assignee: '李技师',
      status: 'pending',
      location: '2F走廊',
      description: '检查消防设备完好性',
    },
    {
      id: 3,
      title: '电力系统维护',
      type: 'maintenance',
      priority: 'medium',
      assignee: '王主管',
      status: 'completed',
      location: '配电室',
      description: '定期维护电力设备',
    },
    {
      id: 4,
      title: '网络设备巡检',
      type: 'routine',
      priority: 'low',
      assignee: '赵工程师',
      status: 'completed',
      location: '3F机房',
      description: '检查网络设备运行状态',
    },
    {
      id: 5,
      title: '安防系统检查',
      type: 'routine',
      priority: 'medium',
      assignee: '张工程师',
      status: 'pending',
      location: '大厅',
      description: '检查监控摄像头',
    },
  ]);

  // 弹窗相关
  const dialogVisible = ref(false);
  const dialogTitle = ref('');
  const dialogType = ref('');
  const selectedTask = ref(null);

  // 任务表单
  const taskForm = reactive({
    title: '',
    type: 'routine',
    priority: 'medium',
    assignee: '',
    location: '',
    description: '',
  });

  // 管理界面标签页
  const managementTabs = ref([
    { key: 'tasks', label: '任务管理' },
    { key: 'routes', label: '路线管理' },
    { key: 'records', label: '记录查询' },
    { key: 'workorders', label: '工单管理' },
    { key: 'qrcode', label: '二维码管理' },
    { key: 'devices', label: '设备监控' },
  ]);

  const activeTab = ref('tasks');

  // 所有巡检任务（扩展数据）
  const allInspectionTasks = reactive([
    ...inspectionTasks,
    {
      id: 6,
      title: '温度传感器检查',
      type: 'routine',
      priority: 'low',
      assignee: '李技师',
      status: 'completed',
      location: '2F机房',
      description: '检查温度传感器工作状态',
    },
    {
      id: 7,
      title: 'UPS电源巡检',
      type: 'maintenance',
      priority: 'high',
      assignee: '王主管',
      status: 'pending',
      location: '配电室',
      description: '检查UPS电源系统',
    },
    {
      id: 8,
      title: '门禁系统检查',
      type: 'special',
      priority: 'medium',
      assignee: '赵工程师',
      status: 'in_progress',
      location: '各楼层',
      description: '检查门禁系统运行状态',
    },
  ]);

  // 巡检路线数据
  const inspectionRoutes = reactive([
    {
      id: 1,
      name: '日常巡检路线A',
      description: '覆盖1-3楼主要设备区域的日常巡检',
      checkPoints: ['1F机房', '2F配电室', '3F网络中心'],
      estimatedDuration: 45,
      difficulty: '简单',
      frequency: '每日',
    },
    {
      id: 2,
      name: '安全专项路线',
      description: '重点检查消防和安防设备',
      checkPoints: ['消防控制室', '各楼层消防设备', '监控中心'],
      estimatedDuration: 60,
      difficulty: '中等',
      frequency: '每周',
    },
    {
      id: 3,
      name: '设备维护路线',
      description: '专业设备深度检查和维护',
      checkPoints: ['UPS机房', '空调机房', '发电机房'],
      estimatedDuration: 90,
      difficulty: '困难',
      frequency: '每月',
    },
  ]);

  // 巡检记录数据
  const inspectionRecords = reactive([
    {
      id: 1,
      taskTitle: '空调系统巡检',
      assignee: '张工程师',
      startTime: '2024-01-15T09:00:00',
      endTime: '2024-01-15T10:30:00',
      result: '正常',
      type: 'routine',
      status: 'completed',
    },
    {
      id: 2,
      taskTitle: '消防设备检查',
      assignee: '李技师',
      startTime: '2024-01-15T14:00:00',
      endTime: '2024-01-15T15:45:00',
      result: '发现问题',
      type: 'special',
      status: 'completed',
    },
    {
      id: 3,
      taskTitle: '网络设备巡检',
      assignee: '赵工程师',
      startTime: '2024-01-16T08:30:00',
      endTime: null,
      result: '进行中',
      type: 'routine',
      status: 'in_progress',
    },
  ]);

  // 记录筛选条件
  const recordFilter = reactive({
    type: '',
    status: '',
  });

  // 二维码数据
  const qrCodes = reactive([
    {
      id: 1,
      location: '1F机房入口',
      description: '机房设备巡检点',
      code: 'QR001',
      createdAt: '2024-01-10',
    },
    {
      id: 2,
      location: '2F配电室',
      description: '配电设备检查点',
      code: 'QR002',
      createdAt: '2024-01-10',
    },
    {
      id: 3,
      location: '3F网络中心',
      description: '网络设备监控点',
      code: 'QR003',
      createdAt: '2024-01-10',
    },
  ]);

  // 设备监控数据
  const deviceStats = reactive({
    normal: 142,
    warning: 8,
    error: 3,
    offline: 3,
  });

  const monitoredDevices = reactive([
    {
      id: 1,
      name: '空调主机A',
      location: '1F机房',
      status: 'normal',
      lastCheck: '2024-01-16T10:30:00',
    },
    {
      id: 2,
      name: 'UPS电源系统',
      location: '配电室',
      status: 'warning',
      lastCheck: '2024-01-16T09:15:00',
    },
    {
      id: 3,
      name: '网络交换机',
      location: '3F网络中心',
      status: 'normal',
      lastCheck: '2024-01-16T11:00:00',
    },
    {
      id: 4,
      name: '消防报警主机',
      location: '消防控制室',
      status: 'error',
      lastCheck: '2024-01-15T16:30:00',
    },
  ]);

  // 显示完整功能界面
  const showFullInterface = () => {
    dialogTitle.value = '智慧巡检系统 - 完整管理界面';
    dialogType.value = 'full';
    dialogVisible.value = true;
  };

  // 方法
  const showCreateTask = () => {
    dialogTitle.value = '创建巡检任务';
    dialogType.value = 'create';
    dialogVisible.value = true;
  };

  const showRouteManagement = () => {
    dialogTitle.value = '巡检路线管理';
    dialogType.value = 'route';
    dialogVisible.value = true;
  };

  const showInspectionRecords = () => {
    dialogTitle.value = '巡检记录查询';
    dialogType.value = 'records';
    dialogVisible.value = true;
  };

  const showQRCodeGenerate = () => {
    dialogTitle.value = '二维码管理';
    dialogType.value = 'qrcode';
    dialogVisible.value = true;
  };

  const startTask = (task) => {
    task.status = 'in_progress';
    inspectionStats.pendingTasks--;
    inspectionStats.pendingTasks++;
    console.log('开始任务:', task.title);
  };

  const completeTask = (task) => {
    task.status = 'completed';
    inspectionStats.pendingTasks--;
    inspectionStats.completedTasks++;
    console.log('完成任务:', task.title);
  };

  const viewTaskDetail = (task) => {
    selectedTask.value = task;
    dialogTitle.value = '任务详情';
    dialogType.value = 'detail';
    dialogVisible.value = true;
  };

  const closeDialog = () => {
    dialogVisible.value = false;
    selectedTask.value = null;
    // 重置表单
    Object.assign(taskForm, {
      title: '',
      type: 'routine',
      priority: 'medium',
      assignee: '',
      location: '',
      description: '',
    });
  };

  const handleDialogClose = () => {
    closeDialog();
  };

  const submitTask = () => {
    if (!taskForm.title || !taskForm.assignee || !taskForm.location) {
      alert('请填写完整信息');
      return;
    }

    // 添加新任务
    const newTask = {
      id: Date.now(),
      title: taskForm.title,
      type: taskForm.type,
      priority: taskForm.priority,
      assignee: taskForm.assignee,
      status: 'pending',
      location: taskForm.location,
      description: taskForm.description,
    };

    inspectionTasks.unshift(newTask);
    inspectionStats.totalTasks++;
    inspectionStats.pendingTasks++;

    closeDialog();
  };

  // 状态和类型相关方法
  const getTaskTypeClass = (type) => {
    switch (type) {
      case 'routine':
        return 'text-[#3B8EE6]';
      case 'special':
        return 'text-[#9C27B0]';
      case 'emergency':
        return 'text-[#FF5252]';
      case 'maintenance':
        return 'text-[#FF9800]';
      default:
        return 'text-white/60';
    }
  };

  const getTaskTypeText = (type) => {
    switch (type) {
      case 'routine':
        return '例行';
      case 'special':
        return '专项';
      case 'emergency':
        return '应急';
      case 'maintenance':
        return '维护';
      default:
        return '-';
    }
  };

  const getPriorityClass = (priority) => {
    switch (priority) {
      case 'low':
        return 'text-[#4CAF50]';
      case 'medium':
        return 'text-[#FF9800]';
      case 'high':
        return 'text-[#FF5252]';
      case 'urgent':
        return 'text-[#E91E63]';
      default:
        return 'text-white/60';
    }
  };

  const getPriorityText = (priority) => {
    switch (priority) {
      case 'low':
        return '低';
      case 'medium':
        return '中';
      case 'high':
        return '高';
      case 'urgent':
        return '紧急';
      default:
        return '-';
    }
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 'pending':
        return 'text-[#FF9800]';
      case 'in_progress':
        return 'text-[#3B8EE6]';
      case 'completed':
        return 'text-[#4CAF50]';
      case 'overdue':
        return 'text-[#FF5252]';
      default:
        return 'text-white/60';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return '待执行';
      case 'in_progress':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'overdue':
        return '已超期';
      default:
        return '-';
    }
  };

  // 实时更新数据
  const updateData = () => {
    // 模拟完成率变化，保留2位小数
    const newRate = Math.min(100, Math.max(80, completionRate.value + Math.random() * 2 - 1));
    completionRate.value = Math.round(newRate * 100) / 100;

    // 保持设备问题数量为0（正常状态）
    issueCount.value = 0;
  };

  // 定时更新
  let updateInterval = null;
  onMounted(() => {
    updateInterval = setInterval(updateData, 45000); // 每45秒更新一次
  });

  onBeforeUnmount(() => {
    if (updateInterval) {
      clearInterval(updateInterval);
    }
  });

  // 计算属性 - 筛选后的记录
  const filteredRecords = computed(() => {
    return inspectionRecords.filter((record) => {
      const typeMatch = !recordFilter.type || record.type === recordFilter.type;
      const statusMatch = !recordFilter.status || record.status === recordFilter.status;
      return typeMatch && statusMatch;
    });
  });

  // 新增方法实现
  const editTask = (task) => {
    selectedTask.value = task;
    Object.assign(taskForm, task);
    dialogTitle.value = '编辑巡检任务';
    dialogType.value = 'create';
    dialogVisible.value = true;
  };

  const deleteTask = (task) => {
    if (confirm(`确定要删除任务"${task.title}"吗？`)) {
      const index = allInspectionTasks.findIndex((t) => t.id === task.id);
      if (index > -1) {
        allInspectionTasks.splice(index, 1);
        // 更新统计数据
        inspectionStats.totalTasks--;
        if (task.status === 'completed') {
          inspectionStats.completedTasks--;
        } else if (task.status === 'pending') {
          inspectionStats.pendingTasks--;
        } else if (task.status === 'overdue') {
          inspectionStats.overdueTasks--;
        }
      }
    }
  };

  const createRoute = () => {
    const routeName = prompt('请输入路线名称:');
    if (routeName) {
      const newRoute = {
        id: Date.now(),
        name: routeName,
        description: '新建巡检路线',
        checkPoints: ['检查点1', '检查点2'],
        estimatedDuration: 30,
        difficulty: '简单',
        frequency: '每日',
      };
      inspectionRoutes.push(newRoute);
    }
  };

  const editRoute = (route) => {
    const newName = prompt('请输入新的路线名称:', route.name);
    if (newName && newName !== route.name) {
      route.name = newName;
    }
  };

  const deleteRoute = (route) => {
    if (confirm(`确定要删除路线"${route.name}"吗？`)) {
      const index = inspectionRoutes.findIndex((r) => r.id === route.id);
      if (index > -1) {
        inspectionRoutes.splice(index, 1);
      }
    }
  };

  const viewRecordDetail = (record) => {
    selectedTask.value = record;
    dialogTitle.value = '巡检记录详情';
    dialogType.value = 'record_detail';
    dialogVisible.value = true;
  };

  const generateQRCode = () => {
    const location = prompt('请输入二维码位置:');
    if (location) {
      const newQR = {
        id: Date.now(),
        location: location,
        description: '巡检点二维码',
        code: `QR${String(Date.now()).slice(-3)}`,
        createdAt: new Date().toISOString().split('T')[0],
      };
      qrCodes.push(newQR);
    }
  };

  const downloadQRCode = (qr) => {
    // 模拟下载二维码
    console.log(`下载二维码: ${qr.location} (${qr.code})`);
    alert(`二维码 ${qr.code} 下载成功！`);
  };

  const deleteQRCode = (qr) => {
    if (confirm(`确定要删除二维码"${qr.location}"吗？`)) {
      const index = qrCodes.findIndex((q) => q.id === qr.id);
      if (index > -1) {
        qrCodes.splice(index, 1);
      }
    }
  };

  const checkDevice = (device) => {
    // 模拟设备检查
    device.lastCheck = new Date().toISOString();
    device.status = Math.random() > 0.8 ? 'warning' : 'normal';
    console.log(`检查设备: ${device.name}`);
  };

  // 格式化日期时间
  const formatDateTime = (dateStr) => {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
  };

  // 获取路线难度样式
  const getRouteDifficultyClass = (difficulty) => {
    switch (difficulty) {
      case '简单':
        return 'text-[#4CAF50]';
      case '中等':
        return 'text-[#FF9800]';
      case '困难':
        return 'text-[#FF5252]';
      default:
        return 'text-white/60';
    }
  };

  // 获取记录结果样式
  const getRecordResultClass = (result) => {
    switch (result) {
      case '正常':
        return 'text-[#4CAF50]';
      case '发现问题':
        return 'text-[#FF9800]';
      case '严重问题':
        return 'text-[#FF5252]';
      case '进行中':
        return 'text-[#3B8EE6]';
      default:
        return 'text-white/60';
    }
  };

  // 获取设备状态样式
  const getDeviceStatusClass = (status) => {
    switch (status) {
      case 'normal':
        return 'text-[#4CAF50]';
      case 'warning':
        return 'text-[#FF9800]';
      case 'error':
        return 'text-[#FF5252]';
      case 'offline':
        return 'text-[#9E9E9E]';
      default:
        return 'text-white/60';
    }
  };

  // 获取设备状态文本
  const getDeviceStatusText = (status) => {
    switch (status) {
      case 'normal':
        return '正常';
      case 'warning':
        return '警告';
      case 'error':
        return '故障';
      case 'offline':
        return '离线';
      default:
        return '-';
    }
  };

  defineExpose({
    updateData,
  });
</script>

<style scoped>
  .custom-scrollbar::-webkit-scrollbar {
    width: 0.2vw;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(59, 142, 230, 0.1);
    border-radius: 0.1vw;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(59, 142, 230, 0.5);
    border-radius: 0.1vw;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 142, 230, 0.7);
  }
</style>
