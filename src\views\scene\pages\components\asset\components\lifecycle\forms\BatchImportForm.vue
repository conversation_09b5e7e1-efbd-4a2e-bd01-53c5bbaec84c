<template>
  <div class="p-[1vw] space-y-[1vw]">
    <!-- 导入说明 -->
    <div class="bg-blue-500/10 border border-blue-500/20 rounded p-[0.8vw]">
      <h3 class="text-[0.7vw] text-blue-400 font-semibold mb-[0.4vw]">导入说明</h3>
      <ul class="text-[0.6vw] text-gray-300 space-y-[0.2vw]">
        <li>• 请使用提供的模板文件进行数据填写</li>
        <li>• 支持 Excel (.xlsx) 和 CSV (.csv) 格式</li>
        <li>• 单次最多导入 1000 条记录</li>
        <li>• 必填字段：资产名称、资产类型、供应商、入库日期</li>
      </ul>
    </div>

    <!-- 模板下载 -->
    <div class="flex items-center justify-between bg-black/20 rounded p-[0.8vw]">
      <div>
        <div class="text-[0.7vw] text-white font-semibold">下载导入模板</div>
        <div class="text-[0.6vw] text-gray-400">请先下载模板文件，按照格式填写数据</div>
      </div>
      <div class="flex space-x-[0.6vw]">
        <button
          class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
          @click="downloadTemplate('excel')"
        >
          <DownloadOutlined class="mr-[0.2vw]" />
          Excel 模板
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
          @click="downloadTemplate('csv')"
        >
          <DownloadOutlined class="mr-[0.2vw]" />
          CSV 模板
        </button>
      </div>
    </div>

    <!-- 文件上传 -->
    <div>
      <label class="block text-[0.7vw] text-white font-semibold mb-[0.6vw]">选择导入文件</label>
      <div
        class="border-2 border-dashed border-white/20 rounded p-[2vw] text-center hover:border-blue-400 transition-colors cursor-pointer"
        @click="triggerFileInput"
        @dragover.prevent
        @drop.prevent="handleFileDrop"
      >
        <div v-if="!selectedFile" class="space-y-[0.6vw]">
          <div class="text-[2vw] text-gray-400">📁</div>
          <div class="text-[0.7vw] text-gray-300"> 点击选择文件或拖拽文件到此区域 </div>
          <div class="text-[0.6vw] text-gray-500"> 支持 .xlsx 和 .csv 格式，文件大小不超过 5MB </div>
        </div>

        <div v-else class="space-y-[0.6vw]">
          <div class="text-[2vw] text-green-400">📄</div>
          <div class="text-[0.7vw] text-white">{{ selectedFile.name }}</div>
          <div class="text-[0.6vw] text-gray-400"> 文件大小: {{ formatFileSize(selectedFile.size) }} </div>
          <button class="text-[0.6vw] text-red-400 hover:text-red-300" @click.stop="removeFile"> 删除文件 </button>
        </div>
      </div>

      <input ref="fileInput" type="file" accept=".xlsx,.csv" class="hidden" @change="handleFileSelect" />
    </div>

    <!-- 导入选项 -->
    <div class="space-y-[0.8vw]">
      <h3 class="text-[0.7vw] text-white font-semibold">导入选项</h3>

      <div class="grid grid-cols-2 gap-[1vw]">
        <div>
          <label class="flex items-center space-x-[0.4vw]">
            <input v-model="importOptions.skipFirstRow" type="checkbox" class="text-blue-500" />
            <span class="text-[0.6vw] text-gray-300">跳过第一行（标题行）</span>
          </label>
        </div>

        <div>
          <label class="flex items-center space-x-[0.4vw]">
            <input v-model="importOptions.updateExisting" type="checkbox" class="text-blue-500" />
            <span class="text-[0.6vw] text-gray-300">更新已存在的资产</span>
          </label>
        </div>

        <div>
          <label class="flex items-center space-x-[0.4vw]">
            <input v-model="importOptions.validateData" type="checkbox" class="text-blue-500" />
            <span class="text-[0.6vw] text-gray-300">导入前验证数据</span>
          </label>
        </div>

        <div>
          <label class="flex items-center space-x-[0.4vw]">
            <input v-model="importOptions.autoGenerateCode" type="checkbox" class="text-blue-500" />
            <span class="text-[0.6vw] text-gray-300">自动生成资产编号</span>
          </label>
        </div>
      </div>
    </div>

    <!-- 预览数据 -->
    <div v-if="previewData.length > 0" class="space-y-[0.6vw]">
      <h3 class="text-[0.7vw] text-white font-semibold">数据预览</h3>
      <div class="bg-black/20 rounded border border-white/10 overflow-hidden">
        <div class="overflow-auto max-h-[15vw]">
          <table class="w-full text-[0.6vw]">
            <thead class="bg-blue-500/20 sticky top-0">
              <tr>
                <th class="text-left p-[0.4vw] text-gray-300 border-b border-white/10">行号</th>
                <th class="text-left p-[0.4vw] text-gray-300 border-b border-white/10">资产名称</th>
                <th class="text-left p-[0.4vw] text-gray-300 border-b border-white/10">资产类型</th>
                <th class="text-left p-[0.4vw] text-gray-300 border-b border-white/10">供应商</th>
                <th class="text-left p-[0.4vw] text-gray-300 border-b border-white/10">状态</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in previewData.slice(0, 10)" :key="index" class="hover:bg-white/5">
                <td class="p-[0.4vw] text-gray-300 border-b border-white/5">{{ index + 1 }}</td>
                <td class="p-[0.4vw] text-white border-b border-white/5">{{ row.name || '-' }}</td>
                <td class="p-[0.4vw] text-gray-300 border-b border-white/5">{{ row.type || '-' }}</td>
                <td class="p-[0.4vw] text-gray-300 border-b border-white/5">{{ row.supplier || '-' }}</td>
                <td class="p-[0.4vw] border-b border-white/5">
                  <span
                    :class="[
                      'px-[0.3vw] py-[0.1vw] rounded text-[0.5vw]',
                      row.valid ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400',
                    ]"
                  >
                    {{ row.valid ? '有效' : '无效' }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-if="previewData.length > 10" class="p-[0.6vw] text-[0.6vw] text-gray-400 text-center border-t border-white/10">
          显示前 10 条记录，共 {{ previewData.length }} 条
        </div>
      </div>
    </div>

    <!-- 导入结果 -->
    <div v-if="importResult" class="space-y-[0.6vw]">
      <h3 class="text-[0.7vw] text-white font-semibold">导入结果</h3>
      <div class="grid grid-cols-3 gap-[0.8vw]">
        <div class="bg-green-500/10 border border-green-500/20 rounded p-[0.6vw] text-center">
          <div class="text-[1vw] text-green-400 font-bold">{{ importResult.success }}</div>
          <div class="text-[0.6vw] text-green-300">成功导入</div>
        </div>
        <div class="bg-red-500/10 border border-red-500/20 rounded p-[0.6vw] text-center">
          <div class="text-[1vw] text-red-400 font-bold">{{ importResult.failed }}</div>
          <div class="text-[0.6vw] text-red-300">导入失败</div>
        </div>
        <div class="bg-yellow-500/10 border border-yellow-500/20 rounded p-[0.6vw] text-center">
          <div class="text-[1vw] text-yellow-400 font-bold">{{ importResult.skipped }}</div>
          <div class="text-[0.6vw] text-yellow-300">跳过记录</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { DownloadOutlined } from '@ant-design/icons-vue';

  const props = defineProps({
    file: {
      type: File,
      default: null,
    },
  });

  const emit = defineEmits(['update:file']);

  // 响应式数据
  const fileInput = ref(null);
  const selectedFile = ref(props.file);
  const previewData = ref([]);
  const importResult = ref(null);

  // 导入选项
  const importOptions = ref({
    skipFirstRow: true,
    updateExisting: false,
    validateData: true,
    autoGenerateCode: true,
  });

  // 监听文件变化
  watch(selectedFile, (newFile) => {
    emit('update:file', newFile);
    if (newFile) {
      parseFile(newFile);
    } else {
      previewData.value = [];
      importResult.value = null;
    }
  });

  // 触发文件选择
  const triggerFileInput = () => {
    fileInput.value?.click();
  };

  // 处理文件选择
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      selectedFile.value = file;
    }
  };

  // 处理文件拖拽
  const handleFileDrop = (event) => {
    const file = event.dataTransfer.files[0];
    if (file && (file.name.endsWith('.xlsx') || file.name.endsWith('.csv'))) {
      selectedFile.value = file;
    }
  };

  // 删除文件
  const removeFile = () => {
    selectedFile.value = null;
    if (fileInput.value) {
      fileInput.value.value = '';
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 下载模板
  const downloadTemplate = (type) => {
    console.log(`下载 ${type} 模板`);
    // 这里应该实现实际的模板下载逻辑
  };

  // 解析文件
  const parseFile = (file) => {
    // 模拟文件解析
    setTimeout(() => {
      previewData.value = [
        { name: '服务器-001', type: 'server', supplier: '戴尔科技', valid: true },
        { name: '交换机-001', type: 'network', supplier: '华为技术', valid: true },
        { name: '', type: 'storage', supplier: '联想集团', valid: false },
        // 更多模拟数据...
      ];
    }, 1000);
  };
</script>
