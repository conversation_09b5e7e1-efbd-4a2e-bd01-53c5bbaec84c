<template>
  <div class="p-4">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <CarOutlined class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">总车位</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.totalSpaces }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <CheckCircleOutlined class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">可用车位</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.availableSpaces }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <CloseCircleOutlined class="h-8 w-8 text-red-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">已占用</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.occupiedSpaces }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <PercentageOutlined class="h-8 w-8 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">使用率</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.occupancyRate }}%</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 停车引导和收入统计 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 停车引导 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <CompassOutlined class="mr-2" />
          停车引导
        </h3>
        <div class="space-y-3">
          <div v-for="guidance in parkingGuidance" :key="guidance.floor" class="flex justify-between items-center p-3 bg-gray-50 rounded">
            <div>
              <div class="font-medium">{{ guidance.floor }} - {{ guidance.area }}</div>
              <div class="text-sm text-gray-500">预计步行 {{ guidance.estimatedWalkTime }} 分钟</div>
            </div>
            <div class="text-right">
              <div class="text-lg font-semibold text-green-600">{{ guidance.availableSpaces }}</div>
              <div class="text-sm text-gray-500">可用车位</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 收入统计 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <DollarOutlined class="mr-2" />
          收入统计
        </h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-gray-500">今日收入</span>
            <span class="text-2xl font-semibold text-green-600">¥{{ stats.todayIncome }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-500">本月收入</span>
            <span class="text-xl font-semibold">¥{{ stats.monthlyIncome }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-500">平均停车时长</span>
            <span class="text-lg">{{ stats.avgParkingDuration }} 小时</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮和筛选 -->
    <div class="mb-4 flex justify-between items-center">
      <div class="flex space-x-2">
        <a-button type="primary" @click="showVehicleEntryModal">
          <LoginOutlined />
          车辆入场
        </a-button>
        <a-button @click="showVehicleExitModal">
          <LogoutOutlined />
          车辆出场
        </a-button>
        <a-button @click="showReservationModal">
          <CalendarOutlined />
          预约车位
        </a-button>
        <a-button @click="refreshData">
          <ReloadOutlined />
          刷新数据
        </a-button>
      </div>

      <div class="flex space-x-2">
        <a-select v-model:value="floorFilter" placeholder="楼层筛选" style="width: 120px" @change="handleSearch">
          <a-select-option value="">全部楼层</a-select-option>
          <a-select-option value="B1">B1层</a-select-option>
          <a-select-option value="B2">B2层</a-select-option>
          <a-select-option value="B3">B3层</a-select-option>
        </a-select>
        <a-select v-model:value="statusFilter" placeholder="状态筛选" style="width: 120px" @change="handleSearch">
          <a-select-option value="">全部状态</a-select-option>
          <a-select-option value="available">可用</a-select-option>
          <a-select-option value="occupied">已占用</a-select-option>
          <a-select-option value="reserved">已预约</a-select-option>
          <a-select-option value="maintenance">维护中</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 车位列表 -->
    <div class="bg-white rounded-lg shadow">
      <a-table :columns="columns" :data-source="parkingSpaces" :loading="loading" :pagination="pagination" @change="handleTableChange" row-key="id">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'parkTime'">
            <span v-if="record.parkTime">
              {{ record.parkTime }}
              <div class="text-xs text-gray-500">
                {{ getParkingDuration(record.parkTime) }}
              </div>
            </span>
            <span v-else>-</span>
          </template>

          <template v-if="column.key === 'fee'">
            <span v-if="record.fee">¥{{ record.fee }}</span>
            <span v-else>-</span>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button v-if="record.status === 'available'" type="primary" size="small" @click="reserveSpace(record)"> 预约 </a-button>

              <a-button v-if="record.status === 'occupied'" size="small" @click="processExit(record)"> 出场 </a-button>

              <a-button v-if="record.status !== 'maintenance'" size="small" @click="setMaintenance(record)"> 维护 </a-button>

              <a-button v-if="record.status === 'maintenance'" type="primary" size="small" @click="setMaintenance(record, false)"> 恢复 </a-button>

              <a-button size="small" @click="viewSpaceDetail(record)"> 详情 </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 车辆入场弹窗 -->
    <VehicleEntryModal v-model:visible="entryModalVisible" @success="handleEntrySuccess" />

    <!-- 车辆出场弹窗 -->
    <VehicleExitModal v-model:visible="exitModalVisible" @success="handleExitSuccess" />

    <!-- 车位预约弹窗 -->
    <ParkingReservationModal v-model:visible="reservationModalVisible" :space="selectedSpace" @success="handleReservationSuccess" />

    <!-- 车位详情弹窗 -->
    <ParkingSpaceDetailModal v-model:visible="detailModalVisible" :space="selectedSpace" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import {
    CarOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    PercentageOutlined,
    CompassOutlined,
    DollarOutlined,
    LoginOutlined,
    LogoutOutlined,
    CalendarOutlined,
    ReloadOutlined,
  } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';
  import {
    getParkingSpaces,
    getParkingStats,
    getParkingGuidance,
    setSpaceMaintenance,
    type ParkingSpace,
    type ParkingStats,
    type ParkingGuidance,
  } from '/@/api/operations/parking';
  import VehicleEntryModal from './components/VehicleEntryModal.vue';
  import VehicleExitModal from './components/VehicleExitModal.vue';
  import ParkingReservationModal from './components/ParkingReservationModal.vue';
  import ParkingSpaceDetailModal from './components/ParkingSpaceDetailModal.vue';

  // 响应式数据
  const loading = ref(false);
  const parkingSpaces = ref<ParkingSpace[]>([]);
  const stats = ref<ParkingStats>({
    totalSpaces: 0,
    occupiedSpaces: 0,
    availableSpaces: 0,
    reservedSpaces: 0,
    occupancyRate: 0,
    todayIncome: 0,
    monthlyIncome: 0,
    avgParkingDuration: 0,
    peakHours: [],
  });
  const parkingGuidance = ref<ParkingGuidance[]>([]);

  // 筛选条件
  const floorFilter = ref('');
  const statusFilter = ref('');

  // 分页
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 弹窗状态
  const entryModalVisible = ref(false);
  const exitModalVisible = ref(false);
  const reservationModalVisible = ref(false);
  const detailModalVisible = ref(false);
  const selectedSpace = ref<ParkingSpace | null>(null);

  // 表格列定义
  const columns = [
    {
      title: '车位号',
      dataIndex: 'spaceNumber',
      key: 'spaceNumber',
    },
    {
      title: '楼层',
      dataIndex: 'floor',
      key: 'floor',
    },
    {
      title: '区域',
      dataIndex: 'area',
      key: 'area',
    },
    {
      title: '类型',
      key: 'type',
    },
    {
      title: '状态',
      key: 'status',
    },
    {
      title: '车牌号',
      dataIndex: 'vehicleNumber',
      key: 'vehicleNumber',
    },
    {
      title: '车主',
      dataIndex: 'ownerName',
      key: 'ownerName',
    },
    {
      title: '停车时间',
      key: 'parkTime',
    },
    {
      title: '费用',
      key: 'fee',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
    },
  ];

  // 方法
  const loadParkingSpaces = async () => {
    loading.value = true;
    try {
      const params = {
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
        floor: floorFilter.value || undefined,
        status: statusFilter.value || undefined,
      };

      const response = await getParkingSpaces(params);
      parkingSpaces.value = response.records;
      pagination.total = response.total;
    } catch (error) {
      message.error('获取车位列表失败');
    } finally {
      loading.value = false;
    }
  };

  const loadStats = async () => {
    try {
      stats.value = await getParkingStats();
    } catch (error) {
      message.error('获取统计数据失败');
    }
  };

  const loadGuidance = async () => {
    try {
      parkingGuidance.value = await getParkingGuidance();
    } catch (error) {
      message.error('获取停车引导失败');
    }
  };

  const refreshData = () => {
    loadParkingSpaces();
    loadStats();
    loadGuidance();
  };

  const handleSearch = () => {
    pagination.current = 1;
    loadParkingSpaces();
  };

  const handleTableChange = (pag: any) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    loadParkingSpaces();
  };

  const showVehicleEntryModal = () => {
    entryModalVisible.value = true;
  };

  const showVehicleExitModal = () => {
    exitModalVisible.value = true;
  };

  const showReservationModal = () => {
    reservationModalVisible.value = true;
  };

  const reserveSpace = (space: ParkingSpace) => {
    selectedSpace.value = space;
    reservationModalVisible.value = true;
  };

  const processExit = (space: ParkingSpace) => {
    selectedSpace.value = space;
    exitModalVisible.value = true;
  };

  const setMaintenance = async (space: ParkingSpace, maintenance = true) => {
    try {
      await setSpaceMaintenance(space.id, maintenance);
      message.success(maintenance ? '设置维护状态成功' : '恢复正常状态成功');
      refreshData();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const viewSpaceDetail = (space: ParkingSpace) => {
    selectedSpace.value = space;
    detailModalVisible.value = true;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      available: 'green',
      occupied: 'red',
      reserved: 'blue',
      maintenance: 'orange',
    };
    return colors[status] || 'gray';
  };

  const getStatusText = (status: string) => {
    const texts = {
      available: '可用',
      occupied: '已占用',
      reserved: '已预约',
      maintenance: '维护中',
    };
    return texts[status] || status;
  };

  const getTypeColor = (type: string) => {
    const colors = {
      normal: 'blue',
      vip: 'gold',
      disabled: 'purple',
      electric: 'green',
    };
    return colors[type] || 'gray';
  };

  const getTypeText = (type: string) => {
    const texts = {
      normal: '普通',
      vip: 'VIP',
      disabled: '无障碍',
      electric: '充电桩',
    };
    return texts[type] || type;
  };

  const getParkingDuration = (parkTime: string) => {
    const start = dayjs(parkTime);
    const now = dayjs();
    const duration = now.diff(start, 'minute');

    if (duration < 60) {
      return `${duration}分钟`;
    } else {
      const hours = Math.floor(duration / 60);
      const minutes = duration % 60;
      return `${hours}小时${minutes}分钟`;
    }
  };

  const handleEntrySuccess = () => {
    entryModalVisible.value = false;
    refreshData();
  };

  const handleExitSuccess = () => {
    exitModalVisible.value = false;
    refreshData();
  };

  const handleReservationSuccess = () => {
    reservationModalVisible.value = false;
    refreshData();
  };

  // 生命周期
  onMounted(() => {
    refreshData();
  });
</script>
