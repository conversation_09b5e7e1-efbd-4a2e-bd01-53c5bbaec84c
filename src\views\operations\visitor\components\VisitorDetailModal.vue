<template>
  <a-modal
    v-model:visible="visible"
    title="访客详情"
    width="600px"
    :footer="null"
  >
    <div v-if="visitor" class="space-y-4">
      <!-- 基本信息 -->
      <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-3 flex items-center">
          <UserOutlined class="mr-2" />
          基本信息
        </h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center space-x-2">
            <a-avatar v-if="visitor.photo" :src="visitor.photo" :size="40" />
            <a-avatar v-else :size="40">
              <UserOutlined />
            </a-avatar>
            <div>
              <div class="font-medium">{{ visitor.name }}</div>
              <div class="text-sm text-gray-500">{{ visitor.phone }}</div>
            </div>
          </div>
          <div>
            <div class="text-sm text-gray-500">身份证号</div>
            <div>{{ visitor.idCard }}</div>
          </div>
          <div>
            <div class="text-sm text-gray-500">所属公司</div>
            <div>{{ visitor.company }}</div>
          </div>
          <div>
            <div class="text-sm text-gray-500">状态</div>
            <a-tag :color="getStatusColor(visitor.status)">
              {{ getStatusText(visitor.status) }}
            </a-tag>
          </div>
        </div>
      </div>

      <!-- 访问信息 -->
      <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-3 flex items-center">
          <CalendarOutlined class="mr-2" />
          访问信息
        </h3>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <div class="text-sm text-gray-500">被访人</div>
            <div>{{ visitor.visitee }}</div>
          </div>
          <div>
            <div class="text-sm text-gray-500">被访人电话</div>
            <div>{{ visitor.visiteePhone }}</div>
          </div>
          <div>
            <div class="text-sm text-gray-500">访问目的</div>
            <div>{{ getVisitPurposeText(visitor.visitPurpose) }}</div>
          </div>
          <div>
            <div class="text-sm text-gray-500">预约时间</div>
            <div>{{ visitor.visitTime }}</div>
          </div>
          <div>
            <div class="text-sm text-gray-500">预计离开时间</div>
            <div>{{ visitor.expectedLeaveTime }}</div>
          </div>
          <div v-if="visitor.actualLeaveTime">
            <div class="text-sm text-gray-500">实际离开时间</div>
            <div>{{ visitor.actualLeaveTime }}</div>
          </div>
        </div>
      </div>

      <!-- 健康信息 -->
      <div v-if="visitor.temperature || visitor.healthCode" class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-3 flex items-center">
          <SafetyOutlined class="mr-2" />
          健康信息
        </h3>
        <div class="grid grid-cols-2 gap-4">
          <div v-if="visitor.temperature">
            <div class="text-sm text-gray-500">体温</div>
            <div :class="visitor.temperature > 37.3 ? 'text-red-500' : 'text-green-500'">
              {{ visitor.temperature }}°C
            </div>
          </div>
          <div v-if="visitor.healthCode">
            <div class="text-sm text-gray-500">健康码</div>
            <a-tag :color="getHealthCodeColor(visitor.healthCode)">
              {{ getHealthCodeText(visitor.healthCode) }}
            </a-tag>
          </div>
        </div>
      </div>

      <!-- 时间记录 -->
      <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-3 flex items-center">
          <ClockCircleOutlined class="mr-2" />
          时间记录
        </h3>
        <div class="space-y-2">
          <div class="flex justify-between">
            <span class="text-gray-500">创建时间:</span>
            <span>{{ visitor.createTime }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">更新时间:</span>
            <span>{{ visitor.updateTime }}</span>
          </div>
          <div v-if="visitor.status === 'visiting' || visitor.status === 'completed'" class="flex justify-between">
            <span class="text-gray-500">访问时长:</span>
            <span>{{ getVisitDuration() }}</span>
          </div>
        </div>
      </div>

      <!-- 二维码 -->
      <div v-if="visitor.qrCode" class="bg-gray-50 p-4 rounded text-center">
        <h3 class="text-lg font-semibold mb-3">访客二维码</h3>
        <div class="flex justify-center">
          <img :src="visitor.qrCode" alt="访客二维码" class="w-32 h-32" />
        </div>
        <div class="text-sm text-gray-500 mt-2">
          访客可使用此二维码进行快速签到
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import {
    UserOutlined,
    CalendarOutlined,
    SafetyOutlined,
    ClockCircleOutlined,
  } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';
  import type { VisitorInfo } from '/@/api/operations/visitor';

  // Props
  interface Props {
    visible: boolean;
    visitor: VisitorInfo | null;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    'update:visible': [value: boolean];
  }>();

  // 计算属性
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  // 方法
  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'orange',
      approved: 'blue',
      visiting: 'green',
      completed: 'gray',
      rejected: 'red',
    };
    return colors[status] || 'gray';
  };

  const getStatusText = (status: string) => {
    const texts = {
      pending: '待审批',
      approved: '已审批',
      visiting: '访问中',
      completed: '已完成',
      rejected: '已拒绝',
    };
    return texts[status] || status;
  };

  const getVisitPurposeText = (purpose: string) => {
    const texts = {
      business: '商务洽谈',
      meeting: '会议参加',
      interview: '面试',
      maintenance: '设备维护',
      delivery: '物品配送',
      inspection: '检查巡视',
      other: '其他',
    };
    return texts[purpose] || purpose;
  };

  const getHealthCodeColor = (code: string) => {
    const colors = {
      green: 'green',
      yellow: 'orange',
      red: 'red',
    };
    return colors[code] || 'gray';
  };

  const getHealthCodeText = (code: string) => {
    const texts = {
      green: '绿码',
      yellow: '黄码',
      red: '红码',
    };
    return texts[code] || code;
  };

  const getVisitDuration = () => {
    if (!props.visitor) return '';
    
    const startTime = dayjs(props.visitor.visitTime);
    const endTime = props.visitor.actualLeaveTime 
      ? dayjs(props.visitor.actualLeaveTime)
      : dayjs();
    
    const duration = endTime.diff(startTime, 'minute');
    
    if (duration < 60) {
      return `${duration}分钟`;
    } else {
      const hours = Math.floor(duration / 60);
      const minutes = duration % 60;
      return `${hours}小时${minutes}分钟`;
    }
  };
</script>
