<template>
  <div class="relative">
    <!-- 巡检工具栏 -->
    <PatrolToolbar ref="patrolToolbarRef" />

    <!-- 巡检主按钮 -->
    <a-tooltip placement="right" :title="getPatrolTooltip">
      <div
        class="control-btn"
        :class="{
          active: isDrawingActive || isToolbarExpanded,
          disabled:
            isProcessing || globalThreeStore.isFloorSwitching || isPatrolActive || globalThreeStore.isPlayActive || globalThreeStore.transparencyMode,
        }"
        @click="handlePatrolButtonClick"
        data-view-control="patrol"
      >
        <div class="flex flex-col items-center justify-center h-full">
          <LoadingOutlined v-if="isProcessing" class="text-[0.8vw]" />
          <template v-else>
            <component :is="getPatrolIcon" class="text-[0.8vw]" />
            <span class="text-[0.5vw] text-white/80 mt-[0.1vw]">{{ getPatrolText }}</span>
          </template>
        </div>
      </div>
    </a-tooltip>

    <!-- 绘图工具栏组件 (保留用于兼容) -->
    <DrawingToolbar ref="drawingToolbarRef" />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onBeforeUnmount, onMounted } from 'vue';
  import { EditOutlined, LoadingOutlined, RobotOutlined } from '@ant-design/icons-vue';
  import { useGlobalThreeStore } from '../store/globalThreeStore';
  import DrawingToolbar from './DrawingToolbar.vue';
  import PatrolToolbar from './PatrolToolbar.vue';
  import { CustomPatrolController } from '../lib/patrol/CustomPatrolController';
  import { PathDrawingTool } from '../lib/patrol/PathDrawingTool';

  // 状态定义
  const isProcessing = ref(false);
  const isDrawingActive = ref(false);
  const isPatrolActive = ref(false); // 跟踪巡检是否处于活动状态
  const isToolbarExpanded = ref(false); // 工具栏是否展开
  const globalThreeStore = useGlobalThreeStore();
  const drawingToolbarRef = ref<InstanceType<typeof DrawingToolbar> | null>(null);
  const patrolToolbarRef = ref<InstanceType<typeof PatrolToolbar> | null>(null);

  // 计算属性
  const getPatrolIcon = computed(() => {
    if (isDrawingActive.value) {
      return EditOutlined;
    }
    return RobotOutlined;
  });

  const getPatrolText = computed(() => {
    if (isDrawingActive.value) {
      return '绘制中';
    }
    return '巡检';
  });

  const getPatrolTooltip = computed(() => {
    if (globalThreeStore.isFloorSwitching) {
      return '楼层切换中，暂时无法使用巡检功能';
    }
    if (globalThreeStore.isPlayActive) {
      return '播放功能正在运行中，请先停止播放再使用巡检功能';
    }
    if (globalThreeStore.transparencyMode) {
      return '透视模式已启用，请先关闭透视模式再使用巡检功能';
    }
    if (isProcessing.value) {
      return '正在处理巡检请求，请稍候...';
    }
    if (isPatrolActive.value) {
      return '巡检进行中，请使用停止按钮结束巡检';
    }
    if (isToolbarExpanded.value) {
      return '关闭巡检工具栏';
    }
    return '开始巡检（沿路径移动）';
  });

  // 处理巡检按钮点击
  const handlePatrolButtonClick = async () => {
    // 检查是否可以执行操作
    if (
      isProcessing.value ||
      globalThreeStore.isFloorSwitching ||
      isPatrolActive.value ||
      globalThreeStore.isPlayActive ||
      globalThreeStore.transparencyMode
    ) {
      if (globalThreeStore.isFloorSwitching) {
        showMessage('楼层切换中，请等待切换完成后再使用巡检功能', 'warning');
        return;
      }
      if (isPatrolActive.value) {
        showMessage('巡检进行中，请使用停止按钮结束巡检', 'warning');
        return;
      }
      if (globalThreeStore.isPlayActive) {
        showMessage('播放功能正在运行中，请先停止播放再使用巡检功能', 'warning');
        return;
      }
      if (globalThreeStore.transparencyMode) {
        showMessage('透视模式已启用，请先关闭透视模式再使用巡检功能', 'warning');
        return;
      }
      return;
    }

    if (!globalThreeStore.canUserInteract) {
      showMessage('系统正在加载中，请稍候再试...', 'warning');
      return;
    }

    // 切换工具栏显示状态
    toggleToolbar();
  };

  // 切换工具栏显示状态
  const toggleToolbar = () => {
    if (patrolToolbarRef.value) {
      if (isToolbarExpanded.value) {
        // 收起工具栏，但不停止正在进行的巡检
        patrolToolbarRef.value.hideToolbar();
        isToolbarExpanded.value = false;
        console.log('[PatrolButton] 工具栏已收起，巡检将继续进行');
      } else {
        // 展开工具栏时，允许使用巡检功能
        patrolToolbarRef.value.showToolbar();
        isToolbarExpanded.value = true;
        console.log('[PatrolButton] 工具栏已展开，可以使用巡检功能');
      }
    }
  };

  // 显示消息的安全方法 - 禁用以保持干净的界面
  const showMessage = (text: string, _type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
    // Message notifications disabled for clean interface during patrol
    console.log(`[PatrolButton] ${text}`);
  };

  // 监听绘制和巡检事件
  const handleDrawingStarted = () => {
    isDrawingActive.value = true;
    isProcessing.value = false;
  };

  const handleDrawingCompleted = () => {
    // 绘制完成后，状态会由巡检状态接管
    // 当绘制完成时，巡检会自动开始
    isPatrolActive.value = true;
  };

  const handleDrawingCancelled = () => {
    isDrawingActive.value = false;
    isProcessing.value = false;
  };

  const handleDrawingFailed = () => {
    isDrawingActive.value = false;
    isProcessing.value = false;
  };

  const handlePatrolStopped = () => {
    isDrawingActive.value = false;
    isProcessing.value = false;
    isPatrolActive.value = false; // 重置巡检活动状态
  };

  // 处理播放功能激活事件
  const handlePlayActivated = () => {
    // 如果巡检正在进行中，需要停止巡检
    if (isPatrolActive.value) {
      // 通知巡检工具栏停止巡检
      if (patrolToolbarRef.value) {
        patrolToolbarRef.value.stopPatrol();
      }

      // 更新状态
      isDrawingActive.value = false;
      isProcessing.value = false;
      isPatrolActive.value = false;

      showMessage('播放功能已激活，巡检功能已自动停止', 'info');
    }
  };

  // 处理透视模式激活事件
  const handleTransparencyActivated = (event: CustomEvent) => {
    // 如果巡检正在进行中或工具栏已展开，需要停止巡检并收起工具栏
    if (isPatrolActive.value || isToolbarExpanded.value || isDrawingActive.value) {
      // 通知巡检工具栏停止巡检
      if (patrolToolbarRef.value) {
        // 停止巡检
        patrolToolbarRef.value.stopPatrol();

        // 如果工具栏已展开，收起工具栏
        if (isToolbarExpanded.value) {
          patrolToolbarRef.value.hideToolbar();
          isToolbarExpanded.value = false;
        }
      }

      // 更新状态
      isDrawingActive.value = false;
      isProcessing.value = false;
      isPatrolActive.value = false;

      // 显示消息（如果有）
      if (event.detail && event.detail.message) {
        showMessage(event.detail.message, 'info');
      } else {
        showMessage('透视模式已激活，巡检功能已自动停止', 'info');
      }
    }
  };

  // 添加事件监听
  onMounted(() => {
    window.addEventListener('patrol-drawing-started', handleDrawingStarted);
    window.addEventListener('patrol-drawing-completed', handleDrawingCompleted);
    window.addEventListener('patrol-drawing-cancelled', handleDrawingCancelled);
    window.addEventListener('patrol-drawing-failed', handleDrawingFailed);
    window.addEventListener('patrol-stopped', handlePatrolStopped);
    window.addEventListener('play-activated', handlePlayActivated);
    window.addEventListener('transparency-activated', handleTransparencyActivated as EventListener);
  });

  // 组件卸载时清理
  onBeforeUnmount(() => {
    // 确保关闭工具栏并停止所有巡检相关功能
    if (patrolToolbarRef.value) {
      patrolToolbarRef.value.hideToolbar();
    }

    // 确保关闭绘图工具栏（兼容）
    if (drawingToolbarRef.value) {
      drawingToolbarRef.value.hideToolbar();
    }

    // 确保停止巡检
    try {
      const patrolController = CustomPatrolController.getInstance();
      if (isPatrolActive.value) {
        patrolController.stop();
        console.log('[PatrolButton] 组件卸载时停止巡检');
      }
    } catch (error) {
      console.error('[PatrolButton] 组件卸载时停止巡检失败:', error);
    }

    // 确保取消绘制
    try {
      const drawingTool = PathDrawingTool.getInstance();
      if (isDrawingActive.value) {
        drawingTool.cancelDrawing();
        console.log('[PatrolButton] 组件卸载时取消路径绘制');
      }
    } catch (error) {
      console.error('[PatrolButton] 组件卸载时取消路径绘制失败:', error);
    }

    // 移除事件监听
    window.removeEventListener('patrol-drawing-started', handleDrawingStarted);
    window.removeEventListener('patrol-drawing-completed', handleDrawingCompleted);
    window.removeEventListener('patrol-drawing-cancelled', handleDrawingCancelled);
    window.removeEventListener('patrol-drawing-failed', handleDrawingFailed);
    window.removeEventListener('patrol-stopped', handlePatrolStopped);
    window.removeEventListener('play-activated', handlePlayActivated);
    window.removeEventListener('transparency-activated', handleTransparencyActivated as EventListener);

    // 重置状态
    isDrawingActive.value = false;
    isPatrolActive.value = false;
    isProcessing.value = false;
    isToolbarExpanded.value = false;
  });
</script>

<style scoped>
  .patrol-toolbar {
    position: absolute;
    right: 100%;
    top: 0;
  }
</style>
