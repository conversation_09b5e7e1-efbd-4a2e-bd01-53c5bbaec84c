<template>
  <button
    :class="buttonClasses"
    :disabled="disabled"
    @click="handleClick"
  >
    <component
      v-if="icon"
      :is="iconComponent"
      :class="iconClasses"
    />
    <span v-if="$slots.default" :class="textClasses">
      <slot />
    </span>
  </button>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import {
    PlayCircleOutlined,
    PauseOutlined,
    LeftOutlined,
    RightOutlined,
    EyeOutlined,
  } from '@ant-design/icons-vue';

  // Props
  interface Props {
    type?: 'default' | 'primary';
    variant?: 'solid' | 'ghost';
    icon?: 'play' | 'pause' | 'left' | 'right' | 'eye';
    disabled?: boolean;
    size?: 'small' | 'medium';
  }

  const props = withDefaults(defineProps<Props>(), {
    type: 'default',
    variant: 'solid',
    disabled: false,
    size: 'small',
  });

  // Emits
  const emit = defineEmits<{
    click: [event: MouseEvent];
  }>();

  // 图标映射
  const iconMap = {
    play: PlayCircleOutlined,
    pause: PauseOutlined,
    left: LeftOutlined,
    right: RightOutlined,
    eye: EyeOutlined,
  };

  // 计算属性
  const iconComponent = computed(() => {
    return props.icon ? iconMap[props.icon] : null;
  });

  const buttonClasses = computed(() => {
    const baseClasses = [
      'inline-flex',
      'items-center',
      'justify-center',
      'rounded',
      'font-medium',
      'transition-all',
      'duration-200',
      'border',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-[#3B8EE6]/50',
    ];

    // 尺寸
    if (props.size === 'small') {
      baseClasses.push('px-3', 'py-1.5', 'text-xs', 'min-h-[28px]');
    } else {
      baseClasses.push('px-4', 'py-2', 'text-sm', 'min-h-[32px]');
    }

    // 类型和变体
    if (props.disabled) {
      baseClasses.push(
        'bg-gray-600',
        'border-gray-600',
        'text-gray-400',
        'cursor-not-allowed'
      );
    } else if (props.type === 'primary') {
      if (props.variant === 'ghost') {
        baseClasses.push(
          'bg-transparent',
          'border-[#3B8EE6]',
          'text-[#3B8EE6]',
          'hover:bg-[#3B8EE6]/10',
          'hover:border-[#4A9EF7]',
          'hover:text-[#4A9EF7]'
        );
      } else {
        baseClasses.push(
          'bg-[#3B8EE6]',
          'border-[#3B8EE6]',
          'text-white',
          'hover:bg-[#4A9EF7]',
          'hover:border-[#4A9EF7]',
          'active:bg-[#2A7DD5]'
        );
      }
    } else {
      baseClasses.push(
        'bg-[#2a3441]',
        'border-[#3a4551]',
        'text-gray-300',
        'hover:bg-[#3a4551]',
        'hover:border-[#4a5561]',
        'hover:text-white',
        'active:bg-[#1a2332]'
      );
    }

    return baseClasses;
  });

  const iconClasses = computed(() => {
    const classes = [];
    if (props.size === 'small') {
      classes.push('text-sm');
    } else {
      classes.push('text-base');
    }
    return classes;
  });

  const textClasses = computed(() => {
    const classes = [];
    if (props.icon) {
      classes.push('ml-1.5');
    }
    return classes;
  });

  // 方法
  const handleClick = (event: MouseEvent) => {
    if (!props.disabled) {
      emit('click', event);
    }
  };
</script>

<style scoped>
  button {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  button:hover:not(:disabled) {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }

  button:active:not(:disabled) {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transform: translateY(1px);
  }
</style>
