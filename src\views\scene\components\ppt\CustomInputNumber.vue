<template>
  <div class="custom-input-number relative" :style="style">
    <input
      ref="inputRef"
      :class="inputClasses"
      :value="displayValue"
      :disabled="disabled"
      :placeholder="placeholder"
      @input="handleInput"
      @blur="handleBlur"
      @keydown="handleKeydown"
      @focus="handleFocus"
    />
    <div class="absolute right-1 top-1/2 transform -translate-y-1/2 flex flex-col">
      <button
        :class="stepButtonClasses"
        :disabled="disabled || (max !== undefined && currentValue >= max)"
        @click="increment"
        @mousedown.prevent
      >
        <UpOutlined class="text-xs" />
      </button>
      <button
        :class="stepButtonClasses"
        :disabled="disabled || (min !== undefined && currentValue <= min)"
        @click="decrement"
        @mousedown.prevent
      >
        <DownOutlined class="text-xs" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { UpOutlined, DownOutlined } from '@ant-design/icons-vue';

  // Props
  interface Props {
    value?: number;
    min?: number;
    max?: number;
    step?: number;
    disabled?: boolean;
    placeholder?: string;
    style?: Record<string, any>;
  }

  const props = withDefaults(defineProps<Props>(), {
    step: 1,
    disabled: false,
    placeholder: '',
  });

  // Emits
  const emit = defineEmits<{
    'update:value': [value: number];
    change: [value: number];
    pressEnter: [event: KeyboardEvent];
  }>();

  // 响应式数据
  const inputRef = ref<HTMLInputElement>();
  const currentValue = ref(props.value || 0);
  const isFocused = ref(false);

  // 计算属性
  const displayValue = computed(() => {
    return currentValue.value.toString();
  });

  const inputClasses = computed(() => {
    const baseClasses = [
      'w-full',
      'px-3',
      'pr-8',
      'py-1.5',
      'text-xs',
      'min-h-[28px]',
      'bg-[#2a3441]',
      'border',
      'border-[#3a4551]',
      'rounded',
      'text-gray-300',
      'transition-all',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-[#3B8EE6]/50',
    ];

    if (props.disabled) {
      baseClasses.push(
        'bg-gray-600',
        'border-gray-600',
        'text-gray-400',
        'cursor-not-allowed'
      );
    } else {
      baseClasses.push(
        'hover:bg-[#3a4551]',
        'hover:border-[#4a5561]',
        'hover:text-white'
      );
    }

    if (isFocused.value) {
      baseClasses.push('border-[#3B8EE6]', 'bg-[#3a4551]');
    }

    return baseClasses;
  });

  const stepButtonClasses = computed(() => [
    'w-4',
    'h-3',
    'flex',
    'items-center',
    'justify-center',
    'text-gray-400',
    'hover:text-[#3B8EE6]',
    'hover:bg-[#3a4551]',
    'rounded-sm',
    'transition-colors',
    'duration-150',
    'disabled:text-gray-600',
    'disabled:cursor-not-allowed',
    'disabled:hover:bg-transparent',
  ]);

  // 方法
  const validateValue = (value: number): number => {
    let validValue = value;
    
    if (props.min !== undefined && validValue < props.min) {
      validValue = props.min;
    }
    
    if (props.max !== undefined && validValue > props.max) {
      validValue = props.max;
    }
    
    return validValue;
  };

  const updateValue = (value: number) => {
    const validValue = validateValue(value);
    currentValue.value = validValue;
    emit('update:value', validValue);
    emit('change', validValue);
  };

  const handleInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const value = parseFloat(target.value);
    
    if (!isNaN(value)) {
      currentValue.value = value;
    }
  };

  const handleBlur = () => {
    isFocused.value = false;
    const validValue = validateValue(currentValue.value);
    if (validValue !== currentValue.value) {
      updateValue(validValue);
    } else {
      emit('update:value', currentValue.value);
      emit('change', currentValue.value);
    }
  };

  const handleFocus = () => {
    isFocused.value = true;
  };

  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Enter') {
      emit('pressEnter', event);
      inputRef.value?.blur();
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      increment();
    } else if (event.key === 'ArrowDown') {
      event.preventDefault();
      decrement();
    }
  };

  const increment = () => {
    if (props.disabled) return;
    const newValue = currentValue.value + props.step;
    updateValue(newValue);
  };

  const decrement = () => {
    if (props.disabled) return;
    const newValue = currentValue.value - props.step;
    updateValue(newValue);
  };

  // 监听props.value变化
  watch(
    () => props.value,
    (newValue) => {
      if (newValue !== undefined && newValue !== currentValue.value) {
        currentValue.value = newValue;
      }
    },
    { immediate: true }
  );
</script>

<style scoped>
  .custom-input-number {
    position: relative;
  }

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
</style>
