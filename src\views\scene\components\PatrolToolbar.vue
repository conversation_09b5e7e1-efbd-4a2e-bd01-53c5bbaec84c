<template>
  <div
    v-if="isVisible"
    class="patrol-toolbar flex items-center h-[2.2vw] bg-[rgba(23,43,77,0.8)] border border-[rgba(36,108,249,0.3)] rounded-l-[0.3vw] overflow-hidden transition-all duration-300 z-50"
    :class="{ expanded: isExpanded }"
    :style="{
      width: isExpanded ? '12vw' : '0',
      opacity: isExpanded ? '1' : '0.8',
      transform: isExpanded ? 'translateX(0)' : 'translateX(1vw)',
    }"
    @click.stop.prevent
  >
    <div class="flex items-center justify-around px-[0.5vw] w-full h-full gap-[0.2vw] pointer-events-auto">
      <!-- 绘制路径按钮 -->
      <a-tooltip placement="top" title="绘制路径">
        <div class="toolbar-btn pointer-events-auto" :class="{ active: isDrawingActive }" @click.stop.prevent="handleDrawPathClick">
          <EditOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 清除路径按钮 -->
      <a-tooltip placement="top" title="清除路径">
        <div class="toolbar-btn pointer-events-auto" :class="{ disabled: !hasDrawnPath || isPatrolling }" @click.stop.prevent="handleClearPathClick">
          <DeleteOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 撤回路径点按钮 -->
      <a-tooltip placement="top" title="撤回上一个路径点">
        <div class="toolbar-btn pointer-events-auto" :class="{ disabled: !canUndo || !isDrawingActive }" @click.stop.prevent="handleUndoClick">
          <UndoOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 重做路径点按钮 -->
      <a-tooltip placement="top" title="重做路径点">
        <div class="toolbar-btn pointer-events-auto" :class="{ disabled: !canRedo || !isDrawingActive }" @click.stop.prevent="handleRedoClick">
          <RedoOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 完成绘制按钮 -->
      <a-tooltip placement="top" title="完成绘制">
        <div
          class="toolbar-btn pointer-events-auto"
          :class="{
            'active-complete': isDrawingActive && canCompleteDrawing,
            disabled: !isDrawingActive || !canCompleteDrawing,
          }"
          @click.stop.prevent="handleCompleteDrawingClick"
        >
          <CheckOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 保存路径按钮 -->
      <a-tooltip placement="top" title="保存路径">
        <div
          class="toolbar-btn pointer-events-auto"
          :class="{ disabled: !hasDrawnPath || isPatrolling || isDrawingActive }"
          @click.stop.prevent="handleSavePathClick"
        >
          <SaveOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 加载路径按钮 -->
      <a-tooltip placement="top" title="加载路径">
        <div class="toolbar-btn pointer-events-auto" :class="{ disabled: isPatrolling || isDrawingActive }" @click.stop.prevent="handleLoadPathClick">
          <FolderOpenOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 开始/暂停巡检按钮 -->
      <a-tooltip placement="top" :title="isPatrolling ? (isPaused ? '继续巡检' : '暂停巡检') : '开始巡检'">
        <div
          class="toolbar-btn pointer-events-auto"
          :class="{
            active: isPatrolling && !isPaused,
            disabled: (!hasDrawnPath && !isPatrolling) || isProcessing || isDrawingActive,
          }"
          @click.stop.prevent="handleStartPauseClick"
        >
          <component
            :is="isPatrolling ? (isPaused ? PlayCircleOutlined : PauseCircleOutlined) : PlayCircleOutlined"
            class="text-[0.8vw] text-white"
          />
        </div>
      </a-tooltip>

      <!-- 停止巡检按钮 -->
      <a-tooltip placement="top" title="停止巡检">
        <div class="toolbar-btn pointer-events-auto" :class="{ disabled: !isPatrolling || isProcessing }" @click.stop.prevent="handleStopClick">
          <StopOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>
    </div>

    <!-- 保存路径对话框 -->
    <a-modal v-model:visible="showSaveDialog" title="保存巡检路径" @ok="saveCurrentPath" okText="保存" cancelText="取消" :maskClosable="false">
      <div class="mb-[1vw]">
        <p class="mb-[0.5vw]">请输入路径名称：</p>
        <a-input v-model:value="pathNameInput" placeholder="请输入路径名称" />
      </div>
    </a-modal>

    <!-- 加载路径对话框 -->
    <a-modal
      v-model:visible="showLoadDialog"
      title="加载巡检路径"
      @ok="loadSelectedPath"
      okText="加载"
      cancelText="取消"
      :maskClosable="false"
      :okButtonProps="{ disabled: selectedPathIndex < 0 }"
    >
      <div class="mb-[1vw]">
        <p class="mb-[0.5vw]" v-if="savedPaths.length > 0">请选择要加载的路径：</p>
        <p class="mb-[0.5vw] text-gray-500" v-else>暂无保存的路径</p>

        <div class="max-h-[50vh] overflow-y-auto">
          <a-list item-layout="horizontal" :data-source="savedPaths">
            <template #renderItem="{ item, index }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <div class="flex items-center">
                      <a-radio :value="index" :checked="selectedPathIndex === index" @change="() => (selectedPathIndex = index)">
                        {{ item.name }}
                      </a-radio>
                    </div>
                  </template>
                  <template #description>
                    <div class="text-[0.6vw] text-gray-400"> 路径点数量: {{ item.points.length }} 个 </div>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a-button type="link" danger @click="deletePathConfirm(index)">
                    <DeleteOutlined />
                  </a-button>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted } from 'vue';
  import * as THREE from 'three';
  import {
    EditOutlined,
    DeleteOutlined,
    UndoOutlined,
    RedoOutlined,
    PlayCircleOutlined,
    PauseCircleOutlined,
    StopOutlined,
    SaveOutlined,
    FolderOpenOutlined,
    CheckOutlined,
  } from '@ant-design/icons-vue';
  import { message, Modal } from 'ant-design-vue';
  import { PathDrawingTool, PathPoint, DrawingToolState } from '../lib/patrol/PathDrawingTool';
  import { CustomPatrolController, PatrolConfig } from '../lib/patrol/CustomPatrolController';
  import { useGlobalThreeStore } from '../store/globalThreeStore';
  import { HighPerformanceTransparencyManager } from '../lib/HighPerformanceTransparencyManager';
  import { SceneManager } from '../lib/SceneManager';

  // 状态定义
  const isVisible = ref(false);
  const isExpanded = ref(false);
  const isDrawingActive = ref(false);
  const isPatrolling = ref(false);
  const isPaused = ref(false);
  const isProcessing = ref(false);
  const hasDrawnPath = ref(false);
  const canUndo = ref(false);
  const canRedo = ref(false);
  const canCompleteDrawing = ref(false); // 是否可以完成绘制（至少有2个点）
  // 全局Three状态存储
  const globalThreeStore = useGlobalThreeStore();

  // 路径保存相关状态
  const showSaveDialog = ref(false);
  const showLoadDialog = ref(false);
  const pathNameInput = ref('');
  const savedPaths = ref<Array<{ name: string; points: any[]; config: PatrolConfig }>>([]);
  const selectedPathIndex = ref(-1);

  // 本地存储键名
  const STORAGE_KEY = 'savedPatrolPaths';

  // 显示工具栏
  const showToolbar = () => {
    isVisible.value = true;
    // 使用requestAnimationFrame确保DOM更新后再设置展开状态
    requestAnimationFrame(() => {
      isExpanded.value = true;
      // 触发自定义事件通知其他组件工具栏已展开
      window.dispatchEvent(new CustomEvent('patrol-toolbar-expanded'));
      console.log('[PatrolToolbar] 工具栏已展开，已触发patrol-toolbar-expanded事件');
    });
  };

  // 隐藏工具栏
  const hideToolbar = () => {
    isExpanded.value = false;
    // 触发自定义事件通知其他组件工具栏已收起
    window.dispatchEvent(new CustomEvent('patrol-toolbar-collapsed'));
    console.log('[PatrolToolbar] 工具栏已收起，已触发patrol-toolbar-collapsed事件');

    // 只有在没有正在进行的巡检时，才清理巡检相关功能
    if (!isPatrolling.value) {
      // 清理绘制路径等非巡检功能
      cleanupNonPatrolFunctions();
    } else {
      console.log('[PatrolToolbar] 检测到巡检正在进行中，收起工具栏不会停止巡检');
    }

    // 使用setTimeout确保动画完成后再隐藏元素
    setTimeout(() => {
      isVisible.value = false;
    }, 300);
  };

  // 清理所有巡检相关功能
  const cleanupPatrolFunctions = () => {
    console.log('[PatrolToolbar] 清理所有巡检相关功能');

    // 1. 如果正在绘制路径，取消绘制
    if (isDrawingActive.value) {
      try {
        const drawingTool = PathDrawingTool.getInstance();
        // 获取当前透明模式状态
        const globalThreeStore = useGlobalThreeStore();
        const isTransparencyActive = globalThreeStore.transparencyMode;

        // 传入true表示强制重新应用透明效果
        drawingTool.cancelDrawing(true);
        isDrawingActive.value = false;
        // 触发自定义事件通知父组件绘制已取消
        window.dispatchEvent(new CustomEvent('patrol-drawing-cancelled'));
        console.log('[PatrolToolbar] 已取消路径绘制，透视模式状态:', isTransparencyActive ? '启用' : '禁用');
      } catch (error) {
        console.error('[PatrolToolbar] 取消路径绘制时发生错误:', error);
      }
    }

    // 2. 如果正在巡检，停止巡检
    if (isPatrolling.value) {
      try {
        const patrolController = CustomPatrolController.getInstance();
        patrolController.stop();
        // 状态更新会通过onStop事件回调处理
        console.log('[PatrolToolbar] 已停止巡检');
      } catch (error) {
        console.error('[PatrolToolbar] 停止巡检时发生错误:', error);
        // 确保状态正确重置
        isPatrolling.value = false;
        isPaused.value = false;
      }
    }

    // 3. 重置处理状态
    isProcessing.value = false;

    // 4. 重置完成按钮状态
    canCompleteDrawing.value = false;

    // 5. 更新撤销/重做状态
    updateUndoRedoState();
  };

  // 清理非巡检功能（仅清理绘制路径等，不停止巡检）
  const cleanupNonPatrolFunctions = () => {
    console.log('[PatrolToolbar] 清理非巡检功能（保持巡检继续运行）');

    // 1. 如果正在绘制路径，取消绘制
    if (isDrawingActive.value) {
      try {
        const drawingTool = PathDrawingTool.getInstance();
        // 获取当前透明模式状态
        const globalThreeStore = useGlobalThreeStore();
        const isTransparencyActive = globalThreeStore.transparencyMode;

        // 传入true表示强制重新应用透明效果
        drawingTool.cancelDrawing(true);
        isDrawingActive.value = false;
        // 触发自定义事件通知父组件绘制已取消
        window.dispatchEvent(new CustomEvent('patrol-drawing-cancelled'));
        console.log('[PatrolToolbar] 已取消路径绘制，透视模式状态:', isTransparencyActive ? '启用' : '禁用');
      } catch (error) {
        console.error('[PatrolToolbar] 取消路径绘制时发生错误:', error);
      }
    }

    // 2. 重置处理状态
    isProcessing.value = false;

    // 3. 重置完成按钮状态
    canCompleteDrawing.value = false;

    // 4. 更新撤销/重做状态
    updateUndoRedoState();

    // 5. 处理透明状态（但不影响巡检）
    try {
      // 获取当前透明模式状态
      const globalThreeStore = useGlobalThreeStore();
      const isTransparencyActive = globalThreeStore.transparencyMode;
      console.log(`[PatrolToolbar] 清理高亮效果，当前透视模式状态: ${isTransparencyActive ? '启用' : '禁用'}`);

      // 获取绘制工具实例
      const drawingTool = PathDrawingTool.getInstance();

      // 使用反射访问私有方法（不推荐，但在这种情况下是必要的）
      // @ts-ignore - 忽略TypeScript的私有属性访问警告
      if (typeof drawingTool['_removeHighlights'] === 'function') {
        // 调用_removeHighlights方法，传入当前透明模式状态，而不是强制重新应用
        // @ts-ignore
        drawingTool['_removeHighlights'](isTransparencyActive);
        console.log('[PatrolToolbar] 已清除所有高亮效果，尊重当前透明模式状态:', isTransparencyActive ? '启用' : '禁用');
      } else {
        console.warn('[PatrolToolbar] 无法访问_removeHighlights方法，尝试备用方案');

        // 备用方案：只在当前处于透明模式时应用透明效果
        if (isTransparencyActive) {
          console.log('[PatrolToolbar] 使用备用方案重新应用透明效果');
          try {
            // 使用更长的延迟，确保其他操作已完成
            setTimeout(() => {
              try {
                const transparencyManager = HighPerformanceTransparencyManager.getInstance();
                transparencyManager.toggleTransparency(true);
                console.log('[PatrolToolbar] 备用方案透明效果应用成功');
              } catch (innerError) {
                console.error('[PatrolToolbar] 备用方案在setTimeout中应用透明效果失败:', innerError);
              }
            }, 100);
          } catch (error) {
            console.error('[PatrolToolbar] 备用方案应用透明效果失败:', error);
          }
        } else {
          console.log('[PatrolToolbar] 当前不处于透明模式，不应用透明效果');
        }
      }

      // 请求渲染以确保视觉效果更新
      SceneManager.getInstance().needsRender = true;
    } catch (error) {
      console.error('[PatrolToolbar] 处理透明状态时发生错误:', error);
    }
  };

  // 绘制路径
  const handleDrawPathClick = async (event: MouseEvent) => {
    // 阻止事件冒泡，防止点击按钮时同时触发3D场景中的地板点击事件
    event.preventDefault();
    event.stopPropagation();

    if (isProcessing.value || isPatrolling.value) return;

    try {
      isProcessing.value = true;

      // 检查当前是否处于透明模式
      const isTransparencyActive = globalThreeStore.transparencyMode;
      if (isTransparencyActive) {
        console.log('[PatrolToolbar] 检测到透视模式已启用，确保在绘制路径操作中保持透视效果');
      }

      // 获取绘制工具
      const drawingTool = PathDrawingTool.getInstance();

      // 如果已经在绘制中，则取消绘制（实现切换功能）
      if (isDrawingActive.value) {
        // 传入true表示强制重新应用透明效果
        drawingTool.cancelDrawing(true);
        isDrawingActive.value = false;
        isProcessing.value = false;
        // 触发自定义事件通知父组件绘制已取消
        window.dispatchEvent(new CustomEvent('patrol-drawing-cancelled'));

        console.log('[PatrolToolbar] 取消绘制完成，透视模式状态:', isTransparencyActive ? '启用' : '禁用');
        return;
      }

      // 设置事件回调
      const events = {
        onStart: () => {
          isDrawingActive.value = true;
          isProcessing.value = false;
          updateUndoRedoState();
          // 触发自定义事件通知父组件绘制已开始
          window.dispatchEvent(new CustomEvent('patrol-drawing-started'));
        },
        onPointAdded: (_point: PathPoint, index: number) => {
          updateUndoRedoState();
          // 当路径点数量达到2个时，启用完成按钮
          canCompleteDrawing.value = index >= 1; // index从0开始，所以index=1表示有2个点
        },
        onComplete: (_points: PathPoint[]) => {
          isDrawingActive.value = false;
          hasDrawnPath.value = true;
          updateUndoRedoState();
          // 触发自定义事件通知父组件绘制已完成
          window.dispatchEvent(new CustomEvent('patrol-drawing-completed'));

          // 自动开始巡检
          startPatrol();
        },
        onCancel: () => {
          isDrawingActive.value = false;
          updateUndoRedoState();
          // 触发自定义事件通知父组件绘制已取消
          window.dispatchEvent(new CustomEvent('patrol-drawing-cancelled'));
        },
      };

      // 开始绘制
      drawingTool.startDrawing(events);
    } catch (error) {
      console.error('开始绘制失败:', error);
      isDrawingActive.value = false;
      isProcessing.value = false;
    }
  };

  // 清除路径
  const handleClearPathClick = (event: MouseEvent) => {
    // 阻止事件冒泡，防止点击按钮时同时触发3D场景中的地板点击事件
    event.preventDefault();
    event.stopPropagation();

    if (isPatrolling.value || !hasDrawnPath.value) return;

    const drawingTool = PathDrawingTool.getInstance();
    drawingTool.clearPath();
    hasDrawnPath.value = false;
    updateUndoRedoState();
  };

  // 撤回路径点
  const handleUndoClick = (event: MouseEvent) => {
    // 阻止事件冒泡，防止点击按钮时同时触发3D场景中的地板点击事件
    event.preventDefault();
    event.stopPropagation();

    if (!isDrawingActive.value || !canUndo.value) return;

    const drawingTool = PathDrawingTool.getInstance();
    drawingTool.undoLastPoint();
    updateUndoRedoState();
  };

  // 重做路径点
  const handleRedoClick = (event: MouseEvent) => {
    // 阻止事件冒泡，防止点击按钮时同时触发3D场景中的地板点击事件
    event.preventDefault();
    event.stopPropagation();

    if (!isDrawingActive.value || !canRedo.value) return;

    const drawingTool = PathDrawingTool.getInstance();
    drawingTool.redoPoint();
    updateUndoRedoState();
  };

  // 开始/暂停巡检
  const handleStartPauseClick = (event: MouseEvent) => {
    // 阻止事件冒泡，防止点击按钮时同时触发3D场景中的地板点击事件
    event.preventDefault();
    event.stopPropagation();

    console.log('[PatrolToolbar] 开始/暂停巡检按钮点击', {
      isProcessing: isProcessing.value,
      isDrawingActive: isDrawingActive.value,
      isPatrolling: isPatrolling.value,
      isPaused: isPaused.value,
      hasDrawnPath: hasDrawnPath.value,
    });

    // 先重置处理状态，防止卡在处理中状态
    if (isProcessing.value && !isPatrolling.value) {
      console.log('[PatrolToolbar] 检测到处理状态卡住，重置处理状态');
      isProcessing.value = false;
    }

    if (isProcessing.value || isDrawingActive.value) {
      console.log('[PatrolToolbar] 无法开始巡检：正在处理中或正在绘制路径');
      return;
    }

    if (isPatrolling.value) {
      if (isPaused.value) {
        console.log('[PatrolToolbar] 继续巡检');
        resumePatrol();
      } else {
        console.log('[PatrolToolbar] 暂停巡检');
        pausePatrol();
      }
    } else if (hasDrawnPath.value) {
      console.log('[PatrolToolbar] 开始巡检');
      startPatrol();
    } else {
      console.log('[PatrolToolbar] 无法开始巡检：没有绘制路径');
      message.warning('请先绘制或加载巡检路径');
    }
  };

  // 开始巡检
  const startPatrol = () => {
    try {
      console.log('[PatrolToolbar] 开始执行startPatrol方法');
      isProcessing.value = true;

      // 检查播放功能是否激活
      if (globalThreeStore.isPlayActive) {
        console.warn('[PatrolToolbar] 无法开始巡检：播放功能正在运行中');
        message.warning('播放功能正在运行中，请先停止播放再使用巡检功能');
        isProcessing.value = false;
        return;
      }

      const patrolController = CustomPatrolController.getInstance();
      console.log('[PatrolToolbar] 获取到巡检控制器实例');

      // 检查路径点是否有效 - 首先检查PathDrawingTool中的路径点
      const drawingTool = PathDrawingTool.getInstance();
      const pathPoints = drawingTool.getPathPoints();
      console.log('[PatrolToolbar] PathDrawingTool中的路径点数量:', pathPoints.length);

      // 如果PathDrawingTool中有足够的路径点，使用它们
      if (pathPoints.length >= 2) {
        // 设置路径点到巡检控制器
        patrolController.setPathPoints(pathPoints);
        console.log('[PatrolToolbar] 已将PathDrawingTool中的路径点设置到巡检控制器');
      } else {
        // 如果PathDrawingTool中没有足够的路径点，检查巡检控制器中是否已有路径点
        // 这种情况通常发生在从已保存的路径加载后
        console.log('[PatrolToolbar] PathDrawingTool中路径点不足，检查巡检控制器中是否已有路径点');

        // 获取巡检控制器中的插值路径点数量（这是一个间接方法，因为没有直接的API）
        // 我们可以通过检查CustomPatrolController的私有属性，但这不是推荐的做法
        // 相反，我们可以尝试直接启动巡检，让巡检控制器自己检查路径点
      }

      // 设置事件回调
      const events = {
        onStart: () => {
          console.log('[PatrolToolbar] 巡检已开始');
          isPatrolling.value = true;
          isPaused.value = false;
          isProcessing.value = false;
        },
        onPause: () => {
          console.log('[PatrolToolbar] 巡检已暂停');
          isPaused.value = true;
        },
        onResume: () => {
          console.log('[PatrolToolbar] 巡检已恢复');
          isPaused.value = false;
        },
        onStop: () => {
          console.log('[PatrolToolbar] 巡检已停止');
          isPatrolling.value = false;
          isPaused.value = false;
          // 触发自定义事件通知父组件巡检已停止
          window.dispatchEvent(new CustomEvent('patrol-stopped'));
        },
      };

      // 开始巡检
      console.log('[PatrolToolbar] 调用巡检控制器的start方法');
      patrolController.start(events);

      // 如果巡检没有成功启动，isProcessing会保持为true，所以我们需要在一段时间后检查
      // 这是一个简单的解决方案，确保UI不会卡在加载状态
      setTimeout(() => {
        if (isProcessing.value && !isPatrolling.value) {
          console.warn('[PatrolToolbar] 巡检未能成功启动，重置处理状态');
          isProcessing.value = false;
          message.warning('无法开始巡检，请确保已加载有效的路径');
        }
      }, 1000);
    } catch (error) {
      console.error('[PatrolToolbar] 开始巡检时发生错误:', error);
      isProcessing.value = false;
      message.error('开始巡检失败');
    }
  };

  // 暂停巡检
  const pausePatrol = () => {
    if (!isPatrolling.value || isPaused.value) return;

    const patrolController = CustomPatrolController.getInstance();
    patrolController.pause();
  };

  // 继续巡检
  const resumePatrol = () => {
    if (!isPatrolling.value || !isPaused.value) return;

    const patrolController = CustomPatrolController.getInstance();
    patrolController.resume();
  };

  // 停止巡检
  const handleStopClick = (event: MouseEvent) => {
    // 阻止事件冒泡，防止点击按钮时同时触发3D场景中的地板点击事件
    event.preventDefault();
    event.stopPropagation();

    if (!isPatrolling.value || isProcessing.value) return;

    isProcessing.value = true;
    const patrolController = CustomPatrolController.getInstance();
    patrolController.stop();
  };

  // 更新撤销/重做状态
  const updateUndoRedoState = () => {
    const drawingTool = PathDrawingTool.getInstance();
    canUndo.value = drawingTool.canUndo();
    canRedo.value = drawingTool.canRedo();
  };

  // 保存路径按钮点击
  const handleSavePathClick = (event: MouseEvent) => {
    // 阻止事件冒泡，防止点击按钮时同时触发3D场景中的地板点击事件
    event.preventDefault();
    event.stopPropagation();

    if (isPatrolling.value || isDrawingActive.value || !hasDrawnPath.value) return;

    // 生成默认路径名称
    pathNameInput.value = `巡检路径_${new Date().toLocaleString().replace(/[\/\s:]/g, '_')}`;
    showSaveDialog.value = true;
  };

  // 保存当前路径
  const saveCurrentPath = () => {
    if (!pathNameInput.value.trim()) {
      message.warning('路径名称不能为空');
      return;
    }

    try {
      // 获取巡检控制器
      const patrolController = CustomPatrolController.getInstance();

      // 获取绘制工具中的路径点
      const drawingTool = PathDrawingTool.getInstance();
      // 确保路径点存在（虽然这里不直接使用，但调用getPathPoints可以确保路径点已加载）
      drawingTool.getPathPoints();

      // 保存路径数据
      const pathData = patrolController.savePatrolPath(pathNameInput.value);

      // 检查是否有同名路径
      const existingIndex = savedPaths.value.findIndex((path) => path.name === pathNameInput.value);
      if (existingIndex >= 0) {
        // 替换同名路径
        savedPaths.value[existingIndex] = pathData;
      } else {
        // 添加新路径
        savedPaths.value.push(pathData);
      }

      // 保存到本地存储
      localStorage.setItem(STORAGE_KEY, JSON.stringify(savedPaths.value));

      // 关闭对话框
      showSaveDialog.value = false;

      // 显示成功消息
      message.success(`路径 "${pathNameInput.value}" 已保存`);
    } catch (error) {
      console.error('[PatrolToolbar] 保存路径失败:', error);
      message.error('保存路径失败');
    }
  };

  // 加载路径按钮点击
  const handleLoadPathClick = (event: MouseEvent) => {
    // 阻止事件冒泡，防止点击按钮时同时触发3D场景中的地板点击事件
    event.preventDefault();
    event.stopPropagation();

    if (isPatrolling.value || isDrawingActive.value || isProcessing.value) return;

    // 重置选择
    selectedPathIndex.value = -1;

    // 确保处理状态为false
    isProcessing.value = false;

    // 强制重置弹窗状态，确保可以再次打开
    showLoadDialog.value = false;

    // 使用setTimeout确保状态更新后再显示弹窗
    setTimeout(() => {
      // 显示加载对话框
      showLoadDialog.value = true;
      console.log('[PatrolToolbar] 显示加载路径弹窗, showLoadDialog =', showLoadDialog.value);
    }, 0);
  };

  // 加载选中的路径
  const loadSelectedPath = () => {
    if (selectedPathIndex.value < 0 || selectedPathIndex.value >= savedPaths.value.length) {
      message.warning('请选择要加载的路径');
      return;
    }

    try {
      // 设置处理状态
      isProcessing.value = true;

      // 获取选中的路径数据
      const pathData = savedPaths.value[selectedPathIndex.value];

      // 获取巡检控制器
      const patrolController = CustomPatrolController.getInstance();

      // 加载路径
      patrolController.loadPatrolPath(pathData);

      // 同步路径点到PathDrawingTool
      const drawingTool = PathDrawingTool.getInstance();

      // 将普通对象转换为PathPoint对象
      const pathPoints: PathPoint[] = pathData.points.map((p: any) => ({
        position: new THREE.Vector3(p.x, p.y, p.z),
      }));

      // 清除当前路径并设置新路径
      drawingTool.clearPath();

      // 使用反射方式设置路径点，因为PathDrawingTool没有提供直接设置路径点的公共方法
      // 这里我们使用一个技巧：先完成绘制，然后再设置路径点
      if (pathPoints.length >= 2) {
        // 使用反射访问私有属性（不推荐，但在这种情况下是必要的）
        // @ts-ignore - 忽略TypeScript的私有属性访问警告
        drawingTool['pathPoints'] = [...pathPoints];
        // @ts-ignore
        drawingTool['state'] = DrawingToolState.COMPLETED;
      }

      // 更新状态
      hasDrawnPath.value = true;
      showLoadDialog.value = false;

      // 重要：重置处理状态，确保可以开始巡检
      isProcessing.value = false;

      // 显示成功消息
      message.success(`路径 "${pathData.name}" 已加载`);

      console.log('[PatrolToolbar] 已同步路径点到PathDrawingTool，点数:', pathPoints.length);
    } catch (error) {
      console.error('[PatrolToolbar] 加载路径失败:', error);
      message.error('加载路径失败');

      // 确保在出错时也重置处理状态
      isProcessing.value = false;
    }
  };

  // 删除路径确认
  const deletePathConfirm = (index: number) => {
    if (index < 0 || index >= savedPaths.value.length) return;

    const pathName = savedPaths.value[index].name;

    // 先关闭加载弹窗，防止删除后弹窗状态异常
    const wasDialogOpen = showLoadDialog.value;
    if (wasDialogOpen) {
      console.log('[PatrolToolbar] 删除路径前关闭加载弹窗');
      showLoadDialog.value = false;
    }

    Modal.confirm({
      title: '删除确认',
      content: `确定要删除路径 "${pathName}" 吗？`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        // 删除路径
        savedPaths.value.splice(index, 1);

        // 如果删除的是当前选中的路径，重置选择
        if (selectedPathIndex.value === index) {
          selectedPathIndex.value = -1;
        } else if (selectedPathIndex.value > index) {
          // 如果删除的是当前选中路径之前的路径，调整选择索引
          selectedPathIndex.value--;
        }

        // 保存到本地存储
        localStorage.setItem(STORAGE_KEY, JSON.stringify(savedPaths.value));

        // 显示成功消息
        message.success(`路径 "${pathName}" 已删除`);

        // 如果之前弹窗是打开的，并且还有路径可以选择，则重新打开弹窗
        if (wasDialogOpen && savedPaths.value.length > 0) {
          console.log('[PatrolToolbar] 删除路径后重新打开加载弹窗');
          // 使用setTimeout确保状态更新后再显示弹窗
          setTimeout(() => {
            showLoadDialog.value = true;
          }, 100);
        }
      },
      onCancel: () => {
        // 如果之前弹窗是打开的，取消删除后重新打开弹窗
        if (wasDialogOpen) {
          console.log('[PatrolToolbar] 取消删除，重新打开加载弹窗');
          setTimeout(() => {
            showLoadDialog.value = true;
          }, 100);
        }
      },
    });
  };

  // 加载保存的路径
  const loadSavedPaths = () => {
    try {
      const savedPathsJson = localStorage.getItem(STORAGE_KEY);
      if (savedPathsJson) {
        const paths = JSON.parse(savedPathsJson);
        if (Array.isArray(paths)) {
          savedPaths.value = paths;
          console.log(`[PatrolToolbar] 已加载${paths.length}条保存的路径`);
        }
      }
    } catch (error) {
      console.error('[PatrolToolbar] 加载保存的路径失败:', error);
    }
  };

  // 完成绘制按钮点击
  const handleCompleteDrawingClick = (event: MouseEvent) => {
    // 阻止事件冒泡，防止点击按钮时同时触发3D场景中的地板点击事件
    event.preventDefault();
    event.stopPropagation();

    if (!isDrawingActive.value || !canCompleteDrawing.value) return;

    try {
      // 检查当前是否处于透明模式
      const isTransparencyActive = globalThreeStore.transparencyMode;
      if (isTransparencyActive) {
        console.log('[PatrolToolbar] 检测到透视模式已启用，确保在完成绘制后保持透视效果');
      }

      const drawingTool = PathDrawingTool.getInstance();

      // 检查是否有足够的路径点
      const pathPoints = drawingTool.getPathPoints();
      if (pathPoints.length < 2) {
        message.warning('至少需要2个点才能完成路径');
        return;
      }

      // 完成绘制
      drawingTool.completeDrawing();

      // 重置状态
      canCompleteDrawing.value = false;

      // 如果处于透明模式，确保在完成绘制后重新应用透明效果
      if (isTransparencyActive) {
        console.log('[PatrolToolbar] 完成绘制后重新应用透明效果');
        try {
          // 使用setTimeout确保在下一帧应用透明效果，避免与当前操作冲突
          setTimeout(() => {
            const transparencyManager = HighPerformanceTransparencyManager.getInstance();
            transparencyManager.toggleTransparency(true);
          }, 0);
        } catch (error) {
          console.error('[PatrolToolbar] 重新应用透视效果失败:', error);
        }
      }
    } catch (error) {
      console.error('[PatrolToolbar] 完成绘制失败:', error);
      message.error('完成绘制失败');
    }
  };

  // 监听绘制和巡检事件
  watch(isDrawingActive, (newVal) => {
    if (newVal) {
      updateUndoRedoState();
    } else {
      // 绘制结束时，重置完成按钮状态
      canCompleteDrawing.value = false;
    }
  });

  // 监听加载弹窗状态，确保在弹窗关闭时正确重置状态
  watch(showLoadDialog, (newVal) => {
    console.log('[PatrolToolbar] 加载弹窗状态变更:', newVal);
    if (!newVal) {
      // 弹窗关闭时，确保处理状态被重置
      isProcessing.value = false;
    }
  });

  // 初始化时加载保存的路径
  onMounted(() => {
    loadSavedPaths();
  });

  // 停止巡检的公共方法，供外部组件调用
  const stopPatrol = () => {
    if (!isPatrolling.value) return;

    const patrolController = CustomPatrolController.getInstance();
    patrolController.stop();

    // 更新状态
    isPatrolling.value = false;
    isPaused.value = false;
    isProcessing.value = false;
  };

  // 导出方法供父组件调用
  defineExpose({
    showToolbar,
    hideToolbar,
    isVisible,
    isExpanded,
    stopPatrol, // 导出停止巡检方法
  });
</script>

<style scoped>
  .patrol-toolbar {
    position: absolute;
    right: 100%;
    top: 0;
    z-index: 1000; /* 提高z-index确保在3D场景之上 */
    overflow: hidden;
    pointer-events: auto; /* 确保工具栏可以接收事件 */
  }

  .patrol-toolbar.expanded {
    box-shadow: 0 0 10px rgba(36, 108, 249, 0.3);
    border-left: 1px solid rgba(36, 108, 249, 0.5);
  }

  /* 确保按钮可以接收事件 */
  .toolbar-btn {
    width: 1.8vw;
    height: 1.8vw;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.2vw;
    cursor: pointer;
    transition: all 0.3s;
    background: rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1001;
    pointer-events: auto !important;
  }

  .toolbar-btn:hover {
    background: rgba(36, 108, 249, 0.3);
  }

  .toolbar-btn.active {
    background: rgba(36, 108, 249, 0.5);
    box-shadow: 0 0 5px rgba(36, 108, 249, 0.5);
  }

  .toolbar-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(0, 0, 0, 0.2);
  }

  /* 完成绘制按钮高亮样式 */
  .toolbar-btn.active-complete {
    background-color: rgba(72, 187, 120, 0.7);
    border: 1px solid rgba(72, 187, 120, 0.9);
    box-shadow: 0 0 8px rgba(72, 187, 120, 0.5);
    animation: pulse 1.5s infinite;
    position: relative;
  }

  .toolbar-btn.active-complete::after {
    content: '';
    position: absolute;
    top: -5px;
    right: -5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #48bb78;
    animation: blink 1s infinite;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);
    }
    70% {
      box-shadow: 0 0 0 6px rgba(72, 187, 120, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(72, 187, 120, 0);
    }
  }

  @keyframes blink {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
</style>
