<template>
  <div
    v-if="isVisible"
    class="roaming-toolbar flex items-center h-[2.2vw] bg-[rgba(23,43,77,0.8)] border border-[rgba(36,108,249,0.3)] rounded-l-[0.3vw] overflow-hidden transition-all duration-300 z-50"
    :class="{ expanded: isExpanded }"
    :style="{
      width: isExpanded ? '4.5vw' : '0',
      opacity: isExpanded ? '1' : '0.8',
      transform: isExpanded ? 'translateX(0)' : 'translateX(1vw)',
    }"
    @click.stop.prevent
  >
    <div class="flex items-center justify-around px-[0.5vw] w-full h-full gap-[0.2vw] pointer-events-auto">
      <!-- 进入漫游按钮 -->
      <a-tooltip placement="top" title="进入漫游">
        <div
          class="toolbar-btn pointer-events-auto"
          :class="{
            active: isRoamingActive,
            disabled: isProcessing,
          }"
          @click.stop.prevent="handleEnterRoamingClick"
        >
          <CompassOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 退出漫游按钮 -->
      <a-tooltip placement="top" title="退出漫游">
        <div
          class="toolbar-btn pointer-events-auto"
          :class="{ disabled: !isRoamingActive || isProcessing }"
          @click.stop.prevent="handleExitRoamingClick"
        >
          <CloseCircleOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onBeforeUnmount } from 'vue';
  import { CompassOutlined, CloseCircleOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { RoamingController } from '../lib/roaming/RoamingController';
  import { useGlobalThreeStore } from '../store/globalThreeStore';

  // 状态定义
  const isVisible = ref(false);
  const isExpanded = ref(false);
  const isRoamingActive = ref(false);
  const isProcessing = ref(false);

  // 显示工具栏
  const showToolbar = () => {
    isVisible.value = true;
    setTimeout(() => {
      isExpanded.value = true;
      // 触发自定义事件通知父组件工具栏已展开
      window.dispatchEvent(new CustomEvent('roaming-toolbar-expanded'));
    }, 50);
  };

  // 隐藏工具栏
  const hideToolbar = () => {
    isExpanded.value = false;
    setTimeout(() => {
      isVisible.value = false;
      // 触发自定义事件通知父组件工具栏已收起
      window.dispatchEvent(new CustomEvent('roaming-toolbar-collapsed'));

      // 如果正在漫游，停止漫游
      if (isRoamingActive.value) {
        exitRoaming();
      }
    }, 300);
  };

  // 处理进入漫游按钮点击
  const handleEnterRoamingClick = () => {
    if (isProcessing.value) return;

    // 开始漫游
    startRoaming();
  };

  // 开始漫游
  const startRoaming = () => {
    isProcessing.value = true;

    try {
      // 获取漫游控制器
      let roamingController = null;
      try {
        console.log('[RoamingToolbar] 正在获取漫游控制器...');
        roamingController = RoamingController.getInstance();
      } catch (e) {
        console.error('[RoamingToolbar] 获取漫游控制器失败:', e);
        throw new Error('获取漫游控制器失败');
      }

      if (!roamingController) {
        throw new Error('漫游控制器未初始化');
      }

      // 设置起始位置 - 不传参数，让控制器自动查找设备位置
      try {
        console.log('[RoamingToolbar] 正在设置起始位置...');
        roamingController.setStartPosition();
      } catch (e) {
        console.error('[RoamingToolbar] 设置起始位置失败:', e);
        throw new Error('设置起始位置失败');
      }

      // 确保场景已加载完成
      const globalThreeStore = useGlobalThreeStore();
      if (!globalThreeStore.appReady) {
        console.warn('[RoamingToolbar] 场景尚未完全加载，可能会影响漫游体验');
        message.warning('场景尚未完全加载，可能会影响漫游体验');
      }

      // 开始漫游
      try {
        console.log('[RoamingToolbar] 正在启动漫游...');

        // 启动漫游
        roamingController.start();
      } catch (e) {
        console.error('[RoamingToolbar] 开始漫游失败:', e);
        throw new Error('开始漫游失败: ' + (e instanceof Error ? e.message : String(e)));
      }

      // 更新状态
      isRoamingActive.value = true;

      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('roaming-started'));

      // 显示提示信息，说明使用了优化的默认灵敏度
      message.success('已进入第一人称漫游模式（已优化操作手感）：WASD键移动，鼠标控制视角方向，Shift键加速，空格键跳跃，ESC键退出');
    } catch (error) {
      console.error('[RoamingToolbar] 开始漫游失败:', error);
      message.error('开始漫游失败: ' + (error instanceof Error ? error.message : String(error)));

      // 确保状态被重置
      isRoamingActive.value = false;

      // 尝试延迟重试一次
      setTimeout(() => {
        console.log('[RoamingToolbar] 尝试延迟重试漫游初始化...');
        isProcessing.value = false;
      }, 1000);
    } finally {
      isProcessing.value = false;
    }
  };

  // 处理退出漫游按钮点击
  const handleExitRoamingClick = () => {
    if (isProcessing.value || !isRoamingActive.value) return;

    // 退出漫游
    exitRoaming();
  };

  // 退出漫游
  const exitRoaming = () => {
    isProcessing.value = true;

    try {
      // 获取漫游控制器
      const roamingController = RoamingController.getInstance();

      // 停止漫游
      roamingController.stop();

      // 更新状态
      isRoamingActive.value = false;
      isProcessing.value = false;

      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('roaming-stopped'));

      // 显示提示信息
      message.success('已退出第一人称漫游模式');
    } catch (error) {
      console.error('[RoamingToolbar] 退出漫游失败:', error);
      message.error('退出漫游失败');
      isProcessing.value = false;
    }
  };

  // 组件卸载前清理
  onBeforeUnmount(() => {
    // 退出漫游
    if (isRoamingActive.value) {
      exitRoaming();
    }
  });

  // 导出方法供父组件调用
  defineExpose({
    showToolbar,
    hideToolbar,
    isVisible,
    isExpanded,
    exitRoaming,
  });
</script>

<style scoped>
  .roaming-toolbar {
    position: absolute;
    right: 100%;
    top: 0;
    z-index: 1000; /* 提高z-index确保在3D场景之上 */
    overflow: hidden;
    pointer-events: auto; /* 确保工具栏可以接收事件 */
  }

  .roaming-toolbar.expanded {
    box-shadow: 0 0 10px rgba(36, 108, 249, 0.3);
    border-left: 1px solid rgba(36, 108, 249, 0.5);
  }

  .toolbar-btn {
    width: 1.8vw;
    height: 1.8vw;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.2vw;
    cursor: pointer;
    transition: all 0.3s;
    background: rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1001;
    pointer-events: auto !important;
  }

  .toolbar-btn:hover {
    background: rgba(36, 108, 249, 0.3);
  }

  .toolbar-btn.active {
    background: rgba(36, 108, 249, 0.5);
    box-shadow: 0 0 5px rgba(36, 108, 249, 0.5);
  }

  .toolbar-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(0, 0, 0, 0.2);
  }
</style>
