<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 综合考核概览 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-award mr-[0.4vw] text-blue-400"></i>
        能耗指标综合考核概览
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in overallStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.grade" class="text-[0.5vw] mt-[0.2vw]" :class="stat.gradeClass">
            {{ stat.grade }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：关键指标考核 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-clipboard-check mr-[0.4vw] text-blue-400"></i>
            关键指标考核
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="category in indicatorCategories"
              :key="category.key"
              @click="activeCategory = category.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activeCategory === category.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30',
              ]"
            >
              {{ category.label }}
            </button>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div
              v-for="indicator in filteredIndicators"
              :key="indicator.id"
              class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all"
            >
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <i :class="indicator.icon" class="mr-[0.6vw] text-blue-400"></i>
                  <span class="text-[0.65vw] text-white font-medium">{{ indicator.name }}</span>
                </div>
                <span class="text-[0.6vw]" :class="getScoreClass(indicator.score)"> {{ indicator.score }}分 </span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400 mb-[0.3vw]">
                <span>目标值：{{ indicator.target }}</span>
                <span>实际值：{{ indicator.actual }}</span>
              </div>
              <div class="flex justify-between text-[0.6vw] mb-[0.3vw]">
                <span class="text-gray-400">权重：{{ indicator.weight }}%</span>
                <span :class="getGradeClass(indicator.grade)">{{ indicator.grade }}</span>
              </div>
              <div class="mt-[0.3vw]">
                <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                  <div
                    :class="getProgressClass(indicator.score)"
                    class="h-[0.3vw] rounded-full transition-all"
                    :style="{ width: indicator.score + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：考核结果和改进建议 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 考核结果汇总 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-chart-pie mr-[0.4vw] text-blue-400"></i>
            考核结果汇总
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="result in assessmentResults" :key="result.category" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ result.category }}</span>
                <span class="text-[0.6vw]" :class="getScoreClass(result.score)">{{ result.score }}分</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400 mb-[0.2vw]">
                <span>权重：{{ result.weight }}%</span>
                <span
                  >等级：<span :class="getGradeClass(result.grade)">{{ result.grade }}</span></span
                >
              </div>
              <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                <div
                  :class="getProgressClass(result.score)"
                  class="h-[0.3vw] rounded-full transition-all"
                  :style="{ width: result.score + '%' }"
                ></div>
              </div>
            </div>
          </div>

          <div class="mt-[0.6vw] pt-[0.6vw] border-t border-gray-600">
            <div class="flex justify-between text-[0.7vw]">
              <span class="text-gray-400">综合得分</span>
              <span class="text-green-400 font-bold">{{ overallScore }}分</span>
            </div>
            <div class="flex justify-between text-[0.6vw] mt-[0.2vw]">
              <span class="text-gray-400">综合等级</span>
              <span :class="getGradeClass(overallGrade)">{{ overallGrade }}</span>
            </div>
          </div>
        </div>

        <!-- 历史对比 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-history mr-[0.4vw] text-blue-400"></i>
            历史对比分析
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="comparison in historicalComparisons" :key="comparison.period" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ comparison.period }}</span>
              <div class="flex items-center gap-[0.4vw]">
                <span class="text-[0.6vw] text-white">{{ comparison.score }}分</span>
                <span class="text-[0.5vw]" :class="getTrendClass(comparison.trend)">{{ comparison.trend }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 改进建议 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-lightbulb mr-[0.4vw] text-blue-400"></i>
            改进建议
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="suggestion in improvementSuggestions" :key="suggestion.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex items-start">
                <div class="w-[0.6vw] h-[0.6vw] rounded-full bg-yellow-400 mt-[0.3vw] mr-[0.4vw] flex-shrink-0"></div>
                <div class="flex-1">
                  <div class="text-[0.6vw] text-white mb-[0.2vw]">{{ suggestion.title }}</div>
                  <div class="text-[0.5vw] text-gray-400 mb-[0.2vw]">{{ suggestion.description }}</div>
                  <div class="flex justify-between text-[0.5vw]">
                    <span class="text-green-400">预期提升：{{ suggestion.expectedImprovement }}</span>
                    <span class="text-blue-400">优先级：{{ suggestion.priority }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 奖惩机制 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-medal mr-[0.4vw] text-blue-400"></i>
            奖惩机制
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="reward in rewardSystem" :key="reward.level" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ reward.level }}</span>
                <span class="text-[0.6vw]" :class="getRewardClass(reward.type)">{{ reward.amount }}</span>
              </div>
              <div class="text-[0.5vw] text-gray-400">{{ reward.condition }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 综合考核概览数据
  const overallStats = ref([
    {
      label: '综合得分',
      value: '92.5',
      valueClass: 'text-green-400',
      grade: 'A级',
      gradeClass: 'text-green-400',
    },
    {
      label: '能效指标',
      value: '94.2',
      valueClass: 'text-green-400',
      grade: 'A级',
      gradeClass: 'text-green-400',
    },
    {
      label: '节能指标',
      value: '89.8',
      valueClass: 'text-blue-400',
      grade: 'A级',
      gradeClass: 'text-green-400',
    },
    {
      label: '管理指标',
      value: '95.1',
      valueClass: 'text-green-400',
      grade: 'A级',
      gradeClass: 'text-green-400',
    },
    {
      label: '环保指标',
      value: '91.3',
      valueClass: 'text-green-400',
      grade: 'A级',
      gradeClass: 'text-green-400',
    },
  ]);

  // 指标分类
  const indicatorCategories = ref([
    { key: 'all', label: '全部' },
    { key: 'efficiency', label: '能效指标' },
    { key: 'saving', label: '节能指标' },
    { key: 'management', label: '管理指标' },
    { key: 'environmental', label: '环保指标' },
  ]);

  const activeCategory = ref('all');

  // 关键指标数据
  const indicators = ref([
    {
      id: 1,
      name: 'PUE值',
      category: 'efficiency',
      icon: 'fas fa-tachometer-alt',
      target: '< 1.5',
      actual: '1.42',
      score: 95,
      weight: 20,
      grade: 'A',
    },
    {
      id: 2,
      name: '能源利用效率',
      category: 'efficiency',
      icon: 'fas fa-bolt',
      target: '> 85%',
      actual: '87.5%',
      score: 92,
      weight: 15,
      grade: 'A',
    },
    {
      id: 3,
      name: '年度节能率',
      category: 'saving',
      icon: 'fas fa-leaf',
      target: '> 15%',
      actual: '18.5%',
      score: 98,
      weight: 25,
      grade: 'A+',
    },
    {
      id: 4,
      name: '设备运行效率',
      category: 'efficiency',
      icon: 'fas fa-cog',
      target: '> 90%',
      actual: '92.3%',
      score: 94,
      weight: 15,
      grade: 'A',
    },
    {
      id: 5,
      name: '能耗监控覆盖率',
      category: 'management',
      icon: 'fas fa-eye',
      target: '100%',
      actual: '98.5%',
      score: 96,
      weight: 10,
      grade: 'A',
    },
    {
      id: 6,
      name: 'CO₂排放强度',
      category: 'environmental',
      icon: 'fas fa-globe-americas',
      target: '< 0.9 t',
      actual: '0.85 t',
      score: 93,
      weight: 15,
      grade: 'A',
    },
  ]);

  // 筛选后的指标
  const filteredIndicators = computed(() => {
    if (activeCategory.value === 'all') {
      return indicators.value;
    }
    return indicators.value.filter((indicator) => indicator.category === activeCategory.value);
  });

  // 考核结果汇总
  const assessmentResults = ref([
    { category: '能效指标', score: 94, weight: 35, grade: 'A' },
    { category: '节能指标', score: 90, weight: 30, grade: 'A' },
    { category: '管理指标', score: 95, weight: 20, grade: 'A' },
    { category: '环保指标', score: 91, weight: 15, grade: 'A' },
  ]);

  const overallScore = ref('92.5');
  const overallGrade = ref('A级');

  // 历史对比
  const historicalComparisons = ref([
    { period: '本月', score: 92.5, trend: '↑ 2.3' },
    { period: '上月', score: 90.2, trend: '↑ 1.8' },
    { period: '本季度', score: 91.8, trend: '↑ 3.5' },
    { period: '去年同期', score: 88.3, trend: '↑ 4.2' },
  ]);

  // 改进建议
  const improvementSuggestions = ref([
    {
      id: 1,
      title: '优化空调运行策略',
      description: '根据负载变化动态调整空调运行参数',
      expectedImprovement: '2-3分',
      priority: '高',
    },
    {
      id: 2,
      title: '完善能耗监控体系',
      description: '增加末端设备监控点，提高监控覆盖率',
      expectedImprovement: '1-2分',
      priority: '中',
    },
    {
      id: 3,
      title: '加强设备维护管理',
      description: '建立预防性维护计划，保持设备最佳状态',
      expectedImprovement: '1-2分',
      priority: '中',
    },
  ]);

  // 奖惩机制
  const rewardSystem = ref([
    { level: 'A级奖励', type: 'reward', amount: '+5万元', condition: '综合得分≥90分' },
    { level: 'B级奖励', type: 'reward', amount: '+2万元', condition: '综合得分80-89分' },
    { level: 'C级整改', type: 'penalty', amount: '限期整改', condition: '综合得分70-79分' },
    { level: 'D级处罚', type: 'penalty', amount: '-3万元', condition: '综合得分<70分' },
  ]);

  // 获取分数样式
  const getScoreClass = (score) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 80) return 'text-blue-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  // 获取等级样式
  const getGradeClass = (grade) => {
    if (grade.includes('A')) return 'text-green-400';
    if (grade.includes('B')) return 'text-blue-400';
    if (grade.includes('C')) return 'text-yellow-400';
    return 'text-red-400';
  };

  // 获取进度条样式
  const getProgressClass = (score) => {
    if (score >= 90) return 'bg-green-400';
    if (score >= 80) return 'bg-blue-400';
    if (score >= 70) return 'bg-yellow-400';
    return 'bg-red-400';
  };

  // 获取趋势样式
  const getTrendClass = (trend) => {
    if (trend.includes('↑')) return 'text-green-400';
    if (trend.includes('↓')) return 'text-red-400';
    return 'text-gray-400';
  };

  // 获取奖惩样式
  const getRewardClass = (type) => {
    return type === 'reward' ? 'text-green-400' : 'text-red-400';
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
