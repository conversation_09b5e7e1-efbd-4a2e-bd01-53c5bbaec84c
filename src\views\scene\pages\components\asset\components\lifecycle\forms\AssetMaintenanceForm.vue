<template>
  <div class="p-[1vw] space-y-[1vw]">
    <div class="text-[0.7vw] text-white">资产维护表单</div>
    <div class="text-[0.6vw] text-gray-400">功能开发中...</div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue';
  const props = defineProps({ formData: { type: Object, default: () => ({}) } });
  const emit = defineEmits(['update:formData']);
  const formData = ref({ ...props.formData });
  watch(formData, (newValue) => { emit('update:formData', newValue); }, { deep: true });
</script>
