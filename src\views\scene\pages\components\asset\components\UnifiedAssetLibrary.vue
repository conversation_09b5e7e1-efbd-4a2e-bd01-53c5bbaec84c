<template>
  <div class="h-full bg-gradient-to-br from-[#0a1628] to-[#1a2332] p-[1vw]">
    <!-- 顶部统计卡片 -->
    <div class="grid grid-cols-6 gap-[0.8vw] mb-[1vw]">
      <div v-for="stat in assetStats" :key="stat.label" class="bg-[rgba(59,142,230,0.1)] rounded-lg p-[0.8vw] border border-white/10 shadow-lg">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-[0.6vw] text-gray-400 mb-[0.2vw]">{{ stat.label }}</div>
            <div :class="['text-[1vw] font-semibold', stat.valueClass]">{{ stat.value }}</div>
          </div>
          <div :class="['text-[1.2vw]', stat.iconClass]">{{ stat.icon }}</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="bg-[rgba(59,142,230,0.1)] rounded-lg border border-white/10 shadow-lg h-[calc(100%-6vw)]">
      <!-- 标签页导航 -->
      <div class="flex border-b border-white/10">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          :class="[
            'px-[1.2vw] py-[0.8vw] text-[0.7vw] font-medium transition-all duration-200 border-b-2 bg-transparent',
            activeTab === tab.key
              ? 'text-blue-400 border-blue-400 bg-blue-400/10'
              : 'text-gray-400 border-transparent hover:text-white hover:bg-white/5',
          ]"
          @click="activeTab = tab.key"
        >
          <span class="mr-[0.4vw]">{{ tab.icon }}</span>
          {{ tab.label }}
        </button>
      </div>

      <!-- 标签页内容 -->
      <div class="p-[1vw] h-[calc(100%-3.5vw)] overflow-hidden">
        <!-- 入库管理 -->
        <AssetInbound v-if="activeTab === 'inbound'" />

        <!-- 申领管理 -->
        <AssetApplication v-if="activeTab === 'application'" />

        <!-- 调拨管理 -->
        <AssetTransfer v-if="activeTab === 'transfer'" />

        <!-- 报废管理 -->
        <AssetDisposal v-if="activeTab === 'disposal'" />

        <!-- 部署管理 -->
        <AssetDeployment v-if="activeTab === 'deployment'" />

        <!-- 监控管理 -->
        <AssetMonitoring v-if="activeTab === 'monitoring'" />

        <!-- 变更管理 -->
        <AssetChange v-if="activeTab === 'change'" />

        <!-- 维护管理 -->
        <AssetMaintenance v-if="activeTab === 'maintenance'" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import AssetInbound from './lifecycle/AssetInbound.vue';
  import AssetApplication from './lifecycle/AssetApplication.vue';
  import AssetTransfer from './lifecycle/AssetTransfer.vue';
  import AssetDisposal from './lifecycle/AssetDisposal.vue';
  import AssetDeployment from './lifecycle/AssetDeployment.vue';
  import AssetMonitoring from './lifecycle/AssetMonitoring.vue';
  import AssetChange from './lifecycle/AssetChange.vue';
  import AssetMaintenance from './lifecycle/AssetMaintenance.vue';

  // 当前活动标签
  const activeTab = ref('inbound');

  // 标签页配置
  const tabs = [
    { key: 'inbound', label: '入库管理', icon: '📦' },
    { key: 'application', label: '申领管理', icon: '📋' },
    { key: 'transfer', label: '调拨管理', icon: '🔄' },
    { key: 'disposal', label: '报废管理', icon: '🗑️' },
    { key: 'deployment', label: '部署管理', icon: '🚀' },
    { key: 'monitoring', label: '监控管理', icon: '📊' },
    { key: 'change', label: '变更管理', icon: '⚙️' },
    { key: 'maintenance', label: '维护管理', icon: '🔧' },
  ];

  // 统计数据
  const assetStats = ref([
    { label: '总资产数', value: '1,248', valueClass: 'text-white', icon: '📦', iconClass: 'text-blue-400' },
    { label: '在库资产', value: '856', valueClass: 'text-green-400', icon: '✅', iconClass: 'text-green-400' },
    { label: '已分配', value: '312', valueClass: 'text-blue-400', icon: '📤', iconClass: 'text-blue-400' },
    { label: '维护中', value: '45', valueClass: 'text-yellow-400', icon: '🔧', iconClass: 'text-yellow-400' },
    { label: '待报废', value: '23', valueClass: 'text-red-400', icon: '⚠️', iconClass: 'text-red-400' },
    { label: '本月新增', value: '12', valueClass: 'text-purple-400', icon: '📈', iconClass: 'text-purple-400' },
  ]);
</script>
