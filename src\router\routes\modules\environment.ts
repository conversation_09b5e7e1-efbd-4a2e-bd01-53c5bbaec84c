// src/router/routes/modules/environment.ts
import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

const EnvironmentRoute: AppRouteModule = {
  path: '/environment',
  name: 'Environment',
  component: LAYOUT,
  meta: {
    orderNo: 31,
    icon: 'ant-design:environment-outlined',
    title: '动环管理',
  },
  children: [
    {
      path: 'real-time-data',
      name: 'EnvRealTimeData',
      component: () => import('/@/views/environment/real-time-data/index.vue'),
      meta: {
        title: '实时数据',
      },
    },
    {
      path: 'alarm',
      name: 'EnvAlarm',
      component: () => import('/@/views/environment/alarm/index.vue'),
      meta: {
        title: '告警数据',
      },
    },
  ],
};

export default EnvironmentRoute;
