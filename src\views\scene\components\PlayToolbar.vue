<template>
  <div
    v-if="isVisible"
    class="play-toolbar flex items-center h-[2.2vw] bg-[rgba(23,43,77,0.8)] border border-[rgba(36,108,249,0.3)] rounded-l-[0.3vw] overflow-hidden transition-all duration-300 z-50"
    :class="{ expanded: isExpanded }"
    :style="{
      width: isExpanded ? '10vw' : '0',
      opacity: isExpanded ? '1' : '0.8',
      transform: isExpanded ? 'translateX(0)' : 'translateX(1vw)',
    }"
  >
    <div class="flex items-center justify-around px-[0.5vw] w-full h-full gap-[0.2vw] pointer-events-auto">
      <!-- 开始/暂停播放按钮 -->
      <a-tooltip placement="top" :title="isPlaying ? (isPaused ? '继续播放' : '暂停播放') : '开始播放'">
        <div
          class="toolbar-btn"
          :class="{
            active: isPlaying && !isPaused,
            disabled: isProcessing,
          }"
          @click="handleStartPauseClick"
        >
          <component :is="isPlaying ? (isPaused ? PlayCircleOutlined : PauseCircleOutlined) : PlayCircleOutlined" class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 停止播放按钮 -->
      <a-tooltip placement="top" title="停止播放">
        <div class="toolbar-btn" :class="{ disabled: !isPlaying || isProcessing }" @click="handleStopClick">
          <StopOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 循环播放按钮 -->
      <a-tooltip placement="top" :title="isLoopMode ? '禁用循环播放' : '启用循环播放'">
        <div class="toolbar-btn" :class="{ active: isLoopMode, disabled: isProcessing }" @click="toggleLoopMode">
          <RetweetOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 环绕观察模式按钮 -->
      <a-tooltip placement="top" :title="isOrbitMode ? '禁用环绕观察' : '启用环绕观察'">
        <div class="toolbar-btn" :class="{ active: isOrbitMode, disabled: isProcessing }" @click="toggleOrbitMode">
          <SyncOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 上一个设备按钮 -->
      <a-tooltip placement="top" title="上一个">
        <div class="toolbar-btn" :class="{ disabled: !isPlaying || isProcessing }" @click="handlePreviousClick">
          <LeftOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 下一个设备按钮 -->
      <a-tooltip placement="top" title="下一个">
        <div class="toolbar-btn" :class="{ disabled: !isPlaying || isProcessing }" @click="handleNextClick">
          <RightOutlined class="text-[0.8vw] text-white" />
        </div>
      </a-tooltip>

      <!-- 设备信息 -->
      <div v-if="isPlaying" class="text-white text-[0.6vw] ml-[0.5vw]"> {{ currentDevice + 1 }}/{{ totalDevices }} </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { PlayDevice } from '../lib/play/PlayController';
  import {
    PlayCircleOutlined,
    PauseCircleOutlined,
    StopOutlined,
    RetweetOutlined,
    SyncOutlined,
    LeftOutlined,
    RightOutlined,
  } from '@ant-design/icons-vue';
  import { PlayController } from '../lib/play/PlayController';
  import { useGlobalThreeStore } from '../store/globalThreeStore';
  import { message } from 'ant-design-vue';

  // 状态定义
  const isVisible = ref(false);
  const isExpanded = ref(false);
  const isPlaying = ref(false);
  const isPaused = ref(false);
  const isProcessing = ref(false);
  const currentDevice = ref(0);
  const totalDevices = ref(0);
  const isLoopMode = ref(true); // 默认启用循环播放
  const isOrbitMode = ref(true); // 默认启用环绕观察
  const globalThreeStore = useGlobalThreeStore();

  // 显示工具栏
  const showToolbar = () => {
    isVisible.value = true;
    // 使用requestAnimationFrame确保DOM更新后再设置展开状态
    requestAnimationFrame(() => {
      isExpanded.value = true;
    });
  };

  // 隐藏工具栏
  const hideToolbar = () => {
    isExpanded.value = false;
    // 使用setTimeout确保动画完成后再隐藏元素
    setTimeout(() => {
      isVisible.value = false;
    }, 300);
  };

  // 开始/暂停播放
  const handleStartPauseClick = () => {
    if (isProcessing.value) return;

    if (isPlaying.value) {
      if (isPaused.value) {
        resumePlay();
      } else {
        pausePlay();
      }
    } else {
      startPlay();
    }
  };

  // 开始播放
  const startPlay = async () => {
    try {
      isProcessing.value = true;

      // 获取播放控制器
      const playController = PlayController.getInstance();

      // 查找设备
      const devices = playController.findDevices();

      if (devices.length === 0) {
        showMessage('未找到可播放的设备，无法开始播放', 'warning');
        isProcessing.value = false;
        return;
      }

      // 更新状态
      totalDevices.value = devices.length;
      currentDevice.value = 0;

      // 同步循环模式和环绕模式状态
      isLoopMode.value = playController.isLoopModeEnabled();
      isOrbitMode.value = playController.isOrbitModeEnabled();

      // 设置事件回调
      const events = {
        onStart: () => {
          isPlaying.value = true;
          isPaused.value = false;
          isProcessing.value = false;
        },
        onPause: () => {
          isPaused.value = true;
        },
        onResume: () => {
          isPaused.value = false;
        },
        onStop: () => {
          isPlaying.value = false;
          isPaused.value = false;
          // 触发自定义事件通知父组件播放已停止
          window.dispatchEvent(new CustomEvent('play-stopped'));
        },
        onDeviceReached: (_device: PlayDevice, index: number) => {
          currentDevice.value = index;
        },
        onPlayComplete: () => {
          isPlaying.value = false;
          // 触发自定义事件通知父组件播放已完成
          window.dispatchEvent(new CustomEvent('play-completed'));
        },
      };

      // 开始播放
      playController.start(events);
    } catch (error) {
      console.error('开始播放失败:', error);
      showMessage('开始播放失败', 'error');
      isProcessing.value = false;
    }
  };

  // 暂停播放
  const pausePlay = () => {
    if (!isPlaying.value || isPaused.value) return;

    const playController = PlayController.getInstance();
    playController.pause();
  };

  // 继续播放
  const resumePlay = () => {
    if (!isPlaying.value || !isPaused.value) return;

    const playController = PlayController.getInstance();
    playController.resume();
  };

  // 停止播放
  const handleStopClick = async () => {
    if (!isPlaying.value || isProcessing.value) return;

    try {
      isProcessing.value = true;

      // 停止播放
      const playController = PlayController.getInstance();
      playController.stop();

      // 触发自定义事件通知父组件播放已停止
      window.dispatchEvent(new CustomEvent('play-stopped'));

      // 更新状态
      isPlaying.value = false;
      isPaused.value = false;
      isProcessing.value = false;
    } catch (error) {
      console.error('停止播放失败:', error);
      showMessage('停止播放失败', 'error');
      isProcessing.value = false;
    }
  };

  // 切换循环播放模式
  const toggleLoopMode = () => {
    if (isProcessing.value) return;

    const playController = PlayController.getInstance();
    isLoopMode.value = playController.toggleLoopMode();
  };

  // 切换环绕观察模式
  const toggleOrbitMode = () => {
    if (isProcessing.value) return;

    const playController = PlayController.getInstance();
    isOrbitMode.value = playController.toggleOrbitMode();

    // 如果当前正在播放，重新观察当前设备以应用新的观察模式
    if (isPlaying.value && !isPaused.value) {
      const currentDeviceObj = playController.getCurrentDevice();
      if (currentDeviceObj) {
        // 停止当前播放
        playController.stop();
        // 重新开始播放
        playController.start({
          onStart: () => {
            isPlaying.value = true;
            isPaused.value = false;
          },
          onDeviceReached: (_device: PlayDevice, index: number) => {
            currentDevice.value = index;
          },
          onPlayComplete: () => {
            isPlaying.value = false;
          },
        });
      }
    }
  };

  // 上一个设备
  const handlePreviousClick = () => {
    if (!isPlaying.value || isProcessing.value) return;

    const playController = PlayController.getInstance();
    const success = playController.goToPreviousDevice();

    if (!success) {
      message.info('已经是第一个设备');
    }
  };

  // 下一个设备
  const handleNextClick = () => {
    if (!isPlaying.value || isProcessing.value) return;

    const playController = PlayController.getInstance();
    const success = playController.goToNextDevice();

    if (!success) {
      message.info('已经是最后一个设备');
    }
  };

  // 显示消息
  const showMessage = (message: string, type: 'success' | 'warning' | 'error' | 'info' = 'info') => {
    if (window.$message) {
      try {
        // @ts-ignore - 忽略类型检查，因为window.$message的类型定义可能不完整
        window.$message[type](message);
      } catch (error) {
        console.error('显示消息失败:', error);
        console.log(`[${type.toUpperCase()}] ${message}`);
      }
    } else {
      console.log(`[${type.toUpperCase()}] ${message}`);
    }
  };

  // 导出方法供父组件调用
  defineExpose({
    showToolbar,
    hideToolbar,
    isVisible,
    isExpanded,
    isPlaying,
    isPaused,
    isLoopMode,
    isOrbitMode,
    currentDevice,
    totalDevices,
    updatePlayState: (playing: boolean, paused: boolean) => {
      isPlaying.value = playing;
      isPaused.value = paused;
    },
    updateDeviceInfo: (current: number, total: number) => {
      currentDevice.value = current;
      totalDevices.value = total;
    },
    updateLoopMode: (enabled: boolean) => {
      isLoopMode.value = enabled;
    },
    updateOrbitMode: (enabled: boolean) => {
      isOrbitMode.value = enabled;
    },
    toggleLoopMode,
    toggleOrbitMode,
    handlePreviousClick,
    handleNextClick,
  });
</script>

<style scoped>
  .play-toolbar {
    position: absolute;
    right: 100%;
    top: 0;
    z-index: 1000; /* 提高z-index确保在3D场景之上 */
    overflow: hidden;
    pointer-events: auto; /* 确保工具栏可以接收事件 */
  }

  .play-toolbar.expanded {
    box-shadow: 0 0 10px rgba(36, 108, 249, 0.3);
    border-left: 1px solid rgba(36, 108, 249, 0.5);
  }

  .toolbar-btn {
    width: 1.8vw;
    height: 1.8vw;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.2vw;
    cursor: pointer;
    transition: all 0.3s;
    background: rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1001;
    pointer-events: auto !important;
  }

  .toolbar-btn:hover {
    background: rgba(36, 108, 249, 0.3);
  }

  .toolbar-btn.active {
    background: rgba(36, 108, 249, 0.5);
    box-shadow: 0 0 5px rgba(36, 108, 249, 0.5);
  }

  .toolbar-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(0, 0, 0, 0.2);
  }
</style>
