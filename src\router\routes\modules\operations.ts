// src/router/routes/modules/operations.ts
import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

const OperationsRoute: AppRouteModule = {
  path: '/operations',
  name: 'Operations',
  component: LAYOUT,
  meta: {
    orderNo: 40,
    icon: 'ant-design:dashboard-outlined',
    title: '运营数据管理',
  },
  children: [
    {
      path: 'visitor',
      name: 'VisitorManagement',
      component: () => import('/@/views/operations/visitor/index.vue'),
      meta: {
        title: '智慧访客系统',
        icon: 'ant-design:user-outlined',
      },
    },
    {
      path: 'parking',
      name: 'ParkingManagement',
      component: () => import('/@/views/operations/parking/index.vue'),
      meta: {
        title: '智慧停车系统',
        icon: 'ant-design:car-outlined',
      },
    },
    {
      path: 'epidemic',
      name: 'EpidemicControl',
      component: () => import('/@/views/operations/epidemic/index.vue'),
      meta: {
        title: '疫情防控系统',
        icon: 'ant-design:safety-outlined',
      },
    },
    {
      path: 'inspection',
      name: 'SmartInspection',
      component: () => import('/@/views/operations/inspection/index.vue'),
      meta: {
        title: '智慧巡检系统',
        icon: 'ant-design:audit-outlined',
      },
    },
  ],
};

export default OperationsRoute;
