# Development Guidelines

1. All commit messages must be written in Chinese and follow Conventional Commits specification.  
2. Vue 3: Use `<script setup lang="ts">` syntax  
3. TypeScript: All development must use TypeScript  
4. State management: Must use Pinia for global state management  
5. Styling: Use Unocss utility classes (e.g. `flex items-center`), only use `<style>` tags when design cannot be achieved with Unocss  
6. Component restriction: No `ant-design-vue` components allowed in `src/views/scene/` directory  
7. API management: All APIs must be placed in `src/api/` directory and organized by module  
8. AI-generated code: Must not contain any language or comments suggesting AI authorship, avoid placeholders like "omitted code" etc.  
9. THREE.js usage: Globally use `import * as THREE from 'three'`  
10. Performance requirements: Event handling must use lodash's `debounce` or `throttle`, prefer GSAP for animations  
11. Memory management: All 3D objects must call `.dispose()` when unmounted  
12. Large data optimization: Use LOD strategy when handling large-scale 3D scenes  
13. Component usage: When using components in `src/views/scene`, must first check `src/views/scene/components`  
14. New component creation: If needed component doesn't exist, create new one in `src/views/scene/components` following `ModalDialog.vue` design structure  
15. Message component: Use `src/views/scene/components/Message.vue`  
16. Modal/Dialog component: Use `src/views/scene/components/ModalDialog.vue`  
17. Select dropdown component: Use `src/views/scene/components/MiniSelect.vue`
18. use lodash-es not lodash 