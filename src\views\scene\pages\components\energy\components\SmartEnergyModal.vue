<template>
  <div :class="['relative', customClass]">
    <ModalDialog v-model:visible="localVisible" title="智慧能耗管理" width="1400px" :default-fullscreen="defaultFullscreen" :class="rootClass">
      <div class="h-full flex flex-col gap-[0.8vw]">
        <!-- 功能导航 -->
        <div class="bg-[#15274D]/40 backdrop-blur-sm rounded p-[0.8vw]">
          <div class="flex gap-[1vw]">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              @click="activeTab = tab.key"
              :class="[
                'px-[1.2vw] py-[0.6vw] rounded text-[0.7vw] transition-all',
                activeTab === tab.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30 hover:text-white',
              ]"
            >
              <i :class="tab.icon" class="mr-[0.4vw]"></i>
              {{ tab.label }}
            </button>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="flex-1 bg-[#15274D]/40 backdrop-blur-sm rounded p-[0.8vw] overflow-y-auto custom-scrollbar">
          <!-- 节能统计 -->
          <EnergySavingsStatsPanel v-if="activeTab === 'savings'" />

          <!-- 能源效率 -->
          <EnergyEfficiencyPanel v-if="activeTab === 'efficiency'" />

          <!-- 能耗趋势分析 -->
          <EnergyTrendAnalysisPanel v-if="activeTab === 'trend'" />

          <!-- 能耗指标综合考核 -->
          <EnergyIndicatorsPanel v-if="activeTab === 'indicators'" />
        </div>
      </div>
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import EnergySavingsStatsPanel from './smart-energy/EnergySavingsStatsPanel.vue';
  import EnergyEfficiencyPanel from './smart-energy/EnergyEfficiencyPanel.vue';
  import EnergyTrendAnalysisPanel from './smart-energy/EnergyTrendAnalysisPanel.vue';
  import EnergyIndicatorsPanel from './smart-energy/EnergyIndicatorsPanel.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    iconSrc: {
      type: String,
      default: '',
    },
    defaultFullscreen: {
      type: Boolean,
      default: false,
    },
    class: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['update:visible']);

  // 将props.class重命名为customClass以避免与HTML class属性冲突
  const customClass = computed(() => props.class);
  const rootClass = computed(() => props.class);

  // 控制弹窗显示
  const localVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  // 当前激活的标签页
  const activeTab = ref('savings');

  // 标签页配置
  const tabs = ref([
    {
      key: 'savings',
      label: '节能统计',
      icon: 'fas fa-chart-pie',
    },
    {
      key: 'efficiency',
      label: '能源效率',
      icon: 'fas fa-tachometer-alt',
    },
    {
      key: 'trend',
      label: '能耗趋势分析',
      icon: 'fas fa-chart-line',
    },
    {
      key: 'indicators',
      label: '能耗指标综合考核',
      icon: 'fas fa-award',
    },
  ]);

  // 监听弹窗打开，重置到第一个标签页
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        activeTab.value = 'savings';
      }
    }
  );
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
