<template>
  <div class="env-realtime-list h-full flex flex-col">
    <!-- 表头 -->
    <div class="grid grid-cols-[40%_40%_20%] text-white/60 text-[0.6vw] mb-[0.5vw]">
      <div class="pl-[0.3vw]">设备名称</div>
      <div>信号名称</div>
      <div>数值</div>
    </div>
    <!-- 列表容器 -->
    <div ref="scrollContainer" class="flex-1 h-[calc(100%-1.5vw)] overflow-hidden relative">
      <div ref="listWrapper" class="will-change-transform" :style="{ transform: `translateY(${scrollOffset}px)` }">
        <div
          v-for="item in displayList"
          :key="item.id"
          class="grid grid-cols-[40%_40%_20%] text-white/90 text-[0.7vw] min-h-[1.8vw] py-[0.2vw] border-b border-white/10"
        >
          <div class="truncate pl-[0.3vw]" :title="item.deviceName">{{ item.deviceName }}</div>
          <div class="truncate" :title="item.signalName">{{ item.signalName }}</div>
          <div class="truncate" :title="formatValue(item)">{{ formatValue(item) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import { getRealtimeDataNoPage } from '/@/api/environment';
  import { debounce } from 'lodash-es';

  interface RealTimeDataItem {
    id: number;
    deviceName: string;
    signalName: string;
    valueData: string;
    describe: string;
    type: number;
    status: number;
    dataTime: string;
  }

  const dataList = ref<RealTimeDataItem[]>([]);
  let refreshTimer: ReturnType<typeof setInterval> | null = null;
  const refreshInterval = 30000; // 30秒刷新一次

  const scrollOffset = ref(0);
  const scrollContainer = ref<HTMLElement>();
  const listWrapper = ref<HTMLElement>();

  // 用于展示的列表数据，包含复制的部分用于无缝滚动
  const displayList = computed(() => {
    if (!dataList.value.length) return [];
    return [...dataList.value, ...dataList.value];
  });

  // 滚动相关变量
  let animationFrameId: number | null = null;
  let lastTimestamp = 0;
  let scrollSpeed = 0.15; // 每帧滚动的像素数，降低速度（原来是0.5）
  let itemHeight = 0; // 缓存行高
  let maxScroll = 0; // 缓存最大滚动距离

  // 计算初始滚动参数
  const calculateScrollParams = () => {
    if (!listWrapper.value || dataList.value.length === 0) return;

    const listItems = listWrapper.value.querySelectorAll('.grid');
    if (listItems.length === 0) return;

    // 获取并缓存第一个元素高度
    const firstItem = listItems[0] as HTMLElement;
    itemHeight = firstItem.offsetHeight;
    maxScroll = -(dataList.value.length * itemHeight);
  };

  // 使用requestAnimationFrame实现平滑滚动
  const scrollStep = (timestamp: number) => {
    if (!scrollContainer.value || !listWrapper.value || dataList.value.length === 0) {
      animationFrameId = requestAnimationFrame(scrollStep);
      return;
    }

    // 首次运行或数据更新时重新计算参数
    if (itemHeight === 0) {
      calculateScrollParams();
    }

    // 计算帧间隔，确保滚动速度一致
    if (!lastTimestamp) lastTimestamp = timestamp;
    const elapsed = timestamp - lastTimestamp;

    // 根据帧率调整滚动速度，保持视觉上的一致性
    const delta = (scrollSpeed * elapsed) / 16.67; // 基于60fps标准化

    scrollOffset.value -= delta;

    // 滚动到底部时重置
    if (scrollOffset.value <= maxScroll) {
      scrollOffset.value = 0;
    }

    lastTimestamp = timestamp;
    animationFrameId = requestAnimationFrame(scrollStep);
  };

  const startScroll = () => {
    if (animationFrameId) return;
    lastTimestamp = 0;
    animationFrameId = requestAnimationFrame(scrollStep);
  };

  const stopScroll = () => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  };

  // 格式化显示值
  const formatValue = (item: RealTimeDataItem) => {
    // 特殊处理开关量
    if (item.describe === '1&开 0&关') {
      return item.valueData === '1' ? '开' : '关';
    }
    // 特殊处理关机信号
    if (item.signalName === '关机') {
      return item.signalName;
    }
    // 默认显示值+单位
    return `${item.valueData}${item.describe || ''}`;
  };

  // 使用 debounce 防止频繁调用
  const getData = debounce(async () => {
    try {
      // 获取实时数据
      const res = await getRealtimeDataNoPage({});

      // 处理返回的数据
      if (Array.isArray(res)) {
        dataList.value = res;
        if (dataList.value.length > 0) {
          // 数据更新后重置滚动参数
          itemHeight = 0;
          maxScroll = 0;
          startScroll();
        }
        console.log('动环实时数据已更新，共', dataList.value.length, '条');
      } else if (res && typeof res === 'object' && 'records' in res) {
        dataList.value = res.records;
        if (dataList.value.length > 0) {
          // 数据更新后重置滚动参数
          itemHeight = 0;
          maxScroll = 0;
          startScroll();
        }
        console.log('动环实时数据已更新，共', dataList.value.length, '条');
      } else {
        console.warn('动环实时数据返回格式异常:', res);
      }
    } catch (error) {
      console.error('获取动环实时数据失败:', error);
    }
  }, 500);

  // 开始定时刷新
  const startRefreshTimer = () => {
    if (!refreshTimer) {
      // 立即执行一次
      getData();
      // 设置定时器
      refreshTimer = setInterval(() => {
        getData();
      }, refreshInterval);
    }
  };

  // 停止定时刷新
  const stopRefreshTimer = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  };

  onMounted(() => {
    getData();
    startRefreshTimer();
  });

  onUnmounted(() => {
    stopRefreshTimer();
    stopScroll();
  });
</script>

<style scoped>
  .env-realtime-list {
    height: 100%;
  }

  .env-realtime-list ::-webkit-scrollbar {
    display: none;
  }

  .truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
