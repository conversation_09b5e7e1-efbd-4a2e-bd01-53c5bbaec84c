<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 安防态势概览 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-shield-alt mr-[0.4vw] text-blue-400"></i>
        安防态势概览
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in securityOverview" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.status" class="text-[0.5vw] mt-[0.2vw]" :class="stat.statusClass">
            {{ stat.status }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：人车监测数据 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-video mr-[0.4vw] text-blue-400"></i>
            人车监测数据
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="filter in monitorFilters"
              :key="filter.key"
              @click="activeMonitorFilter = filter.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activeMonitorFilter === filter.key
                  ? 'bg-[#3B8EE6] text-white'
                  : 'bg-black/20 text-gray-300 hover:bg-black/30'
              ]"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>
        
        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div
              v-for="monitor in filteredMonitorData"
              :key="monitor.id"
              class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all"
            >
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <i :class="monitor.icon" class="mr-[0.6vw] text-blue-400"></i>
                  <span class="text-[0.65vw] text-white font-medium">{{ monitor.location }}</span>
                </div>
                <span class="text-[0.6vw]" :class="getMonitorStatusClass(monitor.status)">
                  {{ getMonitorStatusText(monitor.status) }}
                </span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400 mb-[0.3vw]">
                <span>设备ID：{{ monitor.deviceId }}</span>
                <span>类型：{{ monitor.type }}</span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400">
                <span>检测人数：{{ monitor.personCount }}人</span>
                <span>检测车辆：{{ monitor.vehicleCount }}辆</span>
              </div>
              <div v-if="monitor.lastDetection" class="text-[0.6vw] text-gray-400 mt-[0.2vw]">
                最后检测：{{ monitor.lastDetection }}
              </div>
              <div class="mt-[0.3vw] flex justify-between text-[0.5vw]">
                <span class="text-green-400">识别准确率：{{ monitor.accuracy }}%</span>
                <span class="text-blue-400">运行时长：{{ monitor.uptime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：工单数据和安全事件 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 工单数据 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-clipboard-list mr-[0.4vw] text-blue-400"></i>
            安防工单数据
          </div>
          
          <div class="space-y-[0.4vw]">
            <div v-for="order in workOrders" :key="order.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ order.title }}</span>
                <span class="text-[0.6vw]" :class="getOrderStatusClass(order.status)">{{ order.status }}</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400 mb-[0.2vw]">
                <span>工单号：{{ order.orderNo }}</span>
                <span>优先级：{{ order.priority }}</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>创建时间：{{ order.createTime }}</span>
                <span>处理人：{{ order.handler }}</span>
              </div>
              <div class="text-[0.5vw] text-gray-400 mt-[0.2vw]">{{ order.description }}</div>
            </div>
          </div>
        </div>

        <!-- 安全事件统计 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-exclamation-circle mr-[0.4vw] text-blue-400"></i>
            安全事件统计
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="event in securityEvents" :key="event.type" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ event.type }}</span>
                <span class="text-[0.6vw]" :class="getEventLevelClass(event.level)">{{ event.count }}次</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>本周：{{ event.weekCount }}次</span>
                <span>本月：{{ event.monthCount }}次</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备状态监控 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-cogs mr-[0.4vw] text-blue-400"></i>
            设备状态监控
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="device in deviceStatus" :key="device.type" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ device.type }}</span>
              <div class="flex items-center gap-[0.4vw]">
                <span class="text-[0.6vw] text-white">{{ device.online }}/{{ device.total }}</span>
                <div class="w-[3vw] bg-black/30 rounded-full h-[0.3vw]">
                  <div 
                    class="bg-green-400 h-[0.3vw] rounded-full"
                    :style="{ width: (device.online / device.total * 100) + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="mt-[0.6vw] pt-[0.6vw] border-t border-gray-600">
            <div class="flex justify-between text-[0.6vw]">
              <span class="text-gray-400">总体在线率</span>
              <span class="text-green-400">{{ overallOnlineRate }}%</span>
            </div>
          </div>
        </div>

        <!-- 预警信息 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-bell mr-[0.4vw] text-blue-400"></i>
            预警信息
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="alert in alerts" :key="alert.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ alert.time }}</span>
                <span class="text-[0.5vw]" :class="getAlertLevelClass(alert.level)">{{ alert.level }}</span>
              </div>
              <div class="text-[0.5vw] text-gray-400">{{ alert.message }}</div>
            </div>
          </div>
        </div>

        <!-- 系统健康度 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-heartbeat mr-[0.4vw] text-blue-400"></i>
            系统健康度
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="health in systemHealth" :key="health.component" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ health.component }}</span>
                <span class="text-[0.6vw]" :class="getHealthStatusClass(health.status)">{{ health.score }}分</span>
              </div>
              <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                <div 
                  :class="getHealthProgressClass(health.score)"
                  class="h-[0.3vw] rounded-full transition-all"
                  :style="{ width: health.score + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 安防态势概览数据
  const securityOverview = ref([
    { 
      label: '监控设备', 
      value: '48', 
      valueClass: 'text-green-400',
      status: '正常',
      statusClass: 'text-green-400'
    },
    { 
      label: '在线率', 
      value: '98.5%', 
      valueClass: 'text-green-400',
      status: '优秀',
      statusClass: 'text-green-400'
    },
    { 
      label: '今日工单', 
      value: '12', 
      valueClass: 'text-blue-400',
      status: '正常',
      statusClass: 'text-green-400'
    },
    { 
      label: '安全事件', 
      value: '0', 
      valueClass: 'text-green-400',
      status: '正常',
      statusClass: 'text-green-400'
    },
    { 
      label: '系统健康', 
      value: '95', 
      valueClass: 'text-green-400',
      status: '优秀',
      statusClass: 'text-green-400'
    },
  ]);

  // 监测筛选器
  const monitorFilters = ref([
    { key: 'all', label: '全部' },
    { key: 'camera', label: '摄像头' },
    { key: 'sensor', label: '传感器' },
    { key: 'access', label: '门禁' },
  ]);

  const activeMonitorFilter = ref('all');

  // 人车监测数据
  const monitorData = ref([
    {
      id: 1,
      location: '主入口监控',
      deviceId: 'CAM001',
      type: '高清摄像头',
      category: 'camera',
      status: 'online',
      icon: 'fas fa-video',
      personCount: 156,
      vehicleCount: 89,
      lastDetection: '2分钟前',
      accuracy: 98.5,
      uptime: '24天',
    },
    {
      id: 2,
      location: '车库入口监控',
      deviceId: 'CAM002',
      type: '车牌识别',
      category: 'camera',
      status: 'online',
      icon: 'fas fa-car',
      personCount: 45,
      vehicleCount: 156,
      lastDetection: '1分钟前',
      accuracy: 99.2,
      uptime: '24天',
    },
    {
      id: 3,
      location: '人员通道监控',
      deviceId: 'SEN001',
      type: '人体感应器',
      category: 'sensor',
      status: 'online',
      icon: 'fas fa-walking',
      personCount: 234,
      vehicleCount: 0,
      lastDetection: '30秒前',
      accuracy: 97.8,
      uptime: '24天',
    },
    {
      id: 4,
      location: '门禁系统A',
      deviceId: 'ACC001',
      type: '刷卡门禁',
      category: 'access',
      status: 'online',
      icon: 'fas fa-door-open',
      personCount: 189,
      vehicleCount: 0,
      lastDetection: '5分钟前',
      accuracy: 100,
      uptime: '24天',
    },
  ]);

  // 筛选后的监测数据
  const filteredMonitorData = computed(() => {
    if (activeMonitorFilter.value === 'all') {
      return monitorData.value;
    }
    return monitorData.value.filter(monitor => monitor.category === activeMonitorFilter.value);
  });

  // 工单数据
  const workOrders = ref([
    {
      id: 1,
      title: '摄像头清洁维护',
      orderNo: '*********',
      status: '已完成',
      priority: '正常',
      createTime: '2024-02-28 09:00',
      handler: '张工程师',
      description: '定期清洁主入口摄像头镜头',
    },
    {
      id: 2,
      title: '门禁系统检查',
      orderNo: '*********',
      status: '进行中',
      priority: '正常',
      createTime: '2024-02-28 10:30',
      handler: '李技师',
      description: '检查门禁系统读卡器功能',
    },
    {
      id: 3,
      title: '监控存储扩容',
      orderNo: '*********',
      status: '待处理',
      priority: '正常',
      createTime: '2024-02-28 14:00',
      handler: '王工',
      description: '监控存储空间即将满，需要扩容',
    },
  ]);

  // 安全事件统计
  const securityEvents = ref([
    { type: '异常进入', level: 'normal', count: 0, weekCount: 0, monthCount: 0 },
    { type: '设备故障', level: 'normal', count: 0, weekCount: 0, monthCount: 1 },
    { type: '网络异常', level: 'normal', count: 0, weekCount: 0, monthCount: 0 },
    { type: '权限违规', level: 'normal', count: 0, weekCount: 0, monthCount: 0 },
  ]);

  // 设备状态
  const deviceStatus = ref([
    { type: '监控摄像头', online: 24, total: 24 },
    { type: '门禁设备', online: 12, total: 12 },
    { type: '传感器', online: 8, total: 8 },
    { type: '报警器', online: 4, total: 4 },
  ]);

  const overallOnlineRate = ref('98.5');

  // 预警信息
  const alerts = ref([
    { id: 1, time: '2024-02-28 14:30', level: '正常', message: '所有安防设备运行正常' },
    { id: 2, time: '2024-02-28 10:15', level: '正常', message: '系统自检完成，无异常' },
    { id: 3, time: '2024-02-28 08:45', level: '正常', message: '夜间模式切换正常' },
  ]);

  // 系统健康度
  const systemHealth = ref([
    { component: '监控系统', score: 98, status: 'excellent' },
    { component: '门禁系统', score: 95, status: 'good' },
    { component: '报警系统', score: 97, status: 'excellent' },
    { component: '网络连接', score: 92, status: 'good' },
  ]);

  // 获取监控状态样式
  const getMonitorStatusClass = (status) => {
    switch (status) {
      case 'online':
        return 'text-green-400';
      case 'offline':
        return 'text-red-400';
      case 'maintenance':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取监控状态文本
  const getMonitorStatusText = (status) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'maintenance':
        return '维护中';
      default:
        return '未知';
    }
  };

  // 获取工单状态样式
  const getOrderStatusClass = (status) => {
    switch (status) {
      case '已完成':
        return 'text-green-400';
      case '进行中':
        return 'text-yellow-400';
      case '待处理':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取事件等级样式
  const getEventLevelClass = (level) => {
    switch (level) {
      case 'normal':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'critical':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取预警等级样式
  const getAlertLevelClass = (level) => {
    switch (level) {
      case '正常':
        return 'text-green-400';
      case '警告':
        return 'text-yellow-400';
      case '严重':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取健康状态样式
  const getHealthStatusClass = (status) => {
    switch (status) {
      case 'excellent':
        return 'text-green-400';
      case 'good':
        return 'text-blue-400';
      case 'warning':
        return 'text-yellow-400';
      case 'poor':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取健康进度条样式
  const getHealthProgressClass = (score) => {
    if (score >= 95) return 'bg-green-400';
    if (score >= 85) return 'bg-blue-400';
    if (score >= 70) return 'bg-yellow-400';
    return 'bg-red-400';
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
