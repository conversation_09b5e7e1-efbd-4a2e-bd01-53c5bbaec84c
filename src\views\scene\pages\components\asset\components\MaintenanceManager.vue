<template>
  <div class="p-[1.2vw] h-full flex gap-[1.2vw] bg-gradient-to-br from-slate-900/50 to-slate-800/50">
    <!-- 左侧：维保合同管理 -->
    <div class="flex-1 flex flex-col">
      <!-- 维保概览 - 优化版本 -->
      <div
        class="bg-gradient-to-r from-orange-500/15 to-red-500/15 backdrop-blur-sm p-[1vw] rounded-xl mb-[1.2vw] border border-orange-500/20 shadow-lg"
      >
        <div class="text-[0.9vw] text-white mb-[0.8vw] flex items-center justify-between">
          <div class="flex items-center">
            <div class="bg-orange-500/20 p-[0.3vw] rounded-lg mr-[0.5vw]">
              <ToolOutlined class="text-[1.2vw] text-orange-400" />
            </div>
            <span class="font-semibold">维保合同管理</span>
          </div>
          <button
            class="px-[0.6vw] py-[0.25vw] bg-gradient-to-r from-green-500 to-green-600 text-white text-[0.65vw] rounded-md hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 font-medium flex items-center"
            @click="showAddContract"
          >
            <ToolOutlined class="mr-[0.2vw] text-[0.7vw]" />
            新增
          </button>
        </div>

        <!-- 维保统计 - 优化版本 -->
        <div class="grid grid-cols-4 gap-[0.8vw]">
          <div class="bg-black/30 backdrop-blur-sm p-[0.8vw] rounded-lg text-center border border-white/10 hover:border-white/20 transition-colors">
            <div class="text-[1.4vw] text-white font-bold mb-[0.2vw]">{{ maintenanceStats.totalContracts }}</div>
            <div class="text-[0.7vw] text-gray-300">总合同数</div>
          </div>
          <div
            class="bg-black/30 backdrop-blur-sm p-[0.8vw] rounded-lg text-center border border-white/10 hover:border-green-400/30 transition-colors"
          >
            <div class="text-[1.4vw] text-green-400 font-bold mb-[0.2vw]">{{ maintenanceStats.activeContracts }}</div>
            <div class="text-[0.7vw] text-gray-300">有效合同</div>
          </div>
          <div
            class="bg-black/30 backdrop-blur-sm p-[0.8vw] rounded-lg text-center border border-white/10 hover:border-yellow-400/30 transition-colors"
          >
            <div class="text-[1.4vw] text-yellow-400 font-bold mb-[0.2vw]">{{ maintenanceStats.expiringSoon }}</div>
            <div class="text-[0.7vw] text-gray-300">即将到期</div>
          </div>
          <div class="bg-black/30 backdrop-blur-sm p-[0.8vw] rounded-lg text-center border border-white/10 hover:border-red-400/30 transition-colors">
            <div class="text-[1.4vw] text-red-400 font-bold mb-[0.2vw]">{{ maintenanceStats.expired }}</div>
            <div class="text-[0.7vw] text-gray-300">已过期</div>
          </div>
        </div>
      </div>

      <!-- 合同列表 - 优化版本 -->
      <div class="flex-1 bg-gradient-to-r from-slate-500/15 to-gray-500/15 backdrop-blur-sm p-[1vw] rounded-xl border border-slate-500/20 shadow-lg">
        <div class="text-[0.9vw] text-white mb-[0.8vw] font-semibold">维保合同列表</div>
        <div
          class="h-[calc(100%-2.5vw)] overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-white/20 hover:scrollbar-thumb-white/40 pr-[0.5vw]"
        >
          <table class="w-full border-collapse">
            <thead class="sticky top-0 bg-gradient-to-r from-slate-700/80 to-gray-700/80 backdrop-blur-sm z-10">
              <tr class="text-[0.75vw] text-gray-200 border-b border-white/20">
                <th class="text-left p-[0.6vw] font-semibold">合同编号</th>
                <th class="text-left p-[0.6vw] font-semibold">供应商</th>
                <th class="text-left p-[0.6vw] font-semibold">服务内容</th>
                <th class="text-left p-[0.6vw] font-semibold">开始日期</th>
                <th class="text-left p-[0.6vw] font-semibold">结束日期</th>
                <th class="text-left p-[0.6vw] font-semibold">状态</th>
                <th class="text-left p-[0.6vw] font-semibold">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="contract in maintenanceContracts"
                :key="contract.id"
                class="text-[0.75vw] border-t border-white/10 hover:bg-white/10 transition-all duration-200"
              >
                <td class="p-[0.6vw] text-white font-mono font-bold">{{ contract.contractNumber }}</td>
                <td class="p-[0.6vw] text-gray-200 font-medium">{{ contract.supplier }}</td>
                <td class="p-[0.6vw] text-gray-200">{{ contract.serviceType }}</td>
                <td class="p-[0.6vw] text-gray-200">{{ contract.startDate }}</td>
                <td class="p-[0.6vw] text-gray-200">{{ contract.endDate }}</td>
                <td class="p-[0.6vw]">
                  <span class="px-[0.5vw] py-[0.2vw] rounded-md text-[0.65vw] font-medium" :class="getContractStatusClass(contract.status)">
                    {{ contract.status }}
                  </span>
                </td>
                <td class="p-[0.4vw]">
                  <button
                    class="px-[0.4vw] py-[0.15vw] bg-gradient-to-r from-green-500 to-green-600 text-white text-[0.6vw] rounded hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
                    @click="editContract(contract)"
                  >
                    编辑
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 右侧：维保记录和提醒 - 优化版本 -->
    <div class="w-[450px] flex flex-col gap-[1.2vw]">
      <!-- 到期提醒 - 优化版本 -->
      <div class="bg-gradient-to-r from-orange-500/20 to-red-500/20 backdrop-blur-sm p-[1vw] rounded-xl border border-orange-500/30 shadow-lg">
        <div class="text-[0.9vw] text-white mb-[0.8vw] flex items-center">
          <div class="bg-orange-500/20 p-[0.2vw] rounded-lg mr-[0.4vw]">
            <BellOutlined class="text-[1vw] text-orange-400" />
          </div>
          <span class="font-semibold">到期提醒</span>
        </div>
        <div
          class="space-y-[0.5vw] max-h-[220px] overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-orange-400/20 hover:scrollbar-thumb-orange-400/40 pr-[0.3vw]"
        >
          <div
            v-for="reminder in expirationReminders"
            :key="reminder.id"
            class="bg-black/30 backdrop-blur-sm p-[0.8vw] rounded-lg border border-white/10 hover:border-orange-400/30 transition-colors"
          >
            <div class="text-[0.75vw] text-white mb-[0.3vw] font-medium">{{ reminder.contractNumber }}</div>
            <div class="text-[0.65vw] text-gray-300 mb-[0.3vw]">{{ reminder.supplier }}</div>
            <div class="text-[0.65vw] font-medium" :class="getReminderClass(reminder.daysLeft)">
              {{ reminder.daysLeft > 0 ? `${reminder.daysLeft}天后到期` : `已过期${Math.abs(reminder.daysLeft)}天` }}
            </div>
          </div>
        </div>
      </div>

      <!-- 维保记录 - 优化版本 -->
      <div class="flex-1 bg-gradient-to-r from-blue-500/15 to-indigo-500/15 backdrop-blur-sm p-[1vw] rounded-xl border border-blue-500/20 shadow-lg">
        <div class="text-[0.9vw] text-white mb-[0.8vw] flex items-center justify-between">
          <div class="flex items-center">
            <div class="bg-blue-500/20 p-[0.2vw] rounded-lg mr-[0.4vw]">
              <HistoryOutlined class="text-[1vw] text-blue-400" />
            </div>
            <span class="font-semibold">维保记录</span>
          </div>
          <button
            class="px-[0.6vw] py-[0.25vw] bg-gradient-to-r from-blue-500 to-blue-600 text-white text-[0.65vw] rounded-md hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 font-medium flex items-center"
            @click="showAddRecord"
          >
            <HistoryOutlined class="mr-[0.2vw] text-[0.7vw]" />
            添加
          </button>
        </div>
        <div
          class="h-[calc(100%-3.5vw)] overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-blue-400/20 hover:scrollbar-thumb-blue-400/40 pr-[0.3vw]"
        >
          <div class="space-y-[0.5vw]">
            <div
              v-for="record in maintenanceRecords"
              :key="record.id"
              class="bg-black/30 backdrop-blur-sm p-[0.8vw] rounded-lg border border-white/10 hover:border-blue-400/30 transition-all duration-200"
            >
              <div class="flex justify-between items-start mb-[0.5vw]">
                <div class="text-[0.75vw] text-white font-medium">{{ record.type }}</div>
                <div class="text-[0.65vw] text-gray-300">{{ record.date }}</div>
              </div>
              <div class="text-[0.65vw] text-gray-200 mb-[0.3vw] leading-relaxed">{{ record.description }}</div>
              <div class="flex justify-between items-center">
                <div class="text-[0.65vw] text-gray-300"
                  >执行人：<span class="text-white">{{ record.technician }}</span></div
                >
                <div class="text-[0.65vw] text-green-400 font-medium">¥{{ record.cost }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增/编辑合同弹窗 -->
  <ModalDialog
    v-model:visible="contractDialogVisible"
    :title="isEditMode ? '编辑维保合同' : '新增维保合同'"
    width="60vw"
    :show-footer="true"
    @confirm="saveContract"
    @cancel="contractDialogVisible = false"
  >
    <div class="p-[1vw] space-y-[0.8vw]">
      <div class="grid grid-cols-2 gap-[0.8vw]">
        <div>
          <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">合同编号</div>
          <input
            v-model="contractForm.contractNumber"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
            placeholder="请输入合同编号"
          />
        </div>
        <div>
          <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">供应商</div>
          <input
            v-model="contractForm.supplier"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
            placeholder="请输入供应商名称"
          />
        </div>
      </div>
      <div class="grid grid-cols-2 gap-[0.8vw]">
        <div>
          <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">开始日期</div>
          <input
            v-model="contractForm.startDate"
            type="date"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
          />
        </div>
        <div>
          <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">结束日期</div>
          <input
            v-model="contractForm.endDate"
            type="date"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
          />
        </div>
      </div>
      <div>
        <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">服务内容</div>
        <select
          v-model="contractForm.serviceType"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
        >
          <option value="定期维护">定期维护</option>
          <option value="故障维修">故障维修</option>
          <option value="预防性维护">预防性维护</option>
          <option value="全面维保">全面维保</option>
        </select>
      </div>
      <div>
        <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">合同金额</div>
        <input
          v-model.number="contractForm.amount"
          type="number"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
          placeholder="请输入合同金额"
        />
      </div>
      <div>
        <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">备注</div>
        <textarea
          v-model="contractForm.notes"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none resize-none"
          rows="3"
          placeholder="请输入备注信息"
        ></textarea>
      </div>
    </div>
  </ModalDialog>

  <!-- 新增维保记录弹窗 -->
  <ModalDialog
    v-model:visible="recordDialogVisible"
    title="新增维保记录"
    width="50vw"
    :show-footer="true"
    @confirm="saveRecord"
    @cancel="recordDialogVisible = false"
  >
    <div class="p-[1vw] space-y-[0.8vw]">
      <div class="grid grid-cols-2 gap-[0.8vw]">
        <div>
          <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">维保类型</div>
          <select
            v-model="recordForm.type"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
          >
            <option value="定期检查">定期检查</option>
            <option value="故障维修">故障维修</option>
            <option value="预防性维护">预防性维护</option>
            <option value="硬件更换">硬件更换</option>
            <option value="软件升级">软件升级</option>
          </select>
        </div>
        <div>
          <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">维保日期</div>
          <input
            v-model="recordForm.date"
            type="date"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
          />
        </div>
      </div>
      <div class="grid grid-cols-2 gap-[0.8vw]">
        <div>
          <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">执行人</div>
          <input
            v-model="recordForm.technician"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
            placeholder="请输入执行人姓名"
          />
        </div>
        <div>
          <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">费用</div>
          <input
            v-model.number="recordForm.cost"
            type="number"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none"
            placeholder="请输入费用"
          />
        </div>
      </div>
      <div>
        <div class="text-[0.7vw] text-gray-400 mb-[0.4vw]">维保描述</div>
        <textarea
          v-model="recordForm.description"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.7vw] outline-none resize-none"
          rows="4"
          placeholder="请输入维保工作描述"
        ></textarea>
      </div>
    </div>
  </ModalDialog>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { ToolOutlined, BellOutlined, HistoryOutlined } from '@ant-design/icons-vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';

  const props = defineProps({
    asset: {
      type: Object,
      required: true,
    },
  });

  // 状态管理
  const contractDialogVisible = ref(false);
  const recordDialogVisible = ref(false);
  const isEditMode = ref(false);
  const currentContract = ref(null);

  // 表单数据
  const contractForm = ref({
    contractNumber: '',
    supplier: '',
    serviceType: '定期维护',
    startDate: '',
    endDate: '',
    amount: 0,
    notes: '',
  });

  const recordForm = ref({
    type: '定期检查',
    date: '',
    technician: '',
    cost: 0,
    description: '',
  });

  // 模拟维保合同数据
  const maintenanceContracts = ref([
    {
      id: 1,
      contractNumber: 'MT-2024-001',
      supplier: '华为技术服务有限公司',
      serviceType: '全面维保',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      amount: 50000,
      status: '有效',
      notes: '包含硬件维修、软件升级、定期巡检等服务',
    },
    {
      id: 2,
      contractNumber: 'MT-2024-002',
      supplier: '戴尔服务中心',
      serviceType: '故障维修',
      startDate: '2024-03-01',
      endDate: '2025-02-28',
      amount: 30000,
      status: '有效',
      notes: '服务器硬件故障维修服务',
    },
    {
      id: 3,
      contractNumber: 'MT-2023-015',
      supplier: '施耐德电气',
      serviceType: '定期维护',
      startDate: '2023-06-01',
      endDate: '2024-05-31',
      amount: 25000,
      status: '即将到期',
      notes: 'UPS设备定期维护保养',
    },
    {
      id: 4,
      contractNumber: 'MT-2023-008',
      supplier: '艾默生网络能源',
      serviceType: '预防性维护',
      startDate: '2023-01-01',
      endDate: '2024-01-01',
      amount: 20000,
      status: '已过期',
      notes: '空调系统预防性维护',
    },
  ]);

  // 模拟维保记录数据
  const maintenanceRecords = ref([
    {
      id: 1,
      type: '定期检查',
      date: '2024-02-15',
      description: '服务器硬件状态检查，清理灰尘，检查风扇运行状态',
      technician: '张工程师',
      cost: 500,
    },
    {
      id: 2,
      type: '故障维修',
      date: '2024-02-10',
      description: '更换故障硬盘，恢复RAID阵列',
      technician: '李技术员',
      cost: 2500,
    },
    {
      id: 3,
      type: '预防性维护',
      date: '2024-01-20',
      description: 'UPS电池组检测和维护',
      technician: '王师傅',
      cost: 800,
    },
    {
      id: 4,
      type: '软件升级',
      date: '2024-01-15',
      description: '操作系统安全补丁更新',
      technician: '赵工程师',
      cost: 300,
    },
  ]);

  // 维保统计
  const maintenanceStats = computed(() => {
    const total = maintenanceContracts.value.length;
    const active = maintenanceContracts.value.filter((c) => c.status === '有效').length;
    const expiring = maintenanceContracts.value.filter((c) => c.status === '即将到期').length;
    const expired = maintenanceContracts.value.filter((c) => c.status === '已过期').length;

    return {
      totalContracts: total,
      activeContracts: active,
      expiringSoon: expiring,
      expired: expired,
    };
  });

  // 到期提醒
  const expirationReminders = computed(() => {
    return maintenanceContracts.value
      .filter((contract) => contract.status === '即将到期' || contract.status === '已过期')
      .map((contract) => {
        const endDate = new Date(contract.endDate);
        const today = new Date();
        const diffTime = endDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return {
          id: contract.id,
          contractNumber: contract.contractNumber,
          supplier: contract.supplier,
          daysLeft: diffDays,
        };
      });
  });

  // 获取合同状态样式
  const getContractStatusClass = (status) => {
    switch (status) {
      case '有效':
        return 'bg-green-500/20 text-green-400 border border-green-500/40';
      case '即将到期':
        return 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/40';
      case '已过期':
        return 'bg-red-500/20 text-red-400 border border-red-500/40';
      default:
        return 'bg-gray-500/20 text-gray-400 border border-gray-500/40';
    }
  };

  // 获取提醒样式
  const getReminderClass = (daysLeft) => {
    if (daysLeft < 0) return 'text-red-400';
    if (daysLeft <= 30) return 'text-yellow-400';
    return 'text-green-400';
  };

  // 功能函数
  const showAddContract = () => {
    isEditMode.value = false;
    contractForm.value = {
      contractNumber: '',
      supplier: '',
      serviceType: '定期维护',
      startDate: '',
      endDate: '',
      amount: 0,
      notes: '',
    };
    contractDialogVisible.value = true;
  };

  const editContract = (contract) => {
    isEditMode.value = true;
    currentContract.value = contract;
    contractForm.value = { ...contract };
    contractDialogVisible.value = true;
  };

  const saveContract = () => {
    if (isEditMode.value) {
      // 编辑模式
      const index = maintenanceContracts.value.findIndex((c) => c.id === currentContract.value.id);
      if (index !== -1) {
        maintenanceContracts.value[index] = { ...contractForm.value, id: currentContract.value.id };
      }
    } else {
      // 新增模式
      const newContract = {
        ...contractForm.value,
        id: Date.now(),
        status: '有效',
      };
      maintenanceContracts.value.push(newContract);
    }
    contractDialogVisible.value = false;
  };

  const showAddRecord = () => {
    recordForm.value = {
      type: '定期检查',
      date: new Date().toISOString().split('T')[0],
      technician: '',
      cost: 0,
      description: '',
    };
    recordDialogVisible.value = true;
  };

  const saveRecord = () => {
    const newRecord = {
      ...recordForm.value,
      id: Date.now(),
    };
    maintenanceRecords.value.unshift(newRecord);
    recordDialogVisible.value = false;
  };
</script>
