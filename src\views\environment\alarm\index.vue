<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <template #toolbar> </template>
    </BasicTable>
  </div>
</template>

<script setup lang="ts">
  import { BasicTable, useTable } from '/@/components/Table';
  import { getBasicColumns } from './data';
  import { getCmccAlarmList } from '/@/api/environment';

  const [registerTable] = useTable({
    title: '告警数据',
    api: getCmccAlarmList,
    columns: getBasicColumns(),
    showTableSetting: true,
    showIndexColumn: true,
    canResize: true,
    autoCreateKey: true,
  });
</script>
