<template>
  <div class="custom-slider relative" :style="style">
    <div
      ref="trackRef"
      :class="trackClasses"
      @click="handleTrackClick"
    >
      <!-- 已填充的轨道 -->
      <div
        :class="fillClasses"
        :style="fillStyle"
      ></div>
      
      <!-- 滑块手柄 -->
      <div
        ref="thumbRef"
        :class="thumbClasses"
        :style="thumbStyle"
        @mousedown="handleMouseDown"
        @touchstart="handleTouchStart"
      >
        <!-- 工具提示 -->
        <div
          v-if="showTooltip"
          :class="tooltipClasses"
        >
          {{ tooltipText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue';

  // Props
  interface Props {
    value?: number;
    min?: number;
    max?: number;
    step?: number;
    disabled?: boolean;
    tooltipFormatter?: (value: number) => string;
    style?: Record<string, any>;
  }

  const props = withDefaults(defineProps<Props>(), {
    value: 0,
    min: 0,
    max: 100,
    step: 1,
    disabled: false,
  });

  // Emits
  const emit = defineEmits<{
    'update:value': [value: number];
    change: [value: number];
  }>();

  // 响应式数据
  const trackRef = ref<HTMLElement>();
  const thumbRef = ref<HTMLElement>();
  const isDragging = ref(false);
  const showTooltip = ref(false);

  // 计算属性
  const percentage = computed(() => {
    const range = props.max - props.min;
    return range > 0 ? ((props.value - props.min) / range) * 100 : 0;
  });

  const trackClasses = computed(() => [
    'relative',
    'w-full',
    'h-2',
    'bg-[#2a3441]',
    'rounded-full',
    'cursor-pointer',
    props.disabled ? 'cursor-not-allowed' : '',
  ]);

  const fillClasses = computed(() => [
    'absolute',
    'top-0',
    'left-0',
    'h-full',
    'bg-[#3B8EE6]',
    'rounded-full',
    'transition-all',
    'duration-200',
  ]);

  const fillStyle = computed(() => ({
    width: `${percentage.value}%`,
  }));

  const thumbClasses = computed(() => [
    'absolute',
    'top-1/2',
    'w-4',
    'h-4',
    'bg-white',
    'border-2',
    'border-[#3B8EE6]',
    'rounded-full',
    'transform',
    '-translate-y-1/2',
    'cursor-pointer',
    'transition-all',
    'duration-200',
    'shadow-md',
    isDragging.value ? 'scale-110' : 'hover:scale-105',
    props.disabled ? 'cursor-not-allowed bg-gray-400 border-gray-500' : '',
  ]);

  const thumbStyle = computed(() => ({
    left: `calc(${percentage.value}% - 8px)`,
  }));

  const tooltipClasses = computed(() => [
    'absolute',
    'bottom-8',
    'left-1/2',
    'transform',
    '-translate-x-1/2',
    'px-2',
    'py-1',
    'bg-[#1a2332]',
    'text-white',
    'text-xs',
    'rounded',
    'whitespace-nowrap',
    'shadow-lg',
    'border',
    'border-[#3a4551]',
  ]);

  const tooltipText = computed(() => {
    if (props.tooltipFormatter) {
      return props.tooltipFormatter(props.value);
    }
    return props.value.toString();
  });

  // 方法
  const getValueFromPosition = (clientX: number): number => {
    if (!trackRef.value) return props.value;

    const rect = trackRef.value.getBoundingClientRect();
    const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    const range = props.max - props.min;
    const rawValue = props.min + percentage * range;
    
    // 按步长调整
    const steps = Math.round((rawValue - props.min) / props.step);
    return props.min + steps * props.step;
  };

  const updateValue = (newValue: number) => {
    const clampedValue = Math.max(props.min, Math.min(props.max, newValue));
    if (clampedValue !== props.value) {
      emit('update:value', clampedValue);
      emit('change', clampedValue);
    }
  };

  const handleTrackClick = (event: MouseEvent) => {
    if (props.disabled) return;
    
    const newValue = getValueFromPosition(event.clientX);
    updateValue(newValue);
  };

  const handleMouseDown = (event: MouseEvent) => {
    if (props.disabled) return;
    
    event.preventDefault();
    isDragging.value = true;
    showTooltip.value = true;
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (event: MouseEvent) => {
    if (!isDragging.value) return;
    
    const newValue = getValueFromPosition(event.clientX);
    updateValue(newValue);
  };

  const handleMouseUp = () => {
    isDragging.value = false;
    showTooltip.value = false;
    
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  const handleTouchStart = (event: TouchEvent) => {
    if (props.disabled) return;
    
    event.preventDefault();
    isDragging.value = true;
    showTooltip.value = true;
    
    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleTouchEnd);
  };

  const handleTouchMove = (event: TouchEvent) => {
    if (!isDragging.value) return;
    
    const touch = event.touches[0];
    const newValue = getValueFromPosition(touch.clientX);
    updateValue(newValue);
  };

  const handleTouchEnd = () => {
    isDragging.value = false;
    showTooltip.value = false;
    
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
  };

  // 生命周期
  onMounted(() => {
    // 鼠标悬停显示工具提示
    if (thumbRef.value) {
      thumbRef.value.addEventListener('mouseenter', () => {
        if (!isDragging.value) {
          showTooltip.value = true;
        }
      });
      
      thumbRef.value.addEventListener('mouseleave', () => {
        if (!isDragging.value) {
          showTooltip.value = false;
        }
      });
    }
  });

  onUnmounted(() => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
  });
</script>

<style scoped>
  .custom-slider {
    padding: 8px 0;
  }

  .custom-slider:hover .fill {
    background-color: #4A9EF7;
  }
</style>
