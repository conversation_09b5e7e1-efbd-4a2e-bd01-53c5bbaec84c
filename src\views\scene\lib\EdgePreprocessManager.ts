import * as THREE from 'three';
import { ModelCache } from './load/ModelCache';

// 定义边缘处理状态
export enum ProcessingState {
  IDLE = 'idle',
  PROCESSING = 'processing',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ERROR = 'error'
}

// 定义处理进度接口
export interface ProcessingProgress {
  total: number;
  processed: number;
  percentage: number;
}

// 定义边缘处理管理器
export class EdgePreprocessManager {
  private static instance: EdgePreprocessManager | null = null;
  
  // Worker池
  private workers: Worker[] = [];
  private maxWorkers: number;
  private activeWorkers: number = 0;
  
  // 处理状态
  private state: ProcessingState = ProcessingState.IDLE;
  private progress: ProcessingProgress = { total: 0, processed: 0, percentage: 0 };
  
  // 处理队列
  private meshQueue: THREE.Mesh[] = [];
  private processedMeshes: Set<string> = new Set();
  
  // 缓存
  private modelCache: ModelCache;
  private cacheKey: string = '';
  private cacheVersion: string = '1.0';
  
  // 回调函数
  private onProgressCallback: ((progress: ProcessingProgress) => void) | null = null;
  private onCompleteCallback: (() => void) | null = null;
  private onErrorCallback: ((error: Error) => void) | null = null;
  
  // 控制标志
  private shouldPause: boolean = false;
  private shouldCancel: boolean = false;
  
  // 批处理ID
  private currentBatchId: number = 0;

  private constructor() {
    this.maxWorkers = Math.max(1, navigator.hardwareConcurrency ? Math.min(navigator.hardwareConcurrency - 1, 4) : 2);
    this.modelCache = new ModelCache();
    this.initWorkers();
  }

  public static getInstance(): EdgePreprocessManager {
    if (!EdgePreprocessManager.instance) {
      EdgePreprocessManager.instance = new EdgePreprocessManager();
    }
    return EdgePreprocessManager.instance;
  }

  // 初始化Worker池
  private initWorkers(): void {
    for (let i = 0; i < this.maxWorkers; i++) {
      const worker = new Worker(new URL('../workers/edgePreprocessor.worker.ts', import.meta.url), { type: 'module' });
      worker.onmessage = this.handleWorkerMessage.bind(this);
      this.workers.push(worker);
    }
  }

  // 处理Worker消息
  private handleWorkerMessage(event: MessageEvent): void {
    const { meshId, edgesData, error, batchId } = event.data;
    
    // 忽略旧批次的消息
    if (batchId !== this.currentBatchId) {
      this.activeWorkers--;
      this.processNextMesh();
      return;
    }
    
    // 更新进度
    this.progress.processed++;
    this.progress.percentage = Math.floor((this.progress.processed / this.progress.total) * 100);
    
    if (this.onProgressCallback) {
      this.onProgressCallback(this.progress);
    }
    
    // 处理结果
    if (!error && edgesData) {
      // 找到对应的网格
      const mesh = this.meshQueue.find(m => m.uuid === meshId);
      if (mesh) {
        // 创建边缘几何体
        const edgeGeometry = new THREE.BufferGeometry();
        edgeGeometry.setAttribute('position', new THREE.Float32BufferAttribute(edgesData.position, 3));
        if (edgesData.index) {
          edgeGeometry.setIndex(new THREE.BufferAttribute(new Uint32Array(edgesData.index), 1));
        }
        
        // 保存到网格的userData
        mesh.userData.precomputedEdgeGeometry = edgeGeometry;
        this.processedMeshes.add(meshId);
      }
    } else if (error) {
      console.warn(`边缘处理错误 (${meshId}): ${error}`);
    }
    
    // 减少活动Worker计数
    this.activeWorkers--;
    
    // 处理下一个网格或完成处理
    if (this.shouldCancel) {
      if (this.activeWorkers === 0) {
        this.finishProcessing();
      }
    } else {
      this.processNextMesh();
    }
  }

  // 处理下一个网格
  private processNextMesh(): void {
    if (this.shouldPause || this.shouldCancel) {
      if (this.activeWorkers === 0) {
        this.state = this.shouldPause ? ProcessingState.PAUSED : ProcessingState.IDLE;
      }
      return;
    }
    
    if (this.meshQueue.length === 0) {
      if (this.activeWorkers === 0) {
        this.finishProcessing();
      }
      return;
    }
    
    // 获取空闲Worker
    const availableWorkerIndex = this.workers.findIndex((_, index) => 
      this.activeWorkers < this.maxWorkers
    );
    
    if (availableWorkerIndex === -1) return;
    
    // 获取下一个要处理的网格
    const mesh = this.meshQueue.shift();
    if (!mesh || !mesh.geometry) return;
    
    this.activeWorkers++;
    
    // 提取几何体数据
    const geometry = mesh.geometry;
    const geometryData = {
      position: Array.from(geometry.attributes.position.array),
      index: geometry.index ? Array.from(geometry.index.array) : undefined,
      normal: geometry.attributes.normal ? Array.from(geometry.attributes.normal.array) : undefined,
      uv: geometry.attributes.uv ? Array.from(geometry.attributes.uv.array) : undefined
    };
    
    // 发送到Worker处理
    this.workers[availableWorkerIndex].postMessage({
      geometryData,
      threshold: 30, // 边缘检测阈值
      meshId: mesh.uuid,
      batchId: this.currentBatchId
    });
    
    // 如果还有Worker可用，继续处理下一个
    if (this.activeWorkers < this.maxWorkers) {
      this.processNextMesh();
    }
  }

  // 完成处理
  private finishProcessing(): void {
    // 保存到缓存
    this.saveCacheData();
    
    this.state = ProcessingState.COMPLETED;
    if (this.onCompleteCallback) {
      this.onCompleteCallback();
    }
  }

  // 保存缓存数据
  private async saveCacheData(): Promise<void> {
    if (!this.cacheKey || this.processedMeshes.size === 0) return;
    
    try {
      // 这里可以实现缓存逻辑
      console.log(`[EdgePreprocessManager] 缓存处理结果: ${this.processedMeshes.size} 个网格`);
    } catch (error) {
      console.error('缓存边缘数据失败:', error);
    }
  }

  // 开始处理
  public async process(meshes: THREE.Mesh[], cacheKey: string = ''): Promise<void> {
    if (this.state === ProcessingState.PROCESSING) {
      return;
    }
    
    // 重置状态
    this.resetState();
    this.cacheKey = cacheKey;
    this.currentBatchId++;
    
    // 过滤掉已经处理过的网格
    const meshesToProcess = meshes.filter(mesh => 
      !mesh.userData.precomputedEdgeGeometry
    );
    
    if (meshesToProcess.length === 0) {
      this.progress = { total: 0, processed: 0, percentage: 100 };
      this.state = ProcessingState.COMPLETED;
      if (this.onCompleteCallback) {
        this.onCompleteCallback();
      }
      return;
    }
    
    // 设置进度
    this.progress = {
      total: meshesToProcess.length,
      processed: 0,
      percentage: 0
    };
    
    // 更新状态
    this.state = ProcessingState.PROCESSING;
    this.meshQueue = [...meshesToProcess];
    
    // 开始处理
    for (let i = 0; i < this.maxWorkers && this.meshQueue.length > 0; i++) {
      this.processNextMesh();
    }
  }

  // 暂停处理
  public pause(): void {
    if (this.state !== ProcessingState.PROCESSING) return;
    
    this.shouldPause = true;
    if (this.activeWorkers === 0) {
      this.state = ProcessingState.PAUSED;
    }
  }

  // 恢复处理
  public resume(): void {
    if (this.state !== ProcessingState.PAUSED) return;
    
    this.shouldPause = false;
    this.state = ProcessingState.PROCESSING;
    
    // 恢复处理
    for (let i = 0; i < this.maxWorkers && this.meshQueue.length > 0; i++) {
      this.processNextMesh();
    }
  }

  // 取消处理
  public cancel(): void {
    this.shouldCancel = true;
    this.meshQueue = [];
    
    if (this.activeWorkers === 0) {
      this.resetState();
    }
  }

  // 重置状态
  private resetState(): void {
    this.state = ProcessingState.IDLE;
    this.progress = { total: 0, processed: 0, percentage: 0 };
    this.meshQueue = [];
    this.processedMeshes.clear();
    this.shouldPause = false;
    this.shouldCancel = false;
  }

  // 设置回调
  public onProgress(callback: (progress: ProcessingProgress) => void): void {
    this.onProgressCallback = callback;
  }

  public onComplete(callback: () => void): void {
    this.onCompleteCallback = callback;
  }

  public onError(callback: (error: Error) => void): void {
    this.onErrorCallback = callback;
  }

  // 获取当前状态
  public getState(): ProcessingState {
    return this.state;
  }

  public getProgress(): ProcessingProgress {
    return this.progress;
  }

  // 销毁
  public destroy(): void {
    this.cancel();
    this.workers.forEach(worker => worker.terminate());
    this.workers = [];
    EdgePreprocessManager.instance = null;
  }
}
