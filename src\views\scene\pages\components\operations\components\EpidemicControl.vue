<template>
  <div class="w-full h-full flex flex-col">
    <div class="h-[1.6vw] shrink-0 relative">
      <!-- 背景图片 -->
      <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />

      <!-- 标题文字 - 绝对定位确保与背景图片对齐 -->
      <div class="absolute left-[1vw] top-1/2 transform -translate-y-1/2 text-white text-[0.7vw] font-medium z-10"> 疫情防控系统 </div>

      <!-- 详情管理按钮 - 绝对定位到右侧 -->
      <div
        class="absolute right-[0.5vw] top-1/2 transform -translate-y-1/2 px-[0.6vw] py-[0.25vw] bg-[#3B8EE6]/20 rounded cursor-pointer hover:bg-[#3B8EE6]/30 transition-all text-[0.6vw] text-[#3B8EE6] border border-[#3B8EE6]/30 hover:border-[#3B8EE6]/50 z-10"
        @click="showFullInterface"
      >
        详情管理
      </div>
    </div>
    <div class="flex-1 flex flex-col h-[calc(100%-2vw)] mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw]">
      <!-- 简化的健康统计 -->
      <div class="grid grid-cols-2 gap-[0.4vw] mb-[0.6vw]">
        <div class="text-center p-[0.4vw] bg-[#4CAF50]/10 rounded">
          <div class="text-[#4CAF50] text-[1.2vw] font-bold">{{ healthStats.greenCode }}</div>
          <div class="text-white/80 text-[0.6vw]">绿码人数</div>
        </div>
        <div class="text-center p-[0.4vw] bg-[#3B8EE6]/10 rounded">
          <div class="text-[#3B8EE6] text-[1.2vw] font-bold">{{ vaccineRate }}%</div>
          <div class="text-white/80 text-[0.6vw]">疫苗接种率</div>
        </div>
      </div>

      <!-- 状态指示器 -->
      <div class="flex items-center justify-between mb-[0.6vw]">
        <div class="flex items-center gap-[0.3vw]">
          <div class="w-[0.4vw] h-[0.4vw] bg-[#FF9800] rounded-full"></div>
          <span class="text-white/80 text-[0.6vw]">黄码: {{ healthStats.yellowCode }}</span>
        </div>
        <div class="flex items-center gap-[0.3vw]">
          <div class="w-[0.4vw] h-[0.4vw] bg-[#FF5252] rounded-full"></div>
          <span class="text-white/80 text-[0.6vw]">体温异常: {{ healthStats.abnormalTemp }}</span>
        </div>
      </div>

      <!-- 今日健康检查记录 -->
      <div class="flex-1 overflow-hidden">
        <div class="text-white/80 text-[0.6vw] mb-[0.4vw]">今日健康检查记录</div>
        <div class="h-[calc(100%-1.5vw)] overflow-y-auto custom-scrollbar">
          <div
            v-for="(record, index) in healthRecords"
            :key="index"
            class="flex items-center justify-between p-[0.4vw] mb-[0.3vw] bg-[#3B8EE6]/10 rounded hover:bg-[#3B8EE6]/20 transition-all"
          >
            <div class="flex items-center gap-[0.4vw]">
              <div class="text-white text-[0.6vw]">{{ record.name }}</div>
              <div :class="getHealthCodeClass(record.healthCode)" class="text-[0.5vw]">
                {{ getHealthCodeText(record.healthCode) }}
              </div>
              <div :class="record.temperature > 37.3 ? 'text-[#FF5252]' : 'text-[#4CAF50]'" class="text-[0.5vw]"> {{ record.temperature }}°C </div>
            </div>
            <div class="flex items-center gap-[0.3vw]">
              <div class="text-white/60 text-[0.5vw]">{{ record.time }}</div>
              <div v-if="record.riskLevel" :class="getRiskLevelClass(record.riskLevel)" class="text-[0.5vw]">
                {{ record.riskLevel }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 健康检查弹窗 -->
    <ModalDialog
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :iconSrc="dashboardTitle"
      :width="dialogType === 'full' ? '80vw' : '50vw'"
      :height="dialogType === 'full' ? '80vh' : '60vh'"
      :show-footer="false"
      @close="handleDialogClose"
    >
      <div class="p-[1vw]">
        <!-- 健康检查表单 -->
        <div v-if="dialogType === 'health'" class="space-y-[0.8vw]">
          <div class="grid grid-cols-2 gap-[0.8vw]">
            <div>
              <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">姓名</label>
              <input
                v-model="healthForm.name"
                class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
                placeholder="请输入姓名"
              />
            </div>
            <div>
              <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">联系电话</label>
              <input
                v-model="healthForm.phone"
                class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
                placeholder="请输入联系电话"
              />
            </div>
          </div>
          <div class="grid grid-cols-3 gap-[0.8vw]">
            <div>
              <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">体温</label>
              <input
                v-model="healthForm.temperature"
                type="number"
                step="0.1"
                class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
                placeholder="36.5"
              />
            </div>
            <div>
              <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">健康码</label>
              <select
                v-model="healthForm.healthCode"
                class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
              >
                <option value="green">绿码</option>
                <option value="yellow">黄码</option>
                <option value="red">红码</option>
              </select>
            </div>
            <div>
              <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">疫苗状态</label>
              <select
                v-model="healthForm.vaccineStatus"
                class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
              >
                <option value="none">未接种</option>
                <option value="partial">部分接种</option>
                <option value="full">完全接种</option>
                <option value="booster">加强针</option>
              </select>
            </div>
          </div>
          <div class="flex justify-end gap-[0.5vw] mt-[1vw]">
            <button @click="closeDialog" class="px-[1vw] py-[0.4vw] bg-[#666]/20 text-white rounded hover:bg-[#666]/30 transition-all text-[0.6vw]">
              取消
            </button>
            <button
              @click="submitHealthCheck"
              class="px-[1vw] py-[0.4vw] bg-[#4CAF50]/20 text-[#4CAF50] rounded hover:bg-[#4CAF50]/30 transition-all text-[0.6vw]"
            >
              提交
            </button>
          </div>
        </div>

        <!-- 完整管理界面 -->
        <div v-else-if="dialogType === 'full'" class="h-[calc(80vh-8vw)] overflow-hidden">
          <!-- 标签页导航 -->
          <div class="flex border-b border-[#3B8EE6]/30 mb-[1vw]">
            <button
              v-for="tab in managementTabs"
              :key="tab.key"
              @click="activeTab = tab.key"
              :class="[
                'px-[1vw] py-[0.5vw] text-[0.6vw] transition-all bg-transparent',
                activeTab === tab.key ? 'text-[#3B8EE6] border-b-2 border-[#3B8EE6]' : 'text-white/60 hover:text-white/80',
              ]"
            >
              {{ tab.label }}
            </button>
          </div>

          <!-- 标签页内容 -->
          <div class="h-[calc(100%-3vw)] overflow-y-auto custom-scrollbar">
            <!-- 健康监控 -->
            <div v-if="activeTab === 'health'" class="space-y-[1vw]">
              <div class="grid grid-cols-4 gap-[1vw]">
                <div class="bg-[#4CAF50]/10 border border-[#4CAF50]/30 rounded p-[0.8vw]">
                  <div class="text-[#4CAF50] text-[0.8vw] font-bold">{{ healthStats.greenCode }}</div>
                  <div class="text-white/60 text-[0.5vw]">绿码人数</div>
                </div>
                <div class="bg-[#FF9800]/10 border border-[#FF9800]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF9800] text-[0.8vw] font-bold">{{ healthStats.yellowCode }}</div>
                  <div class="text-white/60 text-[0.5vw]">黄码人数</div>
                </div>
                <div class="bg-[#FF5252]/10 border border-[#FF5252]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF5252] text-[0.8vw] font-bold">{{ healthStats.redCode }}</div>
                  <div class="text-white/60 text-[0.5vw]">红码人数</div>
                </div>
                <div class="bg-[#FF5252]/10 border border-[#FF5252]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF5252] text-[0.8vw] font-bold">{{ healthStats.abnormalTemp }}</div>
                  <div class="text-white/60 text-[0.5vw]">体温异常</div>
                </div>
              </div>

              <!-- 今日健康检查记录 -->
              <div class="bg-[#15274D]/30 border border-[#3B8EE6]/30 rounded p-[1vw]">
                <div class="flex justify-between items-center mb-[0.8vw]">
                  <div class="text-white text-[0.7vw] font-bold">今日健康检查记录</div>
                  <button
                    @click="showHealthCheck"
                    class="px-[0.8vw] py-[0.3vw] bg-[#4CAF50]/20 text-[#4CAF50] rounded text-[0.5vw] hover:bg-[#4CAF50]/30"
                  >
                    新增登记
                  </button>
                </div>
                <div class="space-y-[0.5vw] max-h-[15vw] overflow-y-auto custom-scrollbar">
                  <div
                    v-for="(record, index) in healthRecords"
                    :key="index"
                    class="flex items-center justify-between p-[0.5vw] bg-[#3B8EE6]/10 rounded hover:bg-[#3B8EE6]/20 transition-all"
                  >
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white text-[0.6vw]">{{ record.name }}</div>
                      <div :class="getHealthCodeClass(record.healthCode)" class="text-[0.5vw]">
                        {{ getHealthCodeText(record.healthCode) }}
                      </div>
                      <div :class="record.temperature > 37.3 ? 'text-[#FF5252]' : 'text-[#4CAF50]'" class="text-[0.5vw]">
                        {{ record.temperature }}°C
                      </div>
                    </div>
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white/60 text-[0.5vw]">{{ record.time }}</div>
                      <div v-if="record.riskLevel" :class="getRiskLevelClass(record.riskLevel)" class="text-[0.5vw]">
                        {{ record.riskLevel }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 体温监测 -->
            <div v-else-if="activeTab === 'temperature'" class="space-y-[1vw]">
              <div class="grid grid-cols-3 gap-[1vw]">
                <div class="bg-[#4CAF50]/10 border border-[#4CAF50]/30 rounded p-[0.8vw]">
                  <div class="text-[#4CAF50] text-[0.8vw] font-bold">{{ temperatureStats.normal }}</div>
                  <div class="text-white/60 text-[0.5vw]">正常体温</div>
                </div>
                <div class="bg-[#FF9800]/10 border border-[#FF9800]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF9800] text-[0.8vw] font-bold">{{ temperatureStats.warning }}</div>
                  <div class="text-white/60 text-[0.5vw]">体温预警</div>
                </div>
                <div class="bg-[#FF5252]/10 border border-[#FF5252]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF5252] text-[0.8vw] font-bold">{{ temperatureStats.abnormal }}</div>
                  <div class="text-white/60 text-[0.5vw]">体温异常</div>
                </div>
              </div>

              <!-- 体温检测记录 -->
              <div class="bg-[#15274D]/30 border border-[#3B8EE6]/30 rounded p-[1vw]">
                <div class="flex justify-between items-center mb-[0.8vw]">
                  <div class="text-white text-[0.7vw] font-bold">体温检测记录</div>
                  <button
                    @click="showTemperatureCheck"
                    class="px-[0.8vw] py-[0.3vw] bg-[#3B8EE6]/20 text-[#3B8EE6] rounded text-[0.5vw] hover:bg-[#3B8EE6]/30"
                  >
                    手动检测
                  </button>
                </div>
                <div class="space-y-[0.5vw] max-h-[15vw] overflow-y-auto custom-scrollbar">
                  <div
                    v-for="(record, index) in temperatureRecords"
                    :key="index"
                    class="flex items-center justify-between p-[0.5vw] bg-[#3B8EE6]/10 rounded hover:bg-[#3B8EE6]/20 transition-all"
                  >
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white text-[0.6vw]">{{ record.name }}</div>
                      <div :class="getTemperatureClass(record.temperature)" class="text-[0.6vw] font-bold"> {{ record.temperature }}°C </div>
                      <div class="text-white/60 text-[0.5vw]">{{ record.location }}</div>
                    </div>
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white/60 text-[0.5vw]">{{ record.time }}</div>
                      <div :class="getTemperatureStatusClass(record.status)" class="text-[0.5vw]">
                        {{ record.status }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 疫苗管理 -->
            <div v-else-if="activeTab === 'vaccine'" class="space-y-[1vw]">
              <div class="grid grid-cols-4 gap-[1vw]">
                <div class="bg-[#4CAF50]/10 border border-[#4CAF50]/30 rounded p-[0.8vw]">
                  <div class="text-[#4CAF50] text-[0.8vw] font-bold">{{ vaccineStats.completed }}</div>
                  <div class="text-white/60 text-[0.5vw]">完全接种</div>
                </div>
                <div class="bg-[#FF9800]/10 border border-[#FF9800]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF9800] text-[0.8vw] font-bold">{{ vaccineStats.partial }}</div>
                  <div class="text-white/60 text-[0.5vw]">部分接种</div>
                </div>
                <div class="bg-[#3B8EE6]/10 border border-[#3B8EE6]/30 rounded p-[0.8vw]">
                  <div class="text-[#3B8EE6] text-[0.8vw] font-bold">{{ vaccineStats.booster }}</div>
                  <div class="text-white/60 text-[0.5vw]">加强针</div>
                </div>
                <div class="bg-[#FF5252]/10 border border-[#FF5252]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF5252] text-[0.8vw] font-bold">{{ vaccineStats.unvaccinated }}</div>
                  <div class="text-white/60 text-[0.5vw]">未接种</div>
                </div>
              </div>

              <!-- 疫苗接种记录 -->
              <div class="bg-[#15274D]/30 border border-[#3B8EE6]/30 rounded p-[1vw]">
                <div class="flex justify-between items-center mb-[0.8vw]">
                  <div class="text-white text-[0.7vw] font-bold">疫苗接种记录</div>
                  <button
                    @click="showVaccineRecord"
                    class="px-[0.8vw] py-[0.3vw] bg-[#4CAF50]/20 text-[#4CAF50] rounded text-[0.5vw] hover:bg-[#4CAF50]/30"
                  >
                    登记接种
                  </button>
                </div>
                <div class="space-y-[0.5vw] max-h-[15vw] overflow-y-auto custom-scrollbar">
                  <div
                    v-for="(record, index) in vaccineRecords"
                    :key="index"
                    class="flex items-center justify-between p-[0.5vw] bg-[#3B8EE6]/10 rounded hover:bg-[#3B8EE6]/20 transition-all"
                  >
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white text-[0.6vw]">{{ record.name }}</div>
                      <div class="text-white/60 text-[0.5vw]">{{ record.vaccine }}</div>
                      <div :class="getVaccineStatusClass(record.status)" class="text-[0.5vw]">
                        {{ record.status }}
                      </div>
                    </div>
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white/60 text-[0.5vw]">{{ record.date }}</div>
                      <div class="text-white/60 text-[0.5vw]">{{ record.location }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 核酸检测 -->
            <div v-else-if="activeTab === 'nucleic'" class="space-y-[1vw]">
              <div class="grid grid-cols-3 gap-[1vw]">
                <div class="bg-[#4CAF50]/10 border border-[#4CAF50]/30 rounded p-[0.8vw]">
                  <div class="text-[#4CAF50] text-[0.8vw] font-bold">{{ nucleicStats.negative }}</div>
                  <div class="text-white/60 text-[0.5vw]">阴性结果</div>
                </div>
                <div class="bg-[#FF9800]/10 border border-[#FF9800]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF9800] text-[0.8vw] font-bold">{{ nucleicStats.pending }}</div>
                  <div class="text-white/60 text-[0.5vw]">待出结果</div>
                </div>
                <div class="bg-[#FF5252]/10 border border-[#FF5252]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF5252] text-[0.8vw] font-bold">{{ nucleicStats.positive }}</div>
                  <div class="text-white/60 text-[0.5vw]">阳性结果</div>
                </div>
              </div>

              <!-- 核酸检测记录 -->
              <div class="bg-[#15274D]/30 border border-[#3B8EE6]/30 rounded p-[1vw]">
                <div class="flex justify-between items-center mb-[0.8vw]">
                  <div class="text-white text-[0.7vw] font-bold">核酸检测记录</div>
                  <button
                    @click="addNucleicTest"
                    class="px-[0.8vw] py-[0.3vw] bg-[#3B8EE6]/20 text-[#3B8EE6] rounded text-[0.5vw] hover:bg-[#3B8EE6]/30"
                  >
                    登记检测
                  </button>
                </div>
                <div class="space-y-[0.5vw] max-h-[15vw] overflow-y-auto custom-scrollbar">
                  <div
                    v-for="(record, index) in nucleicRecords"
                    :key="index"
                    class="flex items-center justify-between p-[0.5vw] bg-[#3B8EE6]/10 rounded hover:bg-[#3B8EE6]/20 transition-all"
                  >
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white text-[0.6vw]">{{ record.name }}</div>
                      <div :class="getNucleicResultClass(record.result)" class="text-[0.5vw] font-bold">
                        {{ record.result }}
                      </div>
                      <div class="text-white/60 text-[0.5vw]">{{ record.testType }}</div>
                    </div>
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white/60 text-[0.5vw]">{{ record.testDate }}</div>
                      <div class="text-white/60 text-[0.5vw]">{{ record.location }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 风险管理 -->
            <div v-else-if="activeTab === 'risk'" class="space-y-[1vw]">
              <div class="grid grid-cols-3 gap-[1vw]">
                <div class="bg-[#FF5252]/10 border border-[#FF5252]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF5252] text-[0.8vw] font-bold">{{ riskStats.high }}</div>
                  <div class="text-white/60 text-[0.5vw]">高风险人员</div>
                </div>
                <div class="bg-[#FF9800]/10 border border-[#FF9800]/30 rounded p-[0.8vw]">
                  <div class="text-[#FF9800] text-[0.8vw] font-bold">{{ riskStats.medium }}</div>
                  <div class="text-white/60 text-[0.5vw]">中风险人员</div>
                </div>
                <div class="bg-[#FFC107]/10 border border-[#FFC107]/30 rounded p-[0.8vw]">
                  <div class="text-[#FFC107] text-[0.8vw] font-bold">{{ riskStats.low }}</div>
                  <div class="text-white/60 text-[0.5vw]">低风险人员</div>
                </div>
              </div>

              <!-- 风险人员列表 -->
              <div class="bg-[#15274D]/30 border border-[#3B8EE6]/30 rounded p-[1vw]">
                <div class="flex justify-between items-center mb-[0.8vw]">
                  <div class="text-white text-[0.7vw] font-bold">风险人员管理</div>
                  <button
                    @click="showRiskManagement"
                    class="px-[0.8vw] py-[0.3vw] bg-[#FF9800]/20 text-[#FF9800] rounded text-[0.5vw] hover:bg-[#FF9800]/30"
                  >
                    风险评估
                  </button>
                </div>
                <div class="space-y-[0.5vw] max-h-[15vw] overflow-y-auto custom-scrollbar">
                  <div
                    v-for="(person, index) in riskPersons"
                    :key="index"
                    class="flex items-center justify-between p-[0.5vw] bg-[#3B8EE6]/10 rounded hover:bg-[#3B8EE6]/20 transition-all"
                  >
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white text-[0.6vw]">{{ person.name }}</div>
                      <div :class="getRiskLevelClass(person.riskLevel)" class="text-[0.5vw] font-bold">
                        {{ person.riskLevel }}
                      </div>
                      <div class="text-white/60 text-[0.5vw]">{{ person.reason }}</div>
                    </div>
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white/60 text-[0.5vw]">{{ person.updateTime }}</div>
                      <button
                        @click="handleRiskPerson(person)"
                        class="px-[0.5vw] py-[0.2vw] bg-[#3B8EE6]/20 text-[#3B8EE6] rounded text-[0.4vw] hover:bg-[#3B8EE6]/30"
                      >
                        处理
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 防控设置 -->
            <div v-else-if="activeTab === 'settings'" class="space-y-[1vw]">
              <div class="bg-[#15274D]/30 border border-[#3B8EE6]/30 rounded p-[1vw]">
                <div class="text-white text-[0.7vw] font-bold mb-[0.8vw]">防控措施配置</div>
                <div class="grid grid-cols-2 gap-[1vw]">
                  <div>
                    <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">体温阈值</label>
                    <input
                      v-model="controlSettings.temperatureThreshold"
                      type="number"
                      step="0.1"
                      class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
                    />
                  </div>
                  <div>
                    <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">核酸有效期(天)</label>
                    <input
                      v-model="controlSettings.nucleicValidDays"
                      type="number"
                      class="w-full p-[0.4vw] bg-[#15274D]/50 border border-[#3B8EE6]/30 rounded text-white text-[0.6vw]"
                    />
                  </div>
                </div>
                <div class="mt-[0.8vw]">
                  <label class="text-white/80 text-[0.6vw] block mb-[0.3vw]">防控要求</label>
                  <div class="flex gap-[1vw]">
                    <label class="flex items-center gap-[0.3vw] text-white/80 text-[0.5vw]">
                      <input v-model="controlSettings.requireHealthCode" type="checkbox" class="mr-[0.2vw]" />
                      健康码检查
                    </label>
                    <label class="flex items-center gap-[0.3vw] text-white/80 text-[0.5vw]">
                      <input v-model="controlSettings.requireVaccine" type="checkbox" class="mr-[0.2vw]" />
                      疫苗接种要求
                    </label>
                    <label class="flex items-center gap-[0.3vw] text-white/80 text-[0.5vw]">
                      <input v-model="controlSettings.requireNucleic" type="checkbox" class="mr-[0.2vw]" />
                      核酸检测要求
                    </label>
                  </div>
                </div>
                <div class="flex justify-end mt-[1vw]">
                  <button
                    @click="saveControlSettings"
                    class="px-[1vw] py-[0.4vw] bg-[#4CAF50]/20 text-[#4CAF50] rounded hover:bg-[#4CAF50]/30 transition-all text-[0.6vw]"
                  >
                    保存设置
                  </button>
                </div>
              </div>

              <!-- 应急联系人 -->
              <div class="bg-[#15274D]/30 border border-[#3B8EE6]/30 rounded p-[1vw]">
                <div class="flex justify-between items-center mb-[0.8vw]">
                  <div class="text-white text-[0.7vw] font-bold">应急联系人</div>
                  <button
                    @click="addEmergencyContact"
                    class="px-[0.8vw] py-[0.3vw] bg-[#FF5252]/20 text-[#FF5252] rounded text-[0.5vw] hover:bg-[#FF5252]/30"
                  >
                    添加联系人
                  </button>
                </div>
                <div class="space-y-[0.5vw] max-h-[10vw] overflow-y-auto custom-scrollbar">
                  <div
                    v-for="(contact, index) in emergencyContacts"
                    :key="index"
                    class="flex items-center justify-between p-[0.5vw] bg-[#3B8EE6]/10 rounded hover:bg-[#3B8EE6]/20 transition-all"
                  >
                    <div class="flex items-center gap-[0.5vw]">
                      <div class="text-white text-[0.6vw]">{{ contact.name }}</div>
                      <div class="text-white/60 text-[0.5vw]">{{ contact.role }}</div>
                      <div class="text-[#3B8EE6] text-[0.5vw]">{{ contact.phone }}</div>
                    </div>
                    <button
                      @click="removeEmergencyContact(index)"
                      class="px-[0.5vw] py-[0.2vw] bg-[#FF5252]/20 text-[#FF5252] rounded text-[0.4vw] hover:bg-[#FF5252]/30"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他弹窗内容 -->
        <div v-else class="text-white/80 text-[0.6vw] text-center py-[2vw]"> {{ dialogTitle }}功能界面 </div>
      </div>
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';

  // 健康统计数据
  const healthStats = reactive({
    greenCode: 156,
    yellowCode: 0,
    redCode: 0,
    abnormalTemp: 0,
  });

  // 疫苗和核酸数据
  const vaccineRate = ref(89.25);
  const vaccinatedCount = ref(142);
  const nucleicTestRate = ref(95.67);
  const nucleicTestCount = ref(152);

  // 健康检查记录
  const healthRecords = reactive([
    { name: '张经理', healthCode: 'green', temperature: 36.5, time: '09:30', riskLevel: null },
    { name: '李工程师', healthCode: 'green', temperature: 36.3, time: '10:15', riskLevel: null },
    { name: '王总监', healthCode: 'green', temperature: 36.8, time: '11:20', riskLevel: null },
    { name: '赵工程师', healthCode: 'green', temperature: 36.7, time: '13:45', riskLevel: null },
    { name: '刘主管', healthCode: 'green', temperature: 36.6, time: '14:30', riskLevel: null },
  ]);

  // 弹窗相关
  const dialogVisible = ref(false);
  const dialogTitle = ref('');
  const dialogType = ref('');

  // 健康检查表单
  const healthForm = reactive({
    name: '',
    phone: '',
    temperature: 36.5,
    healthCode: 'green',
    vaccineStatus: 'none',
  });

  // 管理界面标签页
  const managementTabs = ref([
    { key: 'health', label: '健康监控' },
    { key: 'temperature', label: '体温监测' },
    { key: 'vaccine', label: '疫苗管理' },
    { key: 'nucleic', label: '核酸检测' },
    { key: 'risk', label: '风险管理' },
    { key: 'settings', label: '防控设置' },
  ]);

  const activeTab = ref('health');

  // 体温监测数据
  const temperatureStats = reactive({
    normal: 156,
    warning: 0,
    abnormal: 0,
  });

  // 体温检测记录
  const temperatureRecords = reactive([
    { name: '张经理', temperature: 36.5, location: '主入口', time: '09:30', status: '正常' },
    { name: '李工程师', temperature: 36.3, location: '侧门', time: '10:15', status: '正常' },
    { name: '王总监', temperature: 36.8, location: '主入口', time: '11:20', status: '正常' },
    { name: '赵工程师', temperature: 36.7, location: '主入口', time: '13:45', status: '正常' },
    { name: '刘主管', temperature: 36.6, location: '侧门', time: '14:30', status: '正常' },
  ]);

  // 疫苗接种数据
  const vaccineStats = reactive({
    completed: 142,
    partial: 8,
    booster: 89,
    unvaccinated: 6,
  });

  // 疫苗接种记录
  const vaccineRecords = reactive([
    { name: '张经理', vaccine: '科兴疫苗', status: '完全接种', date: '2023-12-15', location: '园区医务室' },
    { name: '李工程师', vaccine: '辉瑞疫苗', status: '加强针', date: '2024-01-10', location: '社区医院' },
    { name: '王总监', vaccine: '国药疫苗', status: '完全接种', date: '2023-11-20', location: '园区医务室' },
    { name: '赵工程师', vaccine: '科兴疫苗', status: '部分接种', date: '2024-01-05', location: '园区医务室' },
    { name: '刘主管', vaccine: '辉瑞疫苗', status: '完全接种', date: '2023-12-28', location: '社区医院' },
  ]);

  // 核酸检测数据
  const nucleicStats = reactive({
    negative: 156,
    pending: 0,
    positive: 0,
  });

  // 核酸检测记录
  const nucleicRecords = reactive([
    { name: '张经理', result: '阴性', testType: '抗原检测', testDate: '2024-01-15', location: '园区检测点' },
    { name: '李工程师', result: '阴性', testType: 'PCR检测', testDate: '2024-01-14', location: '医院' },
    { name: '王总监', result: '阴性', testType: 'PCR检测', testDate: '2024-01-16', location: '园区检测点' },
    { name: '赵工程师', result: '阴性', testType: '抗原检测', testDate: '2024-01-15', location: '园区检测点' },
    { name: '刘主管', result: '阴性', testType: 'PCR检测', testDate: '2024-01-13', location: '社区医院' },
  ]);

  // 风险管理数据
  const riskStats = reactive({
    high: 0,
    medium: 0,
    low: 0,
  });

  // 风险人员列表（清空，保持正常状态）
  const riskPersons = reactive([]);

  // 防控设置
  const controlSettings = reactive({
    temperatureThreshold: 37.3,
    nucleicValidDays: 7,
    requireHealthCode: true,
    requireVaccine: true,
    requireNucleic: true,
  });

  // 应急联系人
  const emergencyContacts = reactive([
    { name: '张医生', role: '园区医务', phone: '138-0000-1001' },
    { name: '李主任', role: '防控负责人', phone: '138-0000-1002' },
    { name: '王队长', role: '安保队长', phone: '138-0000-1003' },
    { name: '赵主管', role: '行政主管', phone: '138-0000-1004' },
  ]);

  // 显示完整功能界面
  const showFullInterface = () => {
    dialogTitle.value = '疫情防控系统 - 完整管理界面';
    dialogType.value = 'full';
    dialogVisible.value = true;
  };

  // 方法
  const showHealthCheck = () => {
    dialogTitle.value = '健康登记';
    dialogType.value = 'health';
    dialogVisible.value = true;
  };

  const showTemperatureCheck = () => {
    dialogTitle.value = '体温检测';
    dialogType.value = 'temperature';
    dialogVisible.value = true;
  };

  const showVaccineRecord = () => {
    dialogTitle.value = '疫苗记录';
    dialogType.value = 'vaccine';
    dialogVisible.value = true;
  };

  const showRiskManagement = () => {
    dialogTitle.value = '风险管理';
    dialogType.value = 'risk';
    dialogVisible.value = true;
  };

  const closeDialog = () => {
    dialogVisible.value = false;
    // 重置表单
    Object.assign(healthForm, {
      name: '',
      phone: '',
      temperature: 36.5,
      healthCode: 'green',
      vaccineStatus: 'none',
    });
  };

  // 监听弹窗关闭事件，重置表单
  const handleDialogClose = () => {
    // 重置表单
    Object.assign(healthForm, {
      name: '',
      phone: '',
      temperature: 36.5,
      healthCode: 'green',
      vaccineStatus: 'none',
    });
  };

  const submitHealthCheck = () => {
    if (!healthForm.name || !healthForm.phone) {
      alert('请填写完整信息');
      return;
    }

    // 添加到记录列表
    const newRecord = {
      name: healthForm.name,
      healthCode: healthForm.healthCode,
      temperature: healthForm.temperature,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      riskLevel: healthForm.temperature > 37.3 || healthForm.healthCode !== 'green' ? (healthForm.temperature > 37.3 ? '高风险' : '中风险') : null,
    };

    healthRecords.unshift(newRecord);

    // 更新统计数据
    updateHealthStats();

    closeDialog();
  };

  const updateHealthStats = () => {
    const greenCount = healthRecords.filter((r) => r.healthCode === 'green').length;
    const yellowCount = healthRecords.filter((r) => r.healthCode === 'yellow').length;
    const redCount = healthRecords.filter((r) => r.healthCode === 'red').length;
    const abnormalTempCount = healthRecords.filter((r) => r.temperature > 37.3).length;

    healthStats.greenCode = greenCount + 150; // 基础数据
    healthStats.yellowCode = yellowCount;
    healthStats.redCode = redCount;
    healthStats.abnormalTemp = abnormalTempCount;
  };

  const getHealthCodeClass = (healthCode) => {
    switch (healthCode) {
      case 'green':
        return 'text-[#4CAF50]';
      case 'yellow':
        return 'text-[#FF9800]';
      case 'red':
        return 'text-[#FF5252]';
      default:
        return 'text-white/60';
    }
  };

  const getHealthCodeText = (healthCode) => {
    switch (healthCode) {
      case 'green':
        return '绿码';
      case 'yellow':
        return '黄码';
      case 'red':
        return '红码';
      default:
        return '-';
    }
  };

  const getRiskLevelClass = (riskLevel) => {
    switch (riskLevel) {
      case '高风险':
        return 'text-[#FF5252]';
      case '中风险':
        return 'text-[#FF9800]';
      case '低风险':
        return 'text-[#FFC107]';
      default:
        return 'text-white/60';
    }
  };

  // 新增的样式方法
  const getTemperatureClass = (temperature) => {
    if (temperature >= 37.3) return 'text-[#FF5252]';
    if (temperature >= 37.0) return 'text-[#FF9800]';
    return 'text-[#4CAF50]';
  };

  const getTemperatureStatusClass = (status) => {
    switch (status) {
      case '正常':
        return 'text-[#4CAF50]';
      case '预警':
        return 'text-[#FF9800]';
      case '异常':
        return 'text-[#FF5252]';
      default:
        return 'text-white/60';
    }
  };

  const getVaccineStatusClass = (status) => {
    switch (status) {
      case '完全接种':
        return 'text-[#4CAF50]';
      case '加强针':
        return 'text-[#3B8EE6]';
      case '部分接种':
        return 'text-[#FF9800]';
      case '未接种':
        return 'text-[#FF5252]';
      default:
        return 'text-white/60';
    }
  };

  const getNucleicResultClass = (result) => {
    switch (result) {
      case '阴性':
        return 'text-[#4CAF50]';
      case '待出结果':
        return 'text-[#FF9800]';
      case '阳性':
        return 'text-[#FF5252]';
      default:
        return 'text-white/60';
    }
  };

  // 新增功能方法
  const addNucleicTest = () => {
    const name = prompt('请输入姓名:');
    if (name) {
      const newRecord = {
        name: name,
        result: '待出结果',
        testType: 'PCR检测',
        testDate: new Date().toLocaleDateString('zh-CN'),
        location: '园区检测点',
      };
      nucleicRecords.unshift(newRecord);
      nucleicStats.pending++;
      alert(`已为 ${name} 登记核酸检测`);
    }
  };

  const handleRiskPerson = (person) => {
    if (confirm(`确定处理风险人员 ${person.name} 吗？`)) {
      const index = riskPersons.findIndex((p) => p.name === person.name);
      if (index > -1) {
        riskPersons.splice(index, 1);
        // 更新风险统计
        if (person.riskLevel === '高风险') riskStats.high--;
        else if (person.riskLevel === '中风险') riskStats.medium--;
        else if (person.riskLevel === '低风险') riskStats.low--;
        alert(`已处理风险人员 ${person.name}`);
      }
    }
  };

  const saveControlSettings = () => {
    alert('防控设置已保存');
  };

  const addEmergencyContact = () => {
    const name = prompt('请输入联系人姓名:');
    if (name) {
      const role = prompt('请输入职务:') || '工作人员';
      const phone = prompt('请输入联系电话:') || '138-0000-0000';
      emergencyContacts.push({ name, role, phone });
      alert(`已添加应急联系人 ${name}`);
    }
  };

  const removeEmergencyContact = (index) => {
    if (confirm('确定删除此联系人吗？')) {
      const contact = emergencyContacts[index];
      emergencyContacts.splice(index, 1);
      alert(`已删除联系人 ${contact.name}`);
    }
  };

  // 实时更新数据
  const updateData = () => {
    // 模拟数据变化，保持正常状态
    const randomChange = () => Math.floor(Math.random() * 3) - 1; // -1, 0, 1

    healthStats.greenCode = Math.max(150, healthStats.greenCode + randomChange());

    // 保留2位小数
    const newVaccineRate = Math.min(100, Math.max(85, vaccineRate.value + Math.random() * 1 - 0.5));
    vaccineRate.value = Math.round(newVaccineRate * 100) / 100;

    const newNucleicRate = Math.min(100, Math.max(95, nucleicTestRate.value + Math.random() * 1 - 0.5));
    nucleicTestRate.value = Math.round(newNucleicRate * 100) / 100;

    // 保持异常数据为0
    healthStats.yellowCode = 0;
    healthStats.redCode = 0;
    healthStats.abnormalTemp = 0;
  };

  // 定时更新
  let updateInterval = null;
  onMounted(() => {
    updateInterval = setInterval(updateData, 60000); // 每分钟更新一次
  });

  onBeforeUnmount(() => {
    if (updateInterval) {
      clearInterval(updateInterval);
    }
  });

  defineExpose({
    updateData,
  });
</script>

<style scoped>
  .custom-scrollbar::-webkit-scrollbar {
    width: 0.2vw;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(59, 142, 230, 0.1);
    border-radius: 0.1vw;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(59, 142, 230, 0.5);
    border-radius: 0.1vw;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 142, 230, 0.7);
  }
</style>
