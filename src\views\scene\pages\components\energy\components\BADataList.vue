<template>
  <div class="ba-list">
    <div class="grid grid-cols-[45%_25%_30%] text-white/60 text-[0.6vw] mb-[0.5vw]">
      <div class="pl-[0.3vw]">名称</div>
      <div>数值</div>
      <div>更新时间</div>
    </div>
    <div ref="scrollContainer" class="flex-1 h-[calc(100%-1.5vw)] overflow-hidden relative">
      <div ref="listWrapper" class="will-change-transform" :style="{ transform: `translateY(${scrollOffset}px)` }">
        <div
          v-for="item in displayList"
          :key="item.id"
          class="grid grid-cols-[45%_25%_30%] text-white/90 text-[0.7vw] min-h-[1.8vw] py-[0.2vw] border-b border-white/10"
        >
          <div class="truncate pl-[0.3vw]" :title="item.remark">{{ item.remark }}</div>
          <div class="truncate" :title="`${item.valueData}${item.unit}`">{{ item.valueData }}{{ item.unit }}</div>
          <div class="flex flex-col leading-[1.2]">
            <span>{{ formatDate(item.updateTime) }}</span>
            <span>{{ formatTime(item.updateTime) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import dayjs from 'dayjs';
  import { getBADataList, type BADataItem } from '/@/api/scene';

  const baList = ref<BADataItem[]>([]);
  const scrollOffset = ref(0);
  const scrollContainer = ref<HTMLElement>();
  const listWrapper = ref<HTMLElement>();

  // 用于展示的列表数据，包含复制的部分用于无缝滚动
  const displayList = computed(() => {
    if (!baList.value.length) return [];
    return [...baList.value, ...baList.value];
  });

  // 滚动相关变量
  let animationFrameId: number | null = null;
  let lastTimestamp = 0;
  let scrollSpeed = 0.15; // 每帧滚动的像素数，降低速度（原来是0.5）
  let itemHeight = 0; // 缓存行高
  let maxScroll = 0; // 缓存最大滚动距离

  // 计算初始滚动参数
  const calculateScrollParams = () => {
    if (!listWrapper.value || baList.value.length === 0) return;

    const listItems = listWrapper.value.querySelectorAll('.grid');
    if (listItems.length === 0) return;

    // 获取并缓存第一个元素高度
    const firstItem = listItems[0] as HTMLElement;
    itemHeight = firstItem.offsetHeight;
    maxScroll = -(baList.value.length * itemHeight);
  };

  // 使用requestAnimationFrame实现平滑滚动
  const scrollStep = (timestamp: number) => {
    if (!scrollContainer.value || !listWrapper.value || baList.value.length === 0) {
      animationFrameId = requestAnimationFrame(scrollStep);
      return;
    }

    // 首次运行或数据更新时重新计算参数
    if (itemHeight === 0) {
      calculateScrollParams();
    }

    // 计算帧间隔，确保滚动速度一致
    if (!lastTimestamp) lastTimestamp = timestamp;
    const elapsed = timestamp - lastTimestamp;

    // 根据帧率调整滚动速度，保持视觉上的一致性
    const delta = (scrollSpeed * elapsed) / 16.67; // 基于60fps标准化

    scrollOffset.value -= delta;

    // 滚动到底部时重置
    if (scrollOffset.value <= maxScroll) {
      scrollOffset.value = 0;
    }

    lastTimestamp = timestamp;
    animationFrameId = requestAnimationFrame(scrollStep);
  };

  const startScroll = () => {
    if (animationFrameId) return;
    lastTimestamp = 0;
    animationFrameId = requestAnimationFrame(scrollStep);
  };

  const stopScroll = () => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  };

  const getBAData = async () => {
    try {
      baList.value = await getBADataList({});
      if (baList.value.length > 0) {
        // 数据更新后重置滚动参数
        itemHeight = 0;
        maxScroll = 0;
        startScroll();
      }
    } catch (error) {
      console.error('获取BA数据失败:', error);
    }
  };

  // 格式化日期
  const formatDate = (time: string | number | Date | dayjs.Dayjs) => {
    if (!time) return '--';
    return dayjs(time).format('YYYY-MM-DD');
  };

  // 格式化时间
  const formatTime = (time: string | number | Date | dayjs.Dayjs) => {
    if (!time) return '--';
    return dayjs(time).format('HH:mm:ss');
  };

  onMounted(() => {
    getBAData();
  });

  onUnmounted(() => {
    stopScroll();
  });
</script>

<style scoped>
  .ba-list {
    height: 100%;
  }

  .ba-list ::-webkit-scrollbar {
    display: none;
  }

  .truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
