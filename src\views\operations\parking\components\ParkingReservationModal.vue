<template>
  <a-modal
    v-model:visible="visible"
    title="车位预约"
    width="500px"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div v-if="space" class="mb-4 p-3 bg-gray-50 rounded">
      <h4>车位信息</h4>
      <p>车位号: {{ space.spaceNumber }}</p>
      <p>位置: {{ space.floor }} {{ space.area }}</p>
      <p>类型: {{ space.type }}</p>
    </div>

    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="车牌号" name="vehicleNumber">
        <a-input v-model:value="formData.vehicleNumber" placeholder="请输入车牌号" />
      </a-form-item>

      <a-form-item label="车主姓名" name="ownerName">
        <a-input v-model:value="formData.ownerName" placeholder="请输入车主姓名" />
      </a-form-item>

      <a-form-item label="联系电话" name="ownerPhone">
        <a-input v-model:value="formData.ownerPhone" placeholder="请输入联系电话" />
      </a-form-item>

      <a-form-item label="预约时间" name="reserveTime">
        <a-date-picker
          v-model:value="formData.reserveTime"
          show-time
          format="YYYY-MM-DD HH:mm"
          placeholder="选择预约时间"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="预计停车时长" name="expectedDuration">
        <a-input-number
          v-model:value="formData.expectedDuration"
          :min="1"
          :max="24"
          placeholder="小时"
          style="width: 100%"
        >
          <template #addonAfter>小时</template>
        </a-input-number>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { reserveParkingSpace, type ParkingSpace } from '/@/api/operations/parking';

  interface Props {
    visible: boolean;
    space: ParkingSpace | null;
  }

  const props = defineProps<Props>();

  const emit = defineEmits<{
    'update:visible': [value: boolean];
    success: [];
  }>();

  const formRef = ref();
  const formData = reactive({
    vehicleNumber: '',
    ownerName: '',
    ownerPhone: '',
    reserveTime: null as Dayjs | null,
    expectedDuration: 2,
  });

  const rules = {
    vehicleNumber: [
      { required: true, message: '请输入车牌号', trigger: 'blur' },
    ],
    ownerName: [
      { required: true, message: '请输入车主姓名', trigger: 'blur' },
    ],
    ownerPhone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
    ],
    reserveTime: [
      { required: true, message: '请选择预约时间', trigger: 'change' },
    ],
    expectedDuration: [
      { required: true, message: '请输入预计停车时长', trigger: 'blur' },
    ],
  };

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();
      if (!props.space) return;

      await reserveParkingSpace({
        spaceNumber: props.space.spaceNumber,
        vehicleNumber: formData.vehicleNumber,
        ownerName: formData.ownerName,
        ownerPhone: formData.ownerPhone,
        reserveTime: formData.reserveTime!.format('YYYY-MM-DD HH:mm:ss'),
        expectedDuration: formData.expectedDuration,
      });

      message.success('车位预约成功');
      emit('success');
    } catch (error) {
      message.error('预约失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };
</script>
