import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetRealtime = '/electricityMeter/getReal',
  GetHistory = '/electricityMeter/getHistory',
  GetTop = '/electricityMeter/top', // 新增的TOP10接口
}

/**
 * @description: 获取电表实时数据
 */
export function getElectricityRealtime() {
  return defHttp.get<any[]>({ url: Api.GetRealtime });
}

/**
 * @description: 获取电表历史数据
 */
export function getElectricityHistory() {
  return defHttp.get<Record<string, any[]>>({ url: Api.GetHistory });
}

/**
 * @description: 获取电表能耗排行Top10
 */
export function getElectricityTop() {
  return defHttp.get<any[]>({ url: Api.GetTop });
}
