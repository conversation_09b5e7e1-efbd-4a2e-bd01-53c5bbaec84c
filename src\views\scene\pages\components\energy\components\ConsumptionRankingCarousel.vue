<template>
  <div class="consumption-carousel w-full h-full">
    <!-- 轮播图 -->
    <div class="carousel-container h-full">
      <a-carousel ref="carouselRef" class="h-full" :autoplay="true" :dots="false" :autoplay-speed="5000" effect="fade" @change="handleChange">
        <div class="carousel-item h-full">
          <ElectricityRealtimeRankingPanel />
        </div>
        <div class="carousel-item h-full">
          <WaterRealtimeRankingPanel />
        </div>
      </a-carousel>
    </div>

    <!-- 标题和指示器 -->
    <div class="absolute top-0 left-0 w-full flex justify-between items-center px-[0.6vw] py-[0.3vw]">
      <div class="text-white text-[0.7vw]">{{ titles[currentIndex] }}</div>
      <div class="flex items-center">
        <!-- 手动切换按钮 -->
        <div class="flex space-x-[0.4vw]">
          <div
            class="w-[0.8vw] h-[0.8vw] rounded-full cursor-pointer transition-all duration-300"
            :class="currentIndex === 0 ? 'bg-[#3B8EE6]' : 'bg-gray-500 opacity-50 hover:opacity-80'"
            @click="changeSlide(0)"
          ></div>
          <div
            class="w-[0.8vw] h-[0.8vw] rounded-full cursor-pointer transition-all duration-300"
            :class="currentIndex === 1 ? 'bg-[#06B6D4]' : 'bg-gray-500 opacity-50 hover:opacity-80'"
            @click="changeSlide(1)"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import ElectricityRealtimeRankingPanel from './ElectricityRealtimeRankingPanel.vue';
  import WaterRealtimeRankingPanel from './WaterRealtimeRankingPanel.vue';

  // 轮播图引用
  const carouselRef = ref(null);

  // 当前显示的索引
  const currentIndex = ref(0);

  // 修改标题数组
  const titles = ['电耗实时排行榜', '水耗实时排行榜'];

  // 处理轮播图切换事件
  const handleChange = (current: number) => {
    currentIndex.value = current;
  };

  // 手动切换轮播图
  const changeSlide = (index: number) => {
    if (carouselRef.value) {
      carouselRef.value.goTo(index);
      currentIndex.value = index;
    }
  };

  onMounted(() => {
    // 初始化时确保显示第一个图表
    currentIndex.value = 0;
  });
</script>

<style scoped>
  .consumption-carousel {
    position: relative;
  }

  .carousel-container {
    width: 100%;
    height: 100%;
  }

  /* 确保轮播项目占满容器 */
  :deep(.ant-carousel),
  :deep(.ant-carousel .slick-slider),
  :deep(.ant-carousel .slick-list),
  :deep(.ant-carousel .slick-track),
  :deep(.ant-carousel .slick-slide > div) {
    height: 100%;
  }

  /* 淡入淡出效果 */
  :deep(.ant-carousel .slick-slide) {
    transition: opacity 0.5s ease;
  }

  :deep(.ant-carousel .slick-slide:not(.slick-active)) {
    opacity: 0;
  }

  :deep(.ant-carousel .slick-slide.slick-active) {
    opacity: 1;
  }

  .carousel-item {
    display: flex !important;
    align-items: center;
    justify-content: center;
  }
</style>
