import * as THREE from 'three';
import { CameraController } from '../CameraController';
import { ControlManager } from '../control/ControlManager';
import { SceneManager } from '../SceneManager';
import { ModelLoaderManager } from '../load/ModelLoaderManager';
import { useGlobalThreeStore } from '../../store/globalThreeStore';
import { gsap } from 'gsap';
import { throttle } from 'lodash-es';

// 巡检路径点接口
export interface PatrolWaypoint {
  position: THREE.Vector3;
  target?: THREE.Vector3;
  stayTime?: number; // 停留时间（毫秒）
  name?: string; // 路径点名称（用于显示）
}

// 巡检状态枚举
export enum PatrolState {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
}

// 巡检配置接口
export interface PatrolConfig {
  speed: number; // 移动速度
  turnSpeed: number; // 转向速度
  waypointReachedDistance: number; // 到达路径点的距离阈值
  cameraHeight: number; // 相机高度（米）
  stayTimeDefault: number; // 默认停留时间（毫秒）
}

// 巡检事件接口
export interface PatrolEvents {
  onStart?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  onStop?: () => void;
  onWaypointReached?: (waypoint: PatrolWaypoint, index: number) => void;
  onPathComplete?: () => void;
}

/**
 * 自动巡检控制器
 * 实现沿预定义路径点的自动巡检，模拟人在室内行走的体验
 */
export class PatrolController {
  private static instance: PatrolController | null = null;
  
  // 基础组件
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private controlManager: ControlManager;
  private cameraController: CameraController;
  
  // 巡检状态
  private state: PatrolState = PatrolState.IDLE;
  private waypoints: PatrolWaypoint[] = [];
  private currentWaypointIndex: number = 0;
  private pathCompleted: boolean = false;
  
  // 移动状态
  private currentPosition: THREE.Vector3 = new THREE.Vector3();
  private targetPosition: THREE.Vector3 = new THREE.Vector3();
  private currentLookAt: THREE.Vector3 = new THREE.Vector3();
  private targetLookAt: THREE.Vector3 = new THREE.Vector3();
  private movementDirection: THREE.Vector3 = new THREE.Vector3();
  
  // 移动状态
  private movementVector: THREE.Vector3 = new THREE.Vector3();
  
  // 动画控制
  private animationId: number | null = null;
  private waypointTimeout: number | null = null;
  private lastUpdateTime: number = 0;
  
  // 配置
  private config: PatrolConfig = {
    speed: 3.0, // 每秒移动距离，增加速度以便更快地浏览
    turnSpeed: 1.5, // 转向速度，降低以使转向更平滑
    waypointReachedDistance: 1.0, // 到达路径点的距离阈值，增加以避免精确定位问题
    cameraHeight: 1.7, // 相机高度（米）
    stayTimeDefault: 2000, // 默认在每个路径点停留2秒
  };
  
  // 事件回调
  private events: PatrolEvents = {};
  
  // 辅助对象（用于调试）
  private debugHelpers: THREE.Object3D[] = [];
  private debugEnabled: boolean = false;
  
  /**
   * 获取单例实例
   */
  public static getInstance(): PatrolController {
    if (!PatrolController.instance) {
      PatrolController.instance = new PatrolController();
    }
    return PatrolController.instance;
  }
  
  /**
   * 构造函数
   */
  private constructor() {
    // 获取必要组件
    this.scene = SceneManager.getInstance().scene;
    this.camera = CameraController.getInstance().camera as THREE.PerspectiveCamera;
    this.controlManager = ControlManager.getInstance();
    this.cameraController = CameraController.getInstance();
    
    // 绑定更新方法到场景管理器
    this._bindUpdateToScene();
  }
  
  /**
   * 绑定更新方法到场景管理器
   */
  private _bindUpdateToScene(): void {
    const updateCallback = (deltaTime: number) => {
      if (this.state === PatrolState.RUNNING) {
        this._update(deltaTime);
      }
    };
    
    SceneManager.getInstance().addUpdateCallback(updateCallback);
  }
  
  /**
   * 更新方法，在每帧调用
   */
  private _update(deltaTime: number): void {
    if (this.state !== PatrolState.RUNNING || this.pathCompleted) return;
    
    // 获取当前路径点
    const currentWaypoint = this.waypoints[this.currentWaypointIndex];
    if (!currentWaypoint) return;
    
    // 计算到目标的距离
    const distanceToTarget = this.currentPosition.distanceTo(this.targetPosition);
    
    // 如果到达当前路径点
    if (distanceToTarget < this.config.waypointReachedDistance) {
      this._waypointReached();
      return;
    }
    
    // 更新移动方向
    this.movementDirection.subVectors(this.targetPosition, this.currentPosition).normalize();
    
    // 计算移动距离
    const moveDistance = this.config.speed * deltaTime;
    
    // 更新位置
    this.currentPosition.addScaledVector(this.movementDirection, moveDistance);
    
    // 更新相机位置
    this.camera.position.copy(this.currentPosition);
    this.camera.position.y = this.config.cameraHeight; // 保持固定高度
    
    // 平滑更新相机朝向
    this.currentLookAt.lerp(this.targetLookAt, this.config.turnSpeed * deltaTime);
    this.camera.lookAt(this.currentLookAt);
    
    // 请求渲染更新
    SceneManager.getInstance().needsRender = true;
  }
  
  /**
   * 当到达路径点时调用
   */
  private _waypointReached(): void {
    const currentWaypoint = this.waypoints[this.currentWaypointIndex];
    
    // 触发路径点到达事件
    if (this.events.onWaypointReached) {
      this.events.onWaypointReached(currentWaypoint, this.currentWaypointIndex);
    }
    
    // 在路径点停留指定时间
    const stayTime = currentWaypoint.stayTime || this.config.stayTimeDefault;
    
    // 清除之前的超时
    if (this.waypointTimeout !== null) {
      window.clearTimeout(this.waypointTimeout);
    }
    
    // 设置新的超时
    this.waypointTimeout = window.setTimeout(() => {
      // 移动到下一个路径点
      this.currentWaypointIndex++;
      
      // 如果所有路径点都已访问
      if (this.currentWaypointIndex >= this.waypoints.length) {
        this._pathCompleted();
        return;
      }
      
      // 设置新的目标位置
      this._setNextWaypointAsTarget();
    }, stayTime);
  }
  
  /**
   * 设置下一个路径点为目标
   */
  private _setNextWaypointAsTarget(): void {
    const nextWaypoint = this.waypoints[this.currentWaypointIndex];
    if (!nextWaypoint) return;
    
    // 设置目标位置
    this.targetPosition.copy(nextWaypoint.position);
    
    // 设置目标朝向
    if (nextWaypoint.target) {
      this.targetLookAt.copy(nextWaypoint.target);
    } else {
      // 如果没有指定目标朝向，使用下一个路径点的位置
      this.targetLookAt.copy(nextWaypoint.position);
    }
  }
  
  /**
   * 当完成整个路径时调用
   */
  private _pathCompleted(): void {
    this.pathCompleted = true;
    this.state = PatrolState.IDLE;
    
    // 触发路径完成事件
    if (this.events.onPathComplete) {
      this.events.onPathComplete();
    }
  }
  
  /**
   * 查找预定义的路径点
   * 查找模型中的PathPoint_1, PathPoint_2等路径点
   */
  public findPathPoints(): PatrolWaypoint[] {
    const waypoints: PatrolWaypoint[] = [];
    
    // 获取当前楼层的模型
    const modelLoader = ModelLoaderManager.getInstance();
    const models = modelLoader.getCurrentModels();
    
    // 查找预定义的路径点
    const pathPointMap = new Map<number, THREE.Object3D>();
    
    // 遍历所有模型，查找PathPoint_开头的对象
    models.forEach((model) => {
      model.traverse((object) => {
        if (object.name && object.name.startsWith('PathPoint_')) {
          // 提取路径点编号
          const match = object.name.match(/PathPoint_(\d+)/);
          if (match) {
            const pointNumber = parseInt(match[1]);
            pathPointMap.set(pointNumber, object);
            console.log(`[PatrolController] 找到路径点: ${object.name}`);
          }
        }
      });
    });
    
    // 如果找到了路径点，按编号排序并创建路径
    if (pathPointMap.size > 0) {
      // 将Map转换为数组并按编号排序
      const sortedPathPoints = Array.from(pathPointMap.entries())
        .sort((a, b) => a[0] - b[0])
        .map((entry) => entry[1]);
      
      console.log(`[PatrolController] 找到 ${sortedPathPoints.length} 个预定义路径点`);
      
      // 为每个路径点创建路径点
      sortedPathPoints.forEach((pathPoint, index) => {
        const position = new THREE.Vector3();
        pathPoint.getWorldPosition(position);
        
        // 确保在地面高度
        position.y = this.config.cameraHeight;
        
        // 计算目标点（如果不是最后一个点，则看向下一个点；否则看向第一个点）
        const nextIndex = (index + 1) % sortedPathPoints.length;
        const targetPosition = new THREE.Vector3();
        sortedPathPoints[nextIndex].getWorldPosition(targetPosition);
        
        // 添加路径点
        waypoints.push({
          position: position,
          target: targetPosition,
          name: pathPoint.name,
          stayTime: 2000 // 在路径点停留2秒
        });
      });
    }
    
    return waypoints;
  }
  
  /**
   * 开始巡检
   * @param events 事件回调
   */
  public start(events?: PatrolEvents): void {
    // 如果已经在运行，先停止
    if (this.state === PatrolState.RUNNING) {
      this.stop();
    }
    
    // 设置事件回调
    if (events) {
      this.events = events;
    }
    
    // 查找预定义的路径点
    this.waypoints = this.findPathPoints();
    
    if (this.waypoints.length === 0) {
      console.warn('[PatrolController] 无法开始巡检：没有找到预定义的路径点');
      return;
    }
    
    // 初始化状态
    this.currentWaypointIndex = 0;
    this.pathCompleted = false;
    
    // 获取当前相机位置作为起点
    this.currentPosition.copy(this.camera.position);
    this.currentPosition.y = 0; // 确保在地面高度
    
    // 设置第一个路径点为目标
    this._setNextWaypointAsTarget();
    
    // 设置初始朝向
    this.currentLookAt.copy(this.targetLookAt);
    
    // 更新状态
    this.state = PatrolState.RUNNING;
    
    // 触发开始事件
    if (this.events.onStart) {
      this.events.onStart();
    }
    
    // 禁用控制器
    this.controlManager.lockControl();
    
    console.log('[PatrolController] 开始巡检，路径点数量:', this.waypoints.length);
  }
  
  /**
   * 暂停巡检
   */
  public pause(): void {
    if (this.state !== PatrolState.RUNNING) return;
    
    this.state = PatrolState.PAUSED;
    
    // 清除路径点超时
    if (this.waypointTimeout !== null) {
      window.clearTimeout(this.waypointTimeout);
      this.waypointTimeout = null;
    }
    
    // 触发暂停事件
    if (this.events.onPause) {
      this.events.onPause();
    }
    
    console.log('[PatrolController] 暂停巡检');
  }
  
  /**
   * 恢复巡检
   */
  public resume(): void {
    if (this.state !== PatrolState.PAUSED) return;
    
    this.state = PatrolState.RUNNING;
    
    // 触发恢复事件
    if (this.events.onResume) {
      this.events.onResume();
    }
    
    console.log('[PatrolController] 恢复巡检');
  }
  
  /**
   * 停止巡检
   */
  public stop(): void {
    if (this.state === PatrolState.IDLE) return;
    
    // 清除路径点超时
    if (this.waypointTimeout !== null) {
      window.clearTimeout(this.waypointTimeout);
      this.waypointTimeout = null;
    }
    
    // 更新状态
    this.state = PatrolState.IDLE;
    this.pathCompleted = true;
    
    // 触发停止事件
    if (this.events.onStop) {
      this.events.onStop();
    }
    
    // 启用控制器
    this.controlManager.unlockControl();
    
    console.log('[PatrolController] 停止巡检');
  }
  
  /**
   * 获取当前状态
   */
  public getState(): PatrolState {
    return this.state;
  }
  
  /**
   * 获取当前路径点索引
   */
  public getCurrentWaypointIndex(): number {
    return this.currentWaypointIndex;
  }
  
  /**
   * 获取路径点总数
   */
  public getWaypointCount(): number {
    return this.waypoints.length;
  }
  
  /**
   * 获取当前路径点
   */
  public getCurrentWaypoint(): PatrolWaypoint | null {
    return this.waypoints[this.currentWaypointIndex] || null;
  }
  
  /**
   * 设置配置
   * @param config 配置对象
   */
  public setConfig(config: Partial<PatrolConfig>): void {
    this.config = { ...this.config, ...config };
  }
  
  /**
   * 获取配置
   */
  public getConfig(): PatrolConfig {
    return { ...this.config };
  }
}
