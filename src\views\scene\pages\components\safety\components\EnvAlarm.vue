<template>
  <div class="h-[calc(100%-1.6vw)] flex flex-col">
    <!-- 表头 -->
    <div class="grid grid-cols-[3fr_0.8fr] text-white/60 text-[0.6vw] h-[1.5vw] px-[0.3vw]">
      <div class="flex items-center justify-center">名称</div>
      <div class="flex items-center justify-center">状态</div>
    </div>

    <!-- 列表容器 -->
    <div ref="scrollContainer" class="flex-1 h-[calc(100%-1.5vw)] overflow-hidden relative">
      <div ref="listWrapper" class="will-change-transform" :style="{ transform: `translateY(${scrollOffset}px)` }">
        <div
          v-for="item in displayList"
          :key="item.id"
          class="grid grid-cols-[3fr_0.8fr] text-white/90 text-[0.7vw] min-h-[1.8vw] py-[0.2vw] border-b border-white/10"
          :title="`${item.description} (${item.name})`"
        >
          <div class="flex items-center justify-center truncate pr-[0.3vw]">{{ item.remark }}</div>
          <div class="flex items-center justify-center" :class="item.valueData === '1' ? 'text-red-500' : 'text-green-500'">
            {{ item.valueData === '1' ? '告警' : '正常' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import { getBAAlarmList } from '/@/api/scene';
  import { debounce } from 'lodash-es';

  interface AlarmItem {
    id: number;
    name: string;
    type: number;
    addr: number;
    description: string;
    remark: string;
    valueData: string;
    unit: string;
    updateTime: string;
  }

  const alarmList = ref<AlarmItem[]>([]);
  let refreshTimer: ReturnType<typeof setInterval> | null = null;
  const refreshInterval = 30000; // 30秒刷新一次

  const scrollOffset = ref(0);
  const scrollContainer = ref<HTMLElement>();
  const listWrapper = ref<HTMLElement>();

  // 滚动相关变量
  let animationFrameId: number | null = null;
  let lastTimestamp = 0;
  let scrollSpeed = 0.15; // 每帧滚动的像素数，降低速度
  let itemHeight = 0; // 缓存行高
  let maxScroll = 0; // 缓存最大滚动距离

  // 用于展示的列表数据，包含复制的部分用于无缝滚动
  const displayList = computed(() => {
    if (!alarmList.value.length) return [];
    return [...alarmList.value, ...alarmList.value];
  });

  // 计算初始滚动参数
  const calculateScrollParams = () => {
    if (!listWrapper.value || alarmList.value.length === 0) return;
    
    const listItems = listWrapper.value.querySelectorAll('.grid');
    if (listItems.length === 0) return;
    
    // 获取并缓存第一个元素高度
    const firstItem = listItems[0] as HTMLElement;
    itemHeight = firstItem.offsetHeight;
    maxScroll = -(alarmList.value.length * itemHeight);
  };

  // 使用requestAnimationFrame实现平滑滚动
  const scrollStep = (timestamp: number) => {
    if (!scrollContainer.value || !listWrapper.value || alarmList.value.length === 0) {
      animationFrameId = requestAnimationFrame(scrollStep);
      return;
    }

    // 首次运行或数据更新时重新计算参数
    if (itemHeight === 0) {
      calculateScrollParams();
    }

    // 计算帧间隔，确保滚动速度一致
    if (!lastTimestamp) lastTimestamp = timestamp;
    const elapsed = timestamp - lastTimestamp;
    
    // 根据帧率调整滚动速度，保持视觉上的一致性
    const delta = (scrollSpeed * elapsed) / 16.67; // 基于60fps标准化
    
    scrollOffset.value -= delta;

    // 滚动到底部时重置
    if (scrollOffset.value <= maxScroll) {
      scrollOffset.value = 0;
    }

    lastTimestamp = timestamp;
    animationFrameId = requestAnimationFrame(scrollStep);
  };

  const startScroll = () => {
    if (animationFrameId) return;
    lastTimestamp = 0;
    animationFrameId = requestAnimationFrame(scrollStep);
  };

  const stopScroll = () => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  };

  // 使用 debounce 防止频繁调用
  const getAlarmData = debounce(async () => {
    try {
      // 获取动环告警数据
      const res = await getBAAlarmList({});

      // 直接使用返回的数组数据
      if (Array.isArray(res)) {
        alarmList.value = res.map((item) => ({
          id: item.id,
          name: item.name,
          type: item.type,
          addr: item.addr,
          description: item.description,
          remark: item.remark,
          valueData: item.valueData ?? '0',
          unit: item.unit,
          updateTime: item.updateTime,
        }));
        if (alarmList.value.length > 0) {
          // 数据更新后重置滚动参数
          itemHeight = 0;
          maxScroll = 0;
          startScroll();
        }
        console.log('动环告警数据已更新，共', alarmList.value.length, '条');
      } else {
        console.warn('动环告警数据返回格式异常:', res);
      }
    } catch (error) {
      console.error('获取动环告警数据失败:', error);
    }
  }, 500);

  // 开始定时刷新
  const startRefreshTimer = () => {
    if (!refreshTimer) {
      // 立即执行一次
      getAlarmData();
      // 设置定时器
      refreshTimer = setInterval(() => {
        getAlarmData();
      }, refreshInterval);
    }
  };

  // 停止定时刷新
  const stopRefreshTimer = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  };

  onMounted(() => {
    getAlarmData();
    startRefreshTimer();
  });

  onUnmounted(() => {
    stopRefreshTimer();
    stopScroll();
  });
</script>

<style scoped>
  /* 移除原有的滚动条样式 */
  ::-webkit-scrollbar {
    display: none;
  }

  .truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
