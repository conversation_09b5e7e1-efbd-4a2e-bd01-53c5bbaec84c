<template>
  <div class="p-[1vw] space-y-[1vw]">
    <!-- 资产基本信息 -->
    <div class="bg-blue-500/10 border border-blue-500/20 rounded p-[0.8vw]">
      <h3 class="text-[0.8vw] text-blue-400 font-semibold mb-[0.6vw]">{{ asset?.name || '资产监控详情' }}</h3>
      <div class="grid grid-cols-3 gap-[0.8vw] text-[0.6vw]">
        <div
          ><span class="text-gray-400">IP地址：</span><span class="text-white">{{ asset?.ipAddress }}</span></div
        >
        <div
          ><span class="text-gray-400">设备类型：</span><span class="text-white">{{ asset?.type }}</span></div
        >
        <div
          ><span class="text-gray-400">当前状态：</span>
          <span
            :class="[
              asset?.status === 'online'
                ? 'text-green-400'
                : asset?.status === 'warning'
                  ? 'text-yellow-400'
                  : asset?.status === 'error'
                    ? 'text-red-400'
                    : 'text-gray-400',
            ]"
            >{{ getStatusText(asset?.status) }}</span
          >
        </div>
      </div>
    </div>

    <!-- 实时监控指标 -->
    <div class="grid grid-cols-2 gap-[1vw]">
      <!-- CPU监控 -->
      <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
        <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">CPU使用率</h4>
        <div class="flex items-center justify-between mb-[0.4vw]">
          <span class="text-[0.6vw] text-gray-400">当前使用率</span>
          <span
            :class="[
              'text-[0.8vw] font-bold',
              (asset?.cpuUsage || 0) > 80 ? 'text-red-400' : (asset?.cpuUsage || 0) > 60 ? 'text-yellow-400' : 'text-green-400',
            ]"
            >{{ asset?.cpuUsage || 0 }}%</span
          >
        </div>
        <div class="w-full bg-gray-600 rounded-full h-[0.4vw]">
          <div
            :class="[
              'h-full rounded-full transition-all duration-300',
              (asset?.cpuUsage || 0) > 80 ? 'bg-red-500' : (asset?.cpuUsage || 0) > 60 ? 'bg-yellow-500' : 'bg-green-500',
            ]"
            :style="{ width: (asset?.cpuUsage || 0) + '%' }"
          ></div>
        </div>
        <div class="mt-[0.4vw] text-[0.5vw] text-gray-500"> 平均负载：{{ ((asset?.cpuUsage || 0) * 0.8).toFixed(1) }}% </div>
      </div>

      <!-- 内存监控 -->
      <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
        <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">内存使用率</h4>
        <div class="flex items-center justify-between mb-[0.4vw]">
          <span class="text-[0.6vw] text-gray-400">当前使用率</span>
          <span
            :class="[
              'text-[0.8vw] font-bold',
              (asset?.memoryUsage || 0) > 80 ? 'text-red-400' : (asset?.memoryUsage || 0) > 60 ? 'text-yellow-400' : 'text-green-400',
            ]"
            >{{ asset?.memoryUsage || 0 }}%</span
          >
        </div>
        <div class="w-full bg-gray-600 rounded-full h-[0.4vw]">
          <div
            :class="[
              'h-full rounded-full transition-all duration-300',
              (asset?.memoryUsage || 0) > 80 ? 'bg-red-500' : (asset?.memoryUsage || 0) > 60 ? 'bg-yellow-500' : 'bg-green-500',
            ]"
            :style="{ width: (asset?.memoryUsage || 0) + '%' }"
          ></div>
        </div>
        <div class="mt-[0.4vw] text-[0.5vw] text-gray-500"> 已用内存：{{ (((asset?.memoryUsage || 0) / 100) * 16).toFixed(1) }}GB / 16GB </div>
      </div>

      <!-- 磁盘监控 -->
      <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
        <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">磁盘使用率</h4>
        <div class="flex items-center justify-between mb-[0.4vw]">
          <span class="text-[0.6vw] text-gray-400">当前使用率</span>
          <span
            :class="[
              'text-[0.8vw] font-bold',
              (asset?.diskUsage || 0) > 80 ? 'text-red-400' : (asset?.diskUsage || 0) > 60 ? 'text-yellow-400' : 'text-green-400',
            ]"
            >{{ asset?.diskUsage || 0 }}%</span
          >
        </div>
        <div class="w-full bg-gray-600 rounded-full h-[0.4vw]">
          <div
            :class="[
              'h-full rounded-full transition-all duration-300',
              (asset?.diskUsage || 0) > 80 ? 'bg-red-500' : (asset?.diskUsage || 0) > 60 ? 'bg-yellow-500' : 'bg-green-500',
            ]"
            :style="{ width: (asset?.diskUsage || 0) + '%' }"
          ></div>
        </div>
        <div class="mt-[0.4vw] text-[0.5vw] text-gray-500"> 已用空间：{{ (((asset?.diskUsage || 0) / 100) * 500).toFixed(0) }}GB / 500GB </div>
      </div>

      <!-- 网络监控 -->
      <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
        <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">网络流量</h4>
        <div class="flex items-center justify-between mb-[0.4vw]">
          <span class="text-[0.6vw] text-gray-400">当前流量</span>
          <span class="text-[0.8vw] font-bold text-blue-400">{{ asset?.networkTraffic || '0MB/s' }}</span>
        </div>
        <div class="space-y-[0.3vw] text-[0.5vw] text-gray-500">
          <div>入站流量：{{ Math.floor(Math.random() * 100) + 20 }}MB/s</div>
          <div>出站流量：{{ Math.floor(Math.random() * 80) + 10 }}MB/s</div>
          <div>总流量：{{ (Math.random() * 10 + 5).toFixed(2) }}GB</div>
        </div>
      </div>
    </div>

    <!-- 历史趋势图 -->
    <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
      <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">24小时趋势</h4>
      <div class="h-[8vw] flex items-end justify-between space-x-[0.2vw]">
        <div
          v-for="(hour, index) in 24"
          :key="index"
          class="flex-1 bg-blue-500/30 rounded-t"
          :style="{ height: Math.random() * 100 + '%' }"
          :title="`${index}:00 - ${Math.floor(Math.random() * 100)}%`"
        ></div>
      </div>
      <div class="flex justify-between text-[0.5vw] text-gray-500 mt-[0.4vw]">
        <span>00:00</span>
        <span>06:00</span>
        <span>12:00</span>
        <span>18:00</span>
        <span>24:00</span>
      </div>
    </div>

    <!-- 告警信息 -->
    <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
      <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">最近告警</h4>
      <div class="space-y-[0.4vw]">
        <div
          v-for="(alarm, index) in recentAlarms"
          :key="index"
          class="flex items-center justify-between p-[0.4vw] bg-red-500/10 border border-red-500/20 rounded"
        >
          <div class="flex items-center space-x-[0.4vw]">
            <span class="text-[0.8vw] text-red-400">⚠️</span>
            <span class="text-[0.6vw] text-white">{{ alarm.message }}</span>
          </div>
          <span class="text-[0.5vw] text-gray-400">{{ alarm.time }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  const props = defineProps({
    asset: { type: Object, default: null },
  });

  const getStatusText = (status) => {
    const statusMap = {
      online: '在线',
      offline: '离线',
      warning: '告警',
      error: '故障',
    };
    return statusMap[status] || '未知';
  };

  // 模拟最近告警数据
  const recentAlarms = ref([
    { message: 'CPU使用率超过85%', time: '2分钟前' },
    { message: '内存使用率达到90%', time: '5分钟前' },
    { message: '磁盘空间不足', time: '10分钟前' },
  ]);
</script>
