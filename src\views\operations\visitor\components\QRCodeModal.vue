<template>
  <a-modal
    v-model:visible="visible"
    title="访客二维码"
    width="400px"
    :footer="null"
  >
    <div class="text-center space-y-4">
      <div v-if="qrCode" class="flex justify-center">
        <img :src="qrCode" alt="访客二维码" class="w-48 h-48 border border-gray-200 rounded" />
      </div>
      
      <div v-if="visitorName" class="text-lg font-semibold">
        {{ visitorName }}
      </div>
      
      <div class="text-sm text-gray-500 space-y-1">
        <div>请访客使用此二维码进行签到</div>
        <div>二维码有效期：24小时</div>
      </div>
      
      <div class="flex justify-center space-x-2">
        <a-button @click="downloadQRCode">
          <DownloadOutlined />
          下载二维码
        </a-button>
        <a-button @click="printQRCode">
          <PrinterOutlined />
          打印二维码
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { DownloadOutlined, PrinterOutlined } from '@ant-design/icons-vue';

  // Props
  interface Props {
    visible: boolean;
    qrCode: string;
    visitorName?: string;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    'update:visible': [value: boolean];
  }>();

  // 计算属性
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  // 方法
  const downloadQRCode = () => {
    if (!props.qrCode) {
      message.error('二维码不存在');
      return;
    }

    try {
      const link = document.createElement('a');
      link.href = props.qrCode;
      link.download = `访客二维码_${props.visitorName || 'visitor'}_${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success('二维码下载成功');
    } catch (error) {
      message.error('下载失败');
    }
  };

  const printQRCode = () => {
    if (!props.qrCode) {
      message.error('二维码不存在');
      return;
    }

    try {
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        message.error('无法打开打印窗口，请检查浏览器设置');
        return;
      }

      const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>访客二维码</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              text-align: center;
              font-family: Arial, sans-serif;
            }
            .qr-container {
              display: inline-block;
              border: 2px solid #000;
              padding: 20px;
              margin: 20px;
            }
            .qr-code {
              width: 200px;
              height: 200px;
              margin: 10px 0;
            }
            .visitor-name {
              font-size: 18px;
              font-weight: bold;
              margin: 10px 0;
            }
            .instructions {
              font-size: 12px;
              color: #666;
              margin-top: 10px;
            }
            @media print {
              body { margin: 0; }
              .qr-container { border: 1px solid #000; }
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <h2>访客二维码</h2>
            <img src="${props.qrCode}" alt="访客二维码" class="qr-code" />
            ${props.visitorName ? `<div class="visitor-name">${props.visitorName}</div>` : ''}
            <div class="instructions">
              <div>请使用此二维码进行签到</div>
              <div>有效期：24小时</div>
            </div>
          </div>
        </body>
        </html>
      `;

      printWindow.document.write(printContent);
      printWindow.document.close();
      
      // 等待图片加载完成后打印
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      message.success('正在准备打印...');
    } catch (error) {
      message.error('打印失败');
    }
  };
</script>
