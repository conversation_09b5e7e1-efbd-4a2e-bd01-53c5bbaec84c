<template>
  <div
    class="absolute w-full h-[3vw] bottom-0 left-0 flex items-center justify-center pointer-events-auto bg-[rgba(21,28,45,0.9)]"
    :style="{ backgroundImage: `url(${bottomnavbg})`, backgroundSize: 'cover', backgroundRepeat: 'no-repeat' }"
  >
    <!-- 常规导航项 -->
    <div class="flex-1 flex items-center justify-center">
      <div
        v-for="item of navigationItems"
        :key="item.title"
        class="flex items-center justify-center w-[8vw] h-[2vw] mx-[0.5vw] cursor-pointer transition-all duration-200 rounded-full border border-white/20"
        :class="
          activeMenu === item.title
            ? 'bg-[rgba(36,108,249,0.1)] border-[#246cf9] shadow-[0_0_10px_rgba(36,108,249,0.2)]'
            : 'hover:border-[#246CF9]/50 hover:bg-white/5'
        "
        @click="$emit('menu-select', item.title)"
      >
        <component
          :is="item.icon"
          class="text-[1vw] mr-[0.5vw] transition-colors duration-300"
          :class="activeMenu === item.title ? 'text-[#246CF9]' : 'text-white'"
        />
        <div class="text-[0.7vw] transition-colors duration-300" :class="activeMenu === item.title ? 'text-[#246CF9] font-medium' : 'text-white'">
          {{ item.title }}
        </div>
      </div>
    </div>

    <!-- 返回园区总览按钮 - 圆形图标按钮 -->
    <div class="mr-4">
      <div
        class="flex items-center justify-center w-[2vw] h-[2vw] cursor-pointer transition-all duration-200 rounded-full border border-white/20 hover:border-[#246CF9]/50 hover:bg-white/5 group"
        @click="returnToPark"
        title="返回园区总览"
      >
        <LogoutOutlined class="text-[1.2vw] text-white" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import type { Component } from 'vue';

  defineProps<{
    activeMenu: string;
  }>();
  import {
    FireOutlined,
    LineChartOutlined,
    SafetyCertificateOutlined,
    ToolOutlined,
    AppstoreOutlined,
    LogoutOutlined,
    BulbOutlined,
    ProjectOutlined,
    BuildOutlined,
    AuditOutlined,
    CloudOutlined,
    FileTextOutlined,
  } from '@ant-design/icons-vue';
  import bottomnavbg from '@/assets/scene/bottomnavbg.png';
  import { useGlobalThreeStore } from '/@/views/scene/store/globalThreeStore';
  import { SELECTION_CONFIG } from '../config';

  interface NavigationItem {
    title: string;
    icon: Component;
  }

  // 全局状态管理
  const globalThreeStore = useGlobalThreeStore();

  // 定义导航项与图标的映射
  const navigationItems = computed<NavigationItem[]>(() => [
    { title: '智慧安消防', icon: FireOutlined },
    { title: '智慧能耗', icon: BulbOutlined },
    { title: '运营数据管理', icon: LineChartOutlined },
    { title: '智慧告警', icon: SafetyCertificateOutlined },
    { title: '智慧运维', icon: ToolOutlined },
    { title: '资产可视化管理', icon: AppstoreOutlined },
    { title: '综合管理', icon: ProjectOutlined },
    { title: 'BIM技术应用', icon: BuildOutlined },
    { title: '存储和部署管理', icon: CloudOutlined },
    { title: '评测服务', icon: AuditOutlined },
    { title: 'PPT演示', icon: FileTextOutlined },
  ]);

  // 返回园区总览的方法
  const returnToPark = () => {
    // 如果处于设备观察模式,先退出
    if (SELECTION_CONFIG.observeMode.enabled) {
      window.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
    }

    // 切换到外景调试设置
    globalThreeStore.switchSceneDebugSettings('exterior');

    // 切换回园区总览视图
    globalThreeStore.toggleView();
  };
</script>
