<template>
  <div class="p-[1vw] space-y-[1vw]">
    <!-- 资产基本信息 -->
    <div class="bg-blue-500/10 border border-blue-500/20 rounded p-[0.8vw]">
      <h3 class="text-[0.8vw] text-blue-400 font-semibold mb-[0.6vw] flex items-center">
        <span class="mr-[0.4vw]">📦</span>
        {{ asset?.name || '资产详情' }}
      </h3>
      <div class="grid grid-cols-3 gap-[0.8vw] text-[0.6vw]">
        <div
          ><span class="text-gray-400">资产编号：</span><span class="text-white">{{ asset?.assetCode }}</span></div
        >
        <div
          ><span class="text-gray-400">资产类型：</span><span class="text-white">{{ asset?.type }}</span></div
        >
        <div
          ><span class="text-gray-400">当前状态：</span>
          <span :class="[asset?.status === 'accepted' ? 'text-green-400' : asset?.status === 'pending' ? 'text-yellow-400' : 'text-red-400']">{{
            getStatusText(asset?.status)
          }}</span>
        </div>
      </div>
    </div>

    <!-- 详细信息标签页 -->
    <div class="bg-black/20 rounded border border-white/10 overflow-hidden">
      <!-- 标签页导航 -->
      <div class="flex border-b border-white/10">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          :class="[
            'px-[1vw] py-[0.6vw] text-[0.6vw] font-medium transition-all duration-200 border-b-2 bg-transparent',
            activeTab === tab.key
              ? 'text-blue-400 border-blue-400 bg-blue-400/10'
              : 'text-gray-400 border-transparent hover:text-white hover:bg-white/5',
          ]"
          @click="activeTab = tab.key"
        >
          <span class="mr-[0.3vw]">{{ tab.icon }}</span>
          {{ tab.label }}
        </button>
      </div>

      <!-- 标签页内容 -->
      <div class="p-[1vw] max-h-[25vw] overflow-auto">
        <!-- 基本信息 -->
        <div v-if="activeTab === 'basic'" class="space-y-[0.6vw]">
          <div class="grid grid-cols-2 gap-[0.8vw]">
            <div v-for="field in basicFields" :key="field.key" class="bg-black/20 rounded p-[0.6vw]">
              <div class="text-[0.5vw] text-gray-400 mb-[0.2vw]">{{ field.label }}</div>
              <div class="text-[0.6vw] text-white">{{ asset?.[field.key] || '暂无数据' }}</div>
            </div>
          </div>
        </div>

        <!-- 技术规格 -->
        <div v-if="activeTab === 'specs'" class="space-y-[0.6vw]">
          <div class="grid grid-cols-2 gap-[0.8vw]">
            <div v-for="spec in techSpecs" :key="spec.key" class="bg-black/20 rounded p-[0.6vw]">
              <div class="text-[0.5vw] text-gray-400 mb-[0.2vw]">{{ spec.label }}</div>
              <div class="text-[0.6vw] text-white">{{ asset?.[spec.key] || generateMockData(spec.type) }}</div>
            </div>
          </div>
        </div>

        <!-- 维保信息 -->
        <div v-if="activeTab === 'maintenance'" class="space-y-[0.6vw]">
          <div class="grid grid-cols-1 gap-[0.6vw]">
            <div class="bg-black/20 rounded p-[0.6vw]">
              <h4 class="text-[0.6vw] text-white font-semibold mb-[0.4vw]">维保状态</h4>
              <div class="grid grid-cols-3 gap-[0.6vw] text-[0.5vw]">
                <div><span class="text-gray-400">保修期：</span><span class="text-green-400">有效</span></div>
                <div><span class="text-gray-400">到期时间：</span><span class="text-white">2025-12-31</span></div>
                <div
                  ><span class="text-gray-400">维保商：</span><span class="text-white">{{ asset?.supplier || '原厂维保' }}</span></div
                >
              </div>
            </div>

            <div class="bg-black/20 rounded p-[0.6vw]">
              <h4 class="text-[0.6vw] text-white font-semibold mb-[0.4vw]">最近维护记录</h4>
              <div class="space-y-[0.3vw]">
                <div v-for="record in maintenanceRecords" :key="record.id" class="flex justify-between items-center p-[0.4vw] bg-white/5 rounded">
                  <div>
                    <div class="text-[0.5vw] text-white">{{ record.type }}</div>
                    <div class="text-[0.4vw] text-gray-400">{{ record.date }}</div>
                  </div>
                  <span
                    :class="[
                      'px-[0.3vw] py-[0.1vw] rounded text-[0.4vw]',
                      record.status === 'completed' ? 'bg-green-500/20 text-green-400' : 'bg-yellow-500/20 text-yellow-400',
                    ]"
                  >
                    {{ record.status === 'completed' ? '已完成' : '进行中' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 使用历史 -->
        <div v-if="activeTab === 'history'" class="space-y-[0.6vw]">
          <div class="bg-black/20 rounded p-[0.6vw]">
            <h4 class="text-[0.6vw] text-white font-semibold mb-[0.4vw]">使用历史</h4>
            <div class="space-y-[0.3vw]">
              <div v-for="history in usageHistory" :key="history.id" class="flex justify-between items-center p-[0.4vw] bg-white/5 rounded">
                <div>
                  <div class="text-[0.5vw] text-white">{{ history.action }}</div>
                  <div class="text-[0.4vw] text-gray-400">{{ history.date }} - {{ history.user }}</div>
                </div>
                <span class="text-[0.4vw] text-blue-400">{{ history.location }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 告警记录 -->
        <div v-if="activeTab === 'alarms'" class="space-y-[0.6vw]">
          <div class="bg-black/20 rounded p-[0.6vw]">
            <h4 class="text-[0.6vw] text-white font-semibold mb-[0.4vw]">最近告警</h4>
            <div v-if="alarmRecords.length === 0" class="text-center py-[2vw]">
              <div class="text-[1.5vw] text-green-400 mb-[0.5vw]">✅</div>
              <div class="text-[0.6vw] text-green-400">暂无告警记录</div>
              <div class="text-[0.5vw] text-gray-400">设备运行正常</div>
            </div>
            <div v-else class="space-y-[0.3vw]">
              <div
                v-for="alarm in alarmRecords"
                :key="alarm.id"
                class="flex justify-between items-center p-[0.4vw] bg-red-500/10 border border-red-500/20 rounded"
              >
                <div>
                  <div class="text-[0.5vw] text-white">{{ alarm.message }}</div>
                  <div class="text-[0.4vw] text-gray-400">{{ alarm.date }}</div>
                </div>
                <span
                  :class="[
                    'px-[0.3vw] py-[0.1vw] rounded text-[0.4vw]',
                    alarm.level === 'critical'
                      ? 'bg-red-500/20 text-red-400'
                      : alarm.level === 'warning'
                        ? 'bg-yellow-500/20 text-yellow-400'
                        : 'bg-blue-500/20 text-blue-400',
                  ]"
                >
                  {{ alarm.level === 'critical' ? '严重' : alarm.level === 'warning' ? '警告' : '信息' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-[0.6vw] pt-[0.6vw] border-t border-white/10">
      <button
        class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
        @click="editAsset"
      >
        编辑资产
      </button>
      <button class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors" @click="printQRCode">
        打印二维码
      </button>
      <button class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors" @click="exportDetail">
        导出详情
      </button>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';

  const props = defineProps({
    asset: { type: Object, default: null },
  });

  const activeTab = ref('basic');

  // 标签页配置
  const tabs = [
    { key: 'basic', label: '基本信息', icon: '📋' },
    { key: 'specs', label: '技术规格', icon: '⚙️' },
    { key: 'maintenance', label: '维保信息', icon: '🔧' },
    { key: 'history', label: '使用历史', icon: '📚' },
    { key: 'alarms', label: '告警记录', icon: '🚨' },
  ];

  // 基本信息字段
  const basicFields = [
    { key: 'assetCode', label: '资产编号' },
    { key: 'name', label: '资产名称' },
    { key: 'model', label: '型号规格' },
    { key: 'serialNumber', label: '序列号' },
    { key: 'supplier', label: '供应商' },
    { key: 'inboundDate', label: '入库时间' },
    { key: 'purchaseDate', label: '采购时间' },
    { key: 'location', label: '存放位置' },
  ];

  // 技术规格字段
  const techSpecs = [
    { key: 'cpu', label: 'CPU', type: 'cpu' },
    { key: 'memory', label: '内存', type: 'memory' },
    { key: 'storage', label: '存储', type: 'storage' },
    { key: 'network', label: '网络', type: 'network' },
    { key: 'power', label: '功耗', type: 'power' },
    { key: 'dimensions', label: '尺寸', type: 'dimensions' },
  ];

  // 模拟数据
  const maintenanceRecords = ref([
    { id: 1, type: '定期检查', date: '2024-01-15', status: 'completed' },
    { id: 2, type: '系统更新', date: '2024-01-10', status: 'completed' },
    { id: 3, type: '硬件清洁', date: '2024-01-05', status: 'completed' },
  ]);

  const usageHistory = ref([
    { id: 1, action: '设备入库', date: '2024-01-01', user: '张三', location: '仓库A' },
    { id: 2, action: '分配使用', date: '2024-01-02', user: '李四', location: '机房B' },
    { id: 3, action: '维护检查', date: '2024-01-15', user: '王五', location: '机房B' },
  ]);

  const alarmRecords = ref([]);

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      pending: '待验收',
      accepted: '已验收',
      rejected: '已拒收',
    };
    return statusMap[status] || status;
  };

  const generateMockData = (type) => {
    const mockData = {
      cpu: 'Intel Xeon E5-2680 v4 @ 2.40GHz',
      memory: '64GB DDR4 ECC',
      storage: '2TB SSD + 4TB HDD',
      network: 'Gigabit Ethernet x4',
      power: '650W',
      dimensions: '482 x 430 x 87 mm',
    };
    return mockData[type] || '暂无数据';
  };

  const editAsset = () => {
    alert('编辑资产功能');
  };

  const printQRCode = () => {
    alert('正在生成二维码...');
  };

  const exportDetail = () => {
    alert('导出资产详情');
  };
</script>
