<template>
  <div class="p-4">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <AuditOutlined class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">总任务数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.totalTasks }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <CheckCircleOutlined class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">已完成</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.completedTasks }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <ClockCircleOutlined class="h-8 w-8 text-orange-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">进行中</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.pendingTasks }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <ExclamationCircleOutlined class="h-8 w-8 text-red-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">超期任务</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.overdueTasks }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 巡检效率和设备统计 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- 巡检效率 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <BarChartOutlined class="mr-2" />
          巡检效率
        </h3>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">完成率</span>
            <span class="font-semibold text-green-600">{{ stats.completionRate }}%</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">平均时长</span>
            <span class="font-semibold">{{ stats.avgDuration }}分钟</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">今日任务</span>
            <span class="font-semibold">{{ stats.todayTasks }}</span>
          </div>
        </div>
      </div>

      <!-- 设备统计 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <DesktopOutlined class="mr-2" />
          设备统计
        </h3>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">设备总数</span>
            <span class="font-semibold">{{ stats.deviceCount }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">发现问题</span>
            <span class="font-semibold text-red-600">{{ stats.issueCount }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">巡检员</span>
            <span class="font-semibold">{{ stats.inspectorCount }}</span>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <ThunderboltOutlined class="mr-2" />
          快速操作
        </h3>
        <div class="space-y-2">
          <a-button type="primary" block @click="showCreateTaskModal">
            <PlusOutlined />
            创建巡检任务
          </a-button>
          <a-button block @click="showRouteManageModal">
            <RouteOutlined />
            巡检路线管理
          </a-button>
          <a-button block @click="showQRCodeGenerateModal">
            <QrcodeOutlined />
            生成检查点二维码
          </a-button>
        </div>
      </div>
    </div>

    <!-- 操作按钮和筛选 -->
    <div class="mb-4 flex justify-between items-center">
      <div class="flex space-x-2">
        <a-button type="primary" @click="showCreateTaskModal">
          <PlusOutlined />
          创建任务
        </a-button>
        <a-button @click="showAssignTaskModal">
          <UserOutlined />
          分配任务
        </a-button>
        <a-button @click="showInspectionRecordsModal">
          <FileTextOutlined />
          巡检记录
        </a-button>
        <a-button @click="refreshData">
          <ReloadOutlined />
          刷新数据
        </a-button>
      </div>
      
      <div class="flex space-x-2">
        <a-input-search
          v-model:value="searchText"
          placeholder="搜索任务编号或标题"
          style="width: 200px"
          @search="handleSearch"
        />
        <a-select
          v-model:value="statusFilter"
          placeholder="状态筛选"
          style="width: 120px"
          @change="handleSearch"
        >
          <a-select-option value="">全部状态</a-select-option>
          <a-select-option value="pending">待执行</a-select-option>
          <a-select-option value="in_progress">进行中</a-select-option>
          <a-select-option value="completed">已完成</a-select-option>
          <a-select-option value="overdue">已超期</a-select-option>
        </a-select>
        <a-select
          v-model:value="typeFilter"
          placeholder="类型筛选"
          style="width: 120px"
          @change="handleSearch"
        >
          <a-select-option value="">全部类型</a-select-option>
          <a-select-option value="routine">例行巡检</a-select-option>
          <a-select-option value="special">专项巡检</a-select-option>
          <a-select-option value="emergency">应急巡检</a-select-option>
          <a-select-option value="maintenance">维护巡检</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 巡检任务列表 -->
    <div class="bg-white rounded-lg shadow">
      <a-table
        :columns="columns"
        :data-source="taskList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'progress'">
            <a-progress
              :percent="getTaskProgress(record)"
              :status="getProgressStatus(record)"
              size="small"
            />
          </template>
          
          <template v-if="column.key === 'duration'">
            <span v-if="record.duration">{{ record.duration }}分钟</span>
            <span v-else-if="record.startTime">{{ getElapsedTime(record.startTime) }}</span>
            <span v-else>-</span>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button
                v-if="record.status === 'pending'"
                type="primary"
                size="small"
                @click="startTask(record)"
              >
                开始
              </a-button>
              
              <a-button
                v-if="record.status === 'in_progress'"
                size="small"
                @click="completeTask(record)"
              >
                完成
              </a-button>
              
              <a-button
                size="small"
                @click="viewTaskDetail(record)"
              >
                详情
              </a-button>
              
              <a-button
                v-if="record.status === 'pending'"
                size="small"
                @click="assignTask(record)"
              >
                分配
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 创建任务弹窗 -->
    <CreateTaskModal
      v-model:visible="createTaskModalVisible"
      @success="handleCreateTaskSuccess"
    />

    <!-- 分配任务弹窗 -->
    <AssignTaskModal
      v-model:visible="assignTaskModalVisible"
      :task="selectedTask"
      @success="handleAssignTaskSuccess"
    />

    <!-- 任务详情弹窗 -->
    <TaskDetailModal
      v-model:visible="taskDetailModalVisible"
      :task="selectedTask"
    />

    <!-- 巡检记录弹窗 -->
    <InspectionRecordsModal
      v-model:visible="recordsModalVisible"
    />

    <!-- 路线管理弹窗 -->
    <RouteManageModal
      v-model:visible="routeManageModalVisible"
      @success="handleRouteManageSuccess"
    />

    <!-- 二维码生成弹窗 -->
    <QRCodeGenerateModal
      v-model:visible="qrCodeGenerateModalVisible"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import {
    AuditOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    ExclamationCircleOutlined,
    BarChartOutlined,
    DesktopOutlined,
    ThunderboltOutlined,
    PlusOutlined,
    RouteOutlined,
    QrcodeOutlined,
    UserOutlined,
    FileTextOutlined,
    ReloadOutlined,
  } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';
  import {
    getInspectionTasks,
    getInspectionStats,
    startInspectionTask,
    completeInspectionTask,
    assignInspectionTask,
    type InspectionTask,
    type InspectionStats,
  } from '/@/api/operations/inspection';
  import CreateTaskModal from './components/CreateTaskModal.vue';
  import AssignTaskModal from './components/AssignTaskModal.vue';
  import TaskDetailModal from './components/TaskDetailModal.vue';
  import InspectionRecordsModal from './components/InspectionRecordsModal.vue';
  import RouteManageModal from './components/RouteManageModal.vue';
  import QRCodeGenerateModal from './components/QRCodeGenerateModal.vue';

  // 响应式数据
  const loading = ref(false);
  const taskList = ref<InspectionTask[]>([]);
  const stats = ref<InspectionStats>({
    totalTasks: 0,
    completedTasks: 0,
    pendingTasks: 0,
    overdueTasks: 0,
    completionRate: 0,
    avgDuration: 0,
    todayTasks: 0,
    weekTasks: 0,
    monthTasks: 0,
    issueCount: 0,
    deviceCount: 0,
    inspectorCount: 0,
  });

  // 搜索和筛选
  const searchText = ref('');
  const statusFilter = ref('');
  const typeFilter = ref('');

  // 分页
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 弹窗状态
  const createTaskModalVisible = ref(false);
  const assignTaskModalVisible = ref(false);
  const taskDetailModalVisible = ref(false);
  const recordsModalVisible = ref(false);
  const routeManageModalVisible = ref(false);
  const qrCodeGenerateModalVisible = ref(false);
  const selectedTask = ref<InspectionTask | null>(null);

  // 表格列定义
  const columns = [
    {
      title: '任务编号',
      dataIndex: 'taskNumber',
      key: 'taskNumber',
    },
    {
      title: '任务标题',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '类型',
      key: 'type',
    },
    {
      title: '优先级',
      key: 'priority',
    },
    {
      title: '状态',
      key: 'status',
    },
    {
      title: '执行人',
      dataIndex: 'assignee',
      key: 'assignee',
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '进度',
      key: 'progress',
    },
    {
      title: '计划时间',
      dataIndex: 'scheduledTime',
      key: 'scheduledTime',
    },
    {
      title: '耗时',
      key: 'duration',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
    },
  ];

  // 方法
  const loadTaskList = async () => {
    loading.value = true;
    try {
      const params = {
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
        taskNumber: searchText.value || undefined,
        status: statusFilter.value || undefined,
        type: typeFilter.value || undefined,
      };
      
      const response = await getInspectionTasks(params);
      taskList.value = response.records;
      pagination.total = response.total;
    } catch (error) {
      message.error('获取巡检任务失败');
    } finally {
      loading.value = false;
    }
  };

  const loadStats = async () => {
    try {
      stats.value = await getInspectionStats();
    } catch (error) {
      message.error('获取统计数据失败');
    }
  };

  const refreshData = () => {
    loadTaskList();
    loadStats();
  };

  const handleSearch = () => {
    pagination.current = 1;
    loadTaskList();
  };

  const handleTableChange = (pag: any) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    loadTaskList();
  };

  const showCreateTaskModal = () => {
    createTaskModalVisible.value = true;
  };

  const showAssignTaskModal = () => {
    assignTaskModalVisible.value = true;
  };

  const showInspectionRecordsModal = () => {
    recordsModalVisible.value = true;
  };

  const showRouteManageModal = () => {
    routeManageModalVisible.value = true;
  };

  const showQRCodeGenerateModal = () => {
    qrCodeGenerateModalVisible.value = true;
  };

  const startTask = async (task: InspectionTask) => {
    try {
      await startInspectionTask(task.id);
      message.success('任务已开始');
      refreshData();
    } catch (error) {
      message.error('开始任务失败');
    }
  };

  const completeTask = async (task: InspectionTask) => {
    try {
      await completeInspectionTask(task.id, '任务完成');
      message.success('任务已完成');
      refreshData();
    } catch (error) {
      message.error('完成任务失败');
    }
  };

  const assignTask = (task: InspectionTask) => {
    selectedTask.value = task;
    assignTaskModalVisible.value = true;
  };

  const viewTaskDetail = (task: InspectionTask) => {
    selectedTask.value = task;
    taskDetailModalVisible.value = true;
  };

  // 状态和类型相关方法
  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'orange',
      in_progress: 'blue',
      completed: 'green',
      overdue: 'red',
      cancelled: 'gray',
    };
    return colors[status] || 'gray';
  };

  const getStatusText = (status: string) => {
    const texts = {
      pending: '待执行',
      in_progress: '进行中',
      completed: '已完成',
      overdue: '已超期',
      cancelled: '已取消',
    };
    return texts[status] || status;
  };

  const getTypeColor = (type: string) => {
    const colors = {
      routine: 'blue',
      special: 'purple',
      emergency: 'red',
      maintenance: 'orange',
    };
    return colors[type] || 'gray';
  };

  const getTypeText = (type: string) => {
    const texts = {
      routine: '例行巡检',
      special: '专项巡检',
      emergency: '应急巡检',
      maintenance: '维护巡检',
    };
    return texts[type] || type;
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
      urgent: 'magenta',
    };
    return colors[priority] || 'gray';
  };

  const getPriorityText = (priority: string) => {
    const texts = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急',
    };
    return texts[priority] || priority;
  };

  const getTaskProgress = (task: InspectionTask) => {
    if (task.status === 'completed') return 100;
    if (task.status === 'pending') return 0;
    
    // 根据检查点完成情况计算进度
    const totalCheckPoints = task.checkPoints?.length || 0;
    if (totalCheckPoints === 0) return 0;
    
    // 这里应该根据实际的检查记录计算，暂时返回模拟值
    return Math.floor(Math.random() * 80) + 10;
  };

  const getProgressStatus = (task: InspectionTask) => {
    if (task.status === 'completed') return 'success';
    if (task.status === 'overdue') return 'exception';
    return 'active';
  };

  const getElapsedTime = (startTime: string) => {
    const start = dayjs(startTime);
    const now = dayjs();
    const minutes = now.diff(start, 'minute');
    
    if (minutes < 60) {
      return `${minutes}分钟`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}小时${remainingMinutes}分钟`;
    }
  };

  // 事件处理方法
  const handleCreateTaskSuccess = () => {
    createTaskModalVisible.value = false;
    refreshData();
  };

  const handleAssignTaskSuccess = () => {
    assignTaskModalVisible.value = false;
    refreshData();
  };

  const handleRouteManageSuccess = () => {
    routeManageModalVisible.value = false;
    message.success('路线管理操作成功');
  };

  // 生命周期
  onMounted(() => {
    refreshData();
  });
</script>
