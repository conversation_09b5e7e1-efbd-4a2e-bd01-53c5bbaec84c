<template>
  <a-modal
    v-model:visible="visible"
    title="任务详情"
    width="800px"
    :footer="null"
  >
    <div v-if="task">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="任务编号">{{ task.taskNumber }}</a-descriptions-item>
        <a-descriptions-item label="任务标题">{{ task.title }}</a-descriptions-item>
        <a-descriptions-item label="任务类型">{{ getTypeText(task.type) }}</a-descriptions-item>
        <a-descriptions-item label="优先级">{{ getPriorityText(task.priority) }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(task.status)">
            {{ getStatusText(task.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="执行人">{{ task.assignee }}</a-descriptions-item>
        <a-descriptions-item label="创建人">{{ task.creator }}</a-descriptions-item>
        <a-descriptions-item label="位置">{{ task.location }}</a-descriptions-item>
        <a-descriptions-item label="楼层">{{ task.floor }}</a-descriptions-item>
        <a-descriptions-item label="区域">{{ task.area }}</a-descriptions-item>
        <a-descriptions-item label="计划时间">{{ task.scheduledTime }}</a-descriptions-item>
        <a-descriptions-item label="开始时间">{{ task.startTime || '-' }}</a-descriptions-item>
        <a-descriptions-item label="结束时间">{{ task.endTime || '-' }}</a-descriptions-item>
        <a-descriptions-item label="耗时">{{ task.duration ? task.duration + '分钟' : '-' }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ task.createTime }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ task.updateTime }}</a-descriptions-item>
      </a-descriptions>

      <a-divider>任务描述</a-divider>
      <p>{{ task.description }}</p>

      <a-divider>检查点列表</a-divider>
      <a-table
        :columns="checkPointColumns"
        :data-source="task.checkPoints"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'required'">
            <a-tag :color="record.required ? 'red' : 'blue'">
              {{ record.required ? '必检' : '可选' }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import type { InspectionTask } from '/@/api/operations/inspection';

  interface Props {
    visible: boolean;
    task: InspectionTask | null;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{ 'update:visible': [value: boolean] }>();

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const checkPointColumns = [
    { title: '检查点名称', dataIndex: 'name', key: 'name' },
    { title: '位置', dataIndex: 'location', key: 'location' },
    { title: '设备名称', dataIndex: 'deviceName', key: 'deviceName' },
    { title: '是否必检', key: 'required' },
    { title: '顺序', dataIndex: 'order', key: 'order' },
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'orange',
      in_progress: 'blue',
      completed: 'green',
      overdue: 'red',
      cancelled: 'gray',
    };
    return colors[status] || 'gray';
  };

  const getStatusText = (status: string) => {
    const texts = {
      pending: '待执行',
      in_progress: '进行中',
      completed: '已完成',
      overdue: '已超期',
      cancelled: '已取消',
    };
    return texts[status] || status;
  };

  const getTypeText = (type: string) => {
    const texts = {
      routine: '例行巡检',
      special: '专项巡检',
      emergency: '应急巡检',
      maintenance: '维护巡检',
    };
    return texts[type] || type;
  };

  const getPriorityText = (priority: string) => {
    const texts = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急',
    };
    return texts[priority] || priority;
  };
</script>
