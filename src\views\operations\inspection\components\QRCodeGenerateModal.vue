<template>
  <a-modal
    v-model:visible="visible"
    title="生成检查点二维码"
    width="600px"
    :footer="null"
  >
    <div class="space-y-4">
      <a-form layout="inline">
        <a-form-item label="选择检查点">
          <a-select
            v-model:value="selectedCheckPoint"
            placeholder="选择检查点"
            style="width: 200px"
            @change="generateQRCode"
          >
            <a-select-option value="1">空调设备检查点-001</a-select-option>
            <a-select-option value="2">消防设备检查点-002</a-select-option>
            <a-select-option value="3">电力设备检查点-003</a-select-option>
            <a-select-option value="4">网络设备检查点-004</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="generateAllQRCodes">
            批量生成
          </a-button>
        </a-form-item>
      </a-form>

      <div v-if="qrCode" class="text-center">
        <div class="mb-4">
          <img :src="qrCode" alt="检查点二维码" class="w-48 h-48 mx-auto border border-gray-200 rounded" />
        </div>
        <div class="space-x-2">
          <a-button @click="downloadQRCode">
            <DownloadOutlined />
            下载二维码
          </a-button>
          <a-button @click="printQRCode">
            <PrinterOutlined />
            打印二维码
          </a-button>
        </div>
      </div>

      <div v-if="!qrCode && !selectedCheckPoint" class="text-center text-gray-500 py-8">
        请选择检查点生成二维码
      </div>

      <a-divider>二维码说明</a-divider>
      <div class="text-sm text-gray-600 space-y-2">
        <p>• 每个检查点都有唯一的二维码标识</p>
        <p>• 巡检员可通过扫描二维码快速定位检查点</p>
        <p>• 二维码包含检查点ID、位置信息等数据</p>
        <p>• 建议将二维码贴在检查点附近明显位置</p>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { DownloadOutlined, PrinterOutlined } from '@ant-design/icons-vue';
  import { generateCheckPointQRCode } from '/@/api/operations/inspection';

  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{ 'update:visible': [value: boolean] }>();

  const selectedCheckPoint = ref<string>('');
  const qrCode = ref<string>('');

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const generateQRCode = async () => {
    if (!selectedCheckPoint.value) return;

    try {
      const response = await generateCheckPointQRCode(Number(selectedCheckPoint.value));
      qrCode.value = response.qrCode;
      message.success('二维码生成成功');
    } catch (error) {
      message.error('生成二维码失败');
    }
  };

  const generateAllQRCodes = () => {
    message.info('批量生成功能开发中...');
  };

  const downloadQRCode = () => {
    if (!qrCode.value) return;

    try {
      const link = document.createElement('a');
      link.href = qrCode.value;
      link.download = `检查点二维码_${selectedCheckPoint.value}_${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success('二维码下载成功');
    } catch (error) {
      message.error('下载失败');
    }
  };

  const printQRCode = () => {
    if (!qrCode.value) return;

    try {
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        message.error('无法打开打印窗口');
        return;
      }

      const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>检查点二维码</title>
          <style>
            body { margin: 0; padding: 20px; text-align: center; font-family: Arial, sans-serif; }
            .qr-container { display: inline-block; border: 2px solid #000; padding: 20px; margin: 20px; }
            .qr-code { width: 200px; height: 200px; margin: 10px 0; }
            .checkpoint-info { font-size: 16px; font-weight: bold; margin: 10px 0; }
            @media print { body { margin: 0; } .qr-container { border: 1px solid #000; } }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <h2>检查点二维码</h2>
            <img src="${qrCode.value}" alt="检查点二维码" class="qr-code" />
            <div class="checkpoint-info">检查点编号: ${selectedCheckPoint.value}</div>
            <div style="font-size: 12px; color: #666; margin-top: 10px;">
              请将此二维码贴在检查点附近明显位置
            </div>
          </div>
        </body>
        </html>
      `;

      printWindow.document.write(printContent);
      printWindow.document.close();
      
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      message.success('正在准备打印...');
    } catch (error) {
      message.error('打印失败');
    }
  };
</script>
