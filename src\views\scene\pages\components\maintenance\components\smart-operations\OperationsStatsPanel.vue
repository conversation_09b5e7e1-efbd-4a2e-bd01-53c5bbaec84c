<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 统计概览 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-chart-line mr-[0.4vw] text-blue-400"></i>
        运维统计概览
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in overviewStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：设备维护分类 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-tools mr-[0.4vw] text-blue-400"></i>
            设备维护分类
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="period in timePeriods"
              :key="period.key"
              @click="activePeriod = period.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activePeriod === period.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30',
              ]"
            >
              {{ period.label }}
            </button>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div
              v-for="category in maintenanceCategories"
              :key="category.id"
              class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all cursor-pointer"
              @click="showCategoryDetail(category)"
            >
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <i :class="category.icon" class="mr-[0.6vw] text-blue-400"></i>
                  <span class="text-[0.65vw] text-white font-medium">{{ category.name }}</span>
                </div>
                <span class="text-[0.6vw] text-blue-400">{{ category.count }}次</span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400">
                <span>完成率：{{ category.completionRate }}%</span>
                <span>平均耗时：{{ category.avgDuration }}</span>
              </div>
              <div class="mt-[0.3vw]">
                <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                  <div class="bg-blue-400 h-[0.3vw] rounded-full transition-all" :style="{ width: category.completionRate + '%' }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：检查统计和工单处理 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 检查统计 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-check-circle mr-[0.4vw] text-blue-400"></i>
            检查统计分析
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="fault in faultStats" :key="fault.type" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ fault.type }}</span>
                <span class="text-[0.6vw]" :class="getFaultLevelClass(fault.level)">{{ fault.count }}次</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>占比：{{ fault.percentage }}%</span>
                <span>趋势：{{ fault.trend }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 工单处理情况 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-clipboard-list mr-[0.4vw] text-blue-400"></i>
            工单处理情况
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="status in workOrderStats" :key="status.status" class="flex justify-between items-center">
              <div class="flex items-center">
                <div class="w-[0.6vw] h-[0.6vw] rounded-full mr-[0.4vw]" :class="status.colorClass"></div>
                <span class="text-[0.6vw] text-gray-400">{{ status.label }}</span>
              </div>
              <span class="text-[0.6vw] text-white font-medium">{{ status.count }}</span>
            </div>
          </div>

          <div class="mt-[0.6vw] pt-[0.6vw] border-t border-gray-600">
            <div class="flex justify-between text-[0.6vw]">
              <span class="text-gray-400">平均处理时间</span>
              <span class="text-white">2.5小时</span>
            </div>
            <div class="flex justify-between text-[0.6vw] mt-[0.2vw]">
              <span class="text-gray-400">及时完成率</span>
              <span class="text-green-400">95.2%</span>
            </div>
          </div>
        </div>

        <!-- 维护人员效率 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-user-cog mr-[0.4vw] text-blue-400"></i>
            维护人员效率
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="engineer in engineerStats" :key="engineer.name" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ engineer.name }}</span>
              <div class="flex items-center gap-[0.4vw]">
                <span class="text-[0.6vw] text-white">{{ engineer.completed }}</span>
                <div class="w-[3vw] bg-black/30 rounded-full h-[0.3vw]">
                  <div class="bg-green-400 h-[0.3vw] rounded-full" :style="{ width: engineer.efficiency + '%' }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';

  // 统计概览数据
  const overviewStats = ref([
    { label: '总工单', value: '156', valueClass: 'text-blue-400' },
    { label: '已完成', value: '156', valueClass: 'text-green-400' },
    { label: '进行中', value: '0', valueClass: 'text-green-400' },
    { label: '逾期', value: '0', valueClass: 'text-green-400' },
    { label: '完成率', value: '100%', valueClass: 'text-green-400' },
  ]);

  // 时间周期
  const timePeriods = ref([
    { key: 'today', label: '今日' },
    { key: 'week', label: '本周' },
    { key: 'month', label: '本月' },
  ]);

  const activePeriod = ref('week');

  // 设备维护分类
  const maintenanceCategories = ref([
    {
      id: 1,
      name: '空调系统维护',
      icon: 'fas fa-snowflake',
      count: 45,
      completionRate: 95,
      avgDuration: '2.5小时',
    },
    {
      id: 2,
      name: 'UPS设备检修',
      icon: 'fas fa-battery-full',
      count: 32,
      completionRate: 88,
      avgDuration: '1.8小时',
    },
    {
      id: 3,
      name: '网络设备维护',
      icon: 'fas fa-network-wired',
      count: 28,
      completionRate: 92,
      avgDuration: '1.2小时',
    },
    {
      id: 4,
      name: '消防系统检查',
      icon: 'fas fa-fire-extinguisher',
      count: 24,
      completionRate: 100,
      avgDuration: '3.0小时',
    },
    {
      id: 5,
      name: '配电系统维护',
      icon: 'fas fa-bolt',
      count: 18,
      completionRate: 85,
      avgDuration: '2.8小时',
    },
    {
      id: 6,
      name: '监控系统维护',
      icon: 'fas fa-video',
      count: 15,
      completionRate: 90,
      avgDuration: '1.5小时',
    },
  ]);

  // 检查统计
  const faultStats = ref([
    { type: '硬件检查', level: 'low', count: 0, percentage: 0, trend: '正常' },
    { type: '软件检查', level: 'low', count: 0, percentage: 0, trend: '正常' },
    { type: '网络检查', level: 'low', count: 0, percentage: 0, trend: '正常' },
    { type: '环境检查', level: 'low', count: 0, percentage: 0, trend: '正常' },
    { type: '其他检查', level: 'low', count: 0, percentage: 0, trend: '正常' },
  ]);

  // 工单状态统计
  const workOrderStats = ref([
    { status: 'completed', label: '已完成', count: 156, colorClass: 'bg-green-400' },
    { status: 'processing', label: '处理中', count: 0, colorClass: 'bg-green-400' },
    { status: 'pending', label: '待处理', count: 0, colorClass: 'bg-green-400' },
    { status: 'overdue', label: '已逾期', count: 0, colorClass: 'bg-green-400' },
  ]);

  // 维护人员效率
  const engineerStats = ref([
    { name: '张工程师', completed: 28, efficiency: 95 },
    { name: '李技师', completed: 24, efficiency: 88 },
    { name: '王师傅', completed: 22, efficiency: 92 },
    { name: '赵工', completed: 18, efficiency: 85 },
  ]);

  // 获取故障等级样式
  const getFaultLevelClass = (level) => {
    switch (level) {
      case 'high':
        return 'text-red-400';
      case 'medium':
        return 'text-yellow-400';
      case 'low':
        return 'text-green-400';
      default:
        return 'text-gray-400';
    }
  };

  // 显示分类详情
  const showCategoryDetail = (category) => {
    console.log('查看分类详情:', category);
    // 这里可以添加详情查看逻辑
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
