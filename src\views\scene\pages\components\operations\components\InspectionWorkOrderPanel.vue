<template>
  <div class="space-y-[0.6vw]">
    <!-- 工单统计 -->
    <div class="grid grid-cols-4 gap-[0.6vw] mb-[0.8vw]">
      <div class="text-center p-[0.6vw] bg-[#4CAF50]/10 rounded">
        <div class="text-[#4CAF50] text-[1.2vw] font-bold">{{ workOrderStats.completed }}</div>
        <div class="text-white/80 text-[0.6vw]">已完成</div>
      </div>
      <div class="text-center p-[0.6vw] bg-[#FF9800]/10 rounded">
        <div class="text-[#FF9800] text-[1.2vw] font-bold">{{ workOrderStats.pending }}</div>
        <div class="text-white/80 text-[0.6vw]">处理中</div>
      </div>
      <div class="text-center p-[0.6vw] bg-[#FF5252]/10 rounded">
        <div class="text-[#FF5252] text-[1.2vw] font-bold">{{ workOrderStats.urgent }}</div>
        <div class="text-white/80 text-[0.6vw]">紧急</div>
      </div>
      <div class="text-center p-[0.6vw] bg-[#9C27B0]/10 rounded">
        <div class="text-[#9C27B0] text-[1.2vw] font-bold">{{ workOrderStats.total }}</div>
        <div class="text-white/80 text-[0.6vw]">总计</div>
      </div>
    </div>

    <!-- 功能按钮 -->
    <div class="flex justify-between items-center">
      <h3 class="text-white text-[0.8vw] font-medium">巡检工单管理</h3>
      <div class="flex gap-[0.5vw]">
        <button
          @click="showCreateWorkOrder"
          class="px-[0.8vw] py-[0.4vw] bg-[#4CAF50]/20 text-[#4CAF50] rounded hover:bg-[#4CAF50]/30 transition-all text-[0.6vw]"
        >
          创建工单
        </button>
        <button
          @click="showPrintTaskSheet"
          class="px-[0.8vw] py-[0.4vw] bg-[#2196F3]/20 text-[#2196F3] rounded hover:bg-[#2196F3]/30 transition-all text-[0.6vw]"
        >
          打印任务单
        </button>
        <button
          @click="autoGenerateWorkOrders"
          class="px-[0.8vw] py-[0.4vw] bg-[#FF9800]/20 text-[#FF9800] rounded hover:bg-[#FF9800]/30 transition-all text-[0.6vw]"
        >
          自动生成故障工单
        </button>
      </div>
    </div>

    <!-- 工单列表 -->
    <div class="max-h-[25vw] overflow-y-auto custom-scrollbar">
      <table class="w-full border-collapse text-white">
        <thead>
          <tr>
            <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">工单编号</th>
            <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">类型</th>
            <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">设备/位置</th>
            <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">优先级</th>
            <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">状态</th>
            <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">创建时间</th>
            <th class="bg-[#3B8EE6]/10 text-[#3B8EE6] font-normal text-left p-[0.6vw] text-[0.6vw] border-b border-[#3B8EE6]/20">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="workOrder in workOrders" :key="workOrder.id" class="hover:bg-[#3B8EE6]/10 transition-all border-b border-white/10">
            <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ workOrder.orderNumber }}</td>
            <td class="p-[0.6vw] text-[0.6vw]">
              <span :class="getWorkOrderTypeClass(workOrder.type)">{{ getWorkOrderTypeText(workOrder.type) }}</span>
            </td>
            <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ workOrder.deviceName || workOrder.location }}</td>
            <td class="p-[0.6vw] text-[0.6vw]">
              <span :class="getPriorityClass(workOrder.priority)">{{ getPriorityText(workOrder.priority) }}</span>
            </td>
            <td class="p-[0.6vw] text-[0.6vw]">
              <span :class="getWorkOrderStatusClass(workOrder.status)">{{ getWorkOrderStatusText(workOrder.status) }}</span>
            </td>
            <td class="p-[0.6vw] text-[0.6vw] text-white/80">{{ formatDateTime(workOrder.createTime) }}</td>
            <td class="p-[0.6vw] text-[0.6vw]">
              <div class="flex gap-[0.2vw]">
                <button
                  @click="viewWorkOrderDetail(workOrder)"
                  class="px-[0.3vw] py-[0.1vw] bg-[#3B8EE6]/20 text-[#3B8EE6] rounded text-[0.5vw] hover:bg-[#3B8EE6]/30 transition-all"
                >
                  详情
                </button>
                <button
                  @click="printWorkOrder(workOrder)"
                  class="px-[0.3vw] py-[0.1vw] bg-[#2196F3]/20 text-[#2196F3] rounded text-[0.5vw] hover:bg-[#2196F3]/30 transition-all"
                >
                  打印
                </button>
                <button
                  v-if="workOrder.status === 'pending'"
                  @click="processWorkOrder(workOrder)"
                  class="px-[0.3vw] py-[0.1vw] bg-[#4CAF50]/20 text-[#4CAF50] rounded text-[0.5vw] hover:bg-[#4CAF50]/30 transition-all"
                >
                  处理
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- 工单详情弹窗 -->
  <ModalDialog v-model:visible="showWorkOrderDetail" title="工单详情" :iconSrc="dashboardTitle" width="70vw" height="70vh" :show-footer="false">
    <div class="p-[1vw]" v-if="selectedWorkOrder">
      <div class="grid grid-cols-2 gap-[1vw]">
        <!-- 基本信息 -->
        <div class="space-y-[0.6vw]">
          <h4 class="text-white text-[0.8vw] font-medium mb-[0.6vw]">基本信息</h4>
          <div class="bg-[#15274D]/30 p-[0.8vw] rounded space-y-[0.4vw]">
            <div class="flex justify-between">
              <span class="text-white/60 text-[0.6vw]">工单编号：</span>
              <span class="text-white text-[0.6vw]">{{ selectedWorkOrder.orderNumber }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60 text-[0.6vw]">工单类型：</span>
              <span :class="getWorkOrderTypeClass(selectedWorkOrder.type)" class="text-[0.6vw]">{{
                getWorkOrderTypeText(selectedWorkOrder.type)
              }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60 text-[0.6vw]">优先级：</span>
              <span :class="getPriorityClass(selectedWorkOrder.priority)" class="text-[0.6vw]">{{
                getPriorityText(selectedWorkOrder.priority)
              }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60 text-[0.6vw]">当前状态：</span>
              <span :class="getWorkOrderStatusClass(selectedWorkOrder.status)" class="text-[0.6vw]">{{
                getWorkOrderStatusText(selectedWorkOrder.status)
              }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60 text-[0.6vw]">创建时间：</span>
              <span class="text-white text-[0.6vw]">{{ formatDateTime(selectedWorkOrder.createTime) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60 text-[0.6vw]">创建人：</span>
              <span class="text-white text-[0.6vw]">{{ selectedWorkOrder.creator }}</span>
            </div>
          </div>
        </div>

        <!-- 设备信息 -->
        <div class="space-y-[0.6vw]">
          <h4 class="text-white text-[0.8vw] font-medium mb-[0.6vw]">设备信息</h4>
          <div class="bg-[#15274D]/30 p-[0.8vw] rounded space-y-[0.4vw]">
            <div class="flex justify-between">
              <span class="text-white/60 text-[0.6vw]">设备名称：</span>
              <span class="text-white text-[0.6vw]">{{ selectedWorkOrder.deviceName || '无' }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60 text-[0.6vw]">设备位置：</span>
              <span class="text-white text-[0.6vw]">{{ selectedWorkOrder.location }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60 text-[0.6vw]">故障描述：</span>
              <span class="text-white text-[0.6vw]">{{ selectedWorkOrder.description }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60 text-[0.6vw]">处理人员：</span>
              <span class="text-white text-[0.6vw]">{{ selectedWorkOrder.assignee || '未分配' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 处理记录 -->
      <div class="mt-[1vw]">
        <h4 class="text-white text-[0.8vw] font-medium mb-[0.6vw]">处理记录</h4>
        <div class="bg-[#15274D]/30 p-[0.8vw] rounded max-h-[15vw] overflow-y-auto custom-scrollbar">
          <div v-if="selectedWorkOrder.processRecords && selectedWorkOrder.processRecords.length > 0" class="space-y-[0.4vw]">
            <div v-for="record in selectedWorkOrder.processRecords" :key="record.id" class="border-l-2 border-[#3B8EE6]/30 pl-[0.6vw] py-[0.4vw]">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-white text-[0.6vw] font-medium">{{ record.action }}</span>
                <span class="text-white/60 text-[0.5vw]">{{ formatDateTime(record.time) }}</span>
              </div>
              <div class="text-white/80 text-[0.55vw]">{{ record.description }}</div>
              <div class="text-white/60 text-[0.5vw] mt-[0.2vw]">操作人：{{ record.operator }}</div>
            </div>
          </div>
          <div v-else class="text-white/60 text-[0.6vw] text-center py-[2vw]">暂无处理记录</div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end gap-[0.5vw] mt-[1vw]">
        <button
          @click="printWorkOrder(selectedWorkOrder)"
          class="px-[1vw] py-[0.4vw] bg-[#2196F3]/20 text-[#2196F3] rounded hover:bg-[#2196F3]/30 transition-all text-[0.6vw]"
        >
          打印工单
        </button>
        <button
          v-if="selectedWorkOrder.status === 'pending'"
          @click="processWorkOrder(selectedWorkOrder)"
          class="px-[1vw] py-[0.4vw] bg-[#4CAF50]/20 text-[#4CAF50] rounded hover:bg-[#4CAF50]/30 transition-all text-[0.6vw]"
        >
          开始处理
        </button>
        <button
          @click="showWorkOrderDetail = false"
          class="px-[1vw] py-[0.4vw] bg-[#666]/20 text-white rounded hover:bg-[#666]/30 transition-all text-[0.6vw]"
        >
          关闭
        </button>
      </div>
    </div>
  </ModalDialog>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';

  // 工单统计数据
  const workOrderStats = reactive({
    total: 5,
    completed: 5,
    pending: 0,
    urgent: 0,
  });

  // 工单数据
  const workOrders = reactive([
    {
      id: 1,
      orderNumber: 'WO-2024-001',
      type: 'inspection',
      deviceName: '空调主机A',
      location: '1F机房',
      priority: 'low',
      status: 'completed',
      description: '空调系统例行巡检，运行正常',
      creator: '张工程师',
      assignee: '李技师',
      createTime: '2024-01-16T09:30:00',
      processRecords: [
        {
          id: 1,
          action: '工单创建',
          description: '创建空调系统例行巡检工单',
          operator: '张工程师',
          time: '2024-01-16T09:30:00',
        },
        {
          id: 2,
          action: '开始处理',
          description: '开始执行空调系统巡检',
          operator: '李技师',
          time: '2024-01-16T10:00:00',
        },
        {
          id: 3,
          action: '工单完成',
          description: '空调系统运行正常，巡检完成',
          operator: '李技师',
          time: '2024-01-16T11:30:00',
        },
      ],
    },
    {
      id: 2,
      orderNumber: 'WO-2024-002',
      type: 'inspection',
      location: '2F配电室',
      priority: 'low',
      status: 'completed',
      description: '配电设备例行巡检，运行正常',
      creator: '王主管',
      assignee: '赵工程师',
      createTime: '2024-01-16T08:00:00',
      processRecords: [
        {
          id: 1,
          action: '工单创建',
          description: '创建配电设备例行巡检工单',
          operator: '王主管',
          time: '2024-01-16T08:00:00',
        },
        {
          id: 2,
          action: '开始处理',
          description: '开始执行配电设备巡检',
          operator: '赵工程师',
          time: '2024-01-16T08:30:00',
        },
        {
          id: 3,
          action: '工单完成',
          description: '配电设备运行正常，巡检完成',
          operator: '赵工程师',
          time: '2024-01-16T10:00:00',
        },
      ],
    },
    {
      id: 3,
      orderNumber: 'WO-2024-003',
      type: 'maintenance',
      deviceName: 'UPS电源系统',
      location: '配电室',
      priority: 'low',
      status: 'completed',
      description: 'UPS电源系统定期维护，运行正常',
      creator: '王主管',
      assignee: '王主管',
      createTime: '2024-01-16T10:15:00',
      processRecords: [
        {
          id: 1,
          action: '工单创建',
          description: '创建UPS电源系统定期维护工单',
          operator: '王主管',
          time: '2024-01-16T10:15:00',
        },
        {
          id: 2,
          action: '开始处理',
          description: '开始执行UPS系统维护',
          operator: '王主管',
          time: '2024-01-16T10:30:00',
        },
        {
          id: 3,
          action: '工单完成',
          description: 'UPS系统维护完成，运行正常',
          operator: '王主管',
          time: '2024-01-16T12:00:00',
        },
      ],
    },
    {
      id: 4,
      orderNumber: 'WO-2024-004',
      type: 'inspection',
      deviceName: '消防报警主机',
      location: '消防控制室',
      priority: 'low',
      status: 'completed',
      description: '消防报警主机例行检查，运行正常',
      creator: '李技师',
      assignee: '张工程师',
      createTime: '2024-01-15T14:20:00',
      processRecords: [
        {
          id: 1,
          action: '工单创建',
          description: '创建消防报警主机例行检查工单',
          operator: '李技师',
          time: '2024-01-15T14:20:00',
        },
        {
          id: 2,
          action: '开始处理',
          description: '开始检查消防报警主机',
          operator: '张工程师',
          time: '2024-01-15T14:30:00',
        },
        {
          id: 3,
          action: '检查完成',
          description: '消防报警主机运行正常',
          operator: '张工程师',
          time: '2024-01-15T15:45:00',
        },
        {
          id: 4,
          action: '工单完成',
          description: '设备运行正常，检查完成',
          operator: '张工程师',
          time: '2024-01-15T16:00:00',
        },
      ],
    },
    {
      id: 5,
      orderNumber: 'WO-2024-005',
      type: 'inspection',
      location: '3F网络中心',
      priority: 'low',
      status: 'completed',
      description: '网络设备例行巡检',
      creator: '赵工程师',
      assignee: '赵工程师',
      createTime: '2024-01-15T09:00:00',
      processRecords: [
        {
          id: 1,
          action: '工单创建',
          description: '创建网络设备巡检工单',
          operator: '赵工程师',
          time: '2024-01-15T09:00:00',
        },
        {
          id: 2,
          action: '开始处理',
          description: '开始网络设备巡检',
          operator: '赵工程师',
          time: '2024-01-15T09:15:00',
        },
        {
          id: 3,
          action: '工单完成',
          description: '网络设备运行正常，巡检完成',
          operator: '赵工程师',
          time: '2024-01-15T10:30:00',
        },
      ],
    },
  ]);

  // 弹窗状态
  const showWorkOrderDetail = ref(false);
  const selectedWorkOrder = ref(null);

  // 样式类方法
  const getWorkOrderTypeClass = (type) => {
    const classes = {
      fault: 'text-[#FF5252]',
      inspection: 'text-[#2196F3]',
      maintenance: 'text-[#FF9800]',
      emergency: 'text-[#F44336]',
    };
    return classes[type] || 'text-white/80';
  };

  const getWorkOrderTypeText = (type) => {
    const texts = {
      fault: '故障工单',
      inspection: '巡检工单',
      maintenance: '维护工单',
      emergency: '应急工单',
    };
    return texts[type] || type;
  };

  const getPriorityClass = (priority) => {
    const classes = {
      low: 'text-[#4CAF50]',
      medium: 'text-[#FF9800]',
      high: 'text-[#FF5722]',
      urgent: 'text-[#F44336]',
    };
    return classes[priority] || 'text-white/80';
  };

  const getPriorityText = (priority) => {
    const texts = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急',
    };
    return texts[priority] || priority;
  };

  const getWorkOrderStatusClass = (status) => {
    const classes = {
      pending: 'text-[#FF9800]',
      in_progress: 'text-[#2196F3]',
      completed: 'text-[#4CAF50]',
      cancelled: 'text-[#9E9E9E]',
    };
    return classes[status] || 'text-white/80';
  };

  const getWorkOrderStatusText = (status) => {
    const texts = {
      pending: '待处理',
      in_progress: '处理中',
      completed: '已完成',
      cancelled: '已取消',
    };
    return texts[status] || status;
  };

  // 时间格式化
  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-';
    const date = new Date(dateTime);
    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  };

  // 方法
  const viewWorkOrderDetail = (workOrder) => {
    selectedWorkOrder.value = workOrder;
    showWorkOrderDetail.value = true;
  };

  const showCreateWorkOrder = () => {
    // 创建新工单
    const newWorkOrder = {
      id: workOrders.length + 1,
      orderNumber: `WO-2024-${String(workOrders.length + 1).padStart(3, '0')}`,
      type: 'inspection',
      location: '1F机房',
      priority: 'low',
      status: 'completed',
      description: '设备例行巡检，运行正常',
      creator: '当前用户',
      assignee: '值班工程师',
      createTime: new Date().toISOString(),
      processRecords: [
        {
          id: 1,
          action: '工单创建',
          description: '创建设备例行巡检工单',
          operator: '当前用户',
          time: new Date().toISOString(),
        },
        {
          id: 2,
          action: '工单完成',
          description: '设备巡检完成，运行正常',
          operator: '值班工程师',
          time: new Date().toISOString(),
        },
      ],
    };

    workOrders.unshift(newWorkOrder);
    workOrderStats.total++;
    workOrderStats.completed++;

    console.log('创建工单成功:', newWorkOrder.orderNumber);
  };

  const showPrintTaskSheet = () => {
    // 打印任务单
    const printContent = generateTaskSheetContent();
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
    console.log('打印任务单');
  };

  const autoGenerateWorkOrders = () => {
    // 模拟根据巡检结果自动生成巡检工单
    const inspectionDevices = [
      { name: 'UPS电源系统', location: '配电室', task: '定期维护检查' },
      { name: '消防报警主机', location: '消防控制室', task: '例行功能检查' },
    ];

    inspectionDevices.forEach((device, index) => {
      const newWorkOrder = {
        id: workOrders.length + index + 1,
        orderNumber: `WO-2024-${String(workOrders.length + index + 1).padStart(3, '0')}`,
        type: 'inspection',
        deviceName: device.name,
        location: device.location,
        priority: 'low',
        status: 'completed',
        description: `${device.task}，设备运行正常`,
        creator: '系统自动',
        assignee: '值班工程师',
        createTime: new Date().toISOString(),
        processRecords: [
          {
            id: 1,
            action: '工单创建',
            description: `自动生成${device.name}巡检工单`,
            operator: '系统',
            time: new Date().toISOString(),
          },
          {
            id: 2,
            action: '工单完成',
            description: `${device.name}巡检完成，运行正常`,
            operator: '值班工程师',
            time: new Date().toISOString(),
          },
        ],
      };

      workOrders.unshift(newWorkOrder);
    });

    workOrderStats.total += inspectionDevices.length;
    workOrderStats.completed += inspectionDevices.length;

    console.log(`自动生成${inspectionDevices.length}个巡检工单`);
  };

  // 生成任务单打印内容
  const generateTaskSheetContent = () => {
    const currentDate = new Date().toLocaleDateString('zh-CN');
    const currentTime = new Date().toLocaleTimeString('zh-CN');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>巡检任务单</title>
        <style>
          body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          .info-table td { padding: 8px; border: 1px solid #ccc; }
          .label { background-color: #f5f5f5; font-weight: bold; width: 120px; }
          .task-list { margin-top: 20px; }
          .task-item { margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; }
          .signature { margin-top: 40px; display: flex; justify-content: space-between; }
          .signature div { width: 200px; text-align: center; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="title">数据中心巡检任务单</div>
          <div>打印时间：${currentDate} ${currentTime}</div>
        </div>

        <table class="info-table">
          <tr>
            <td class="label">任务编号</td>
            <td>TASK-${Date.now()}</td>
            <td class="label">执行日期</td>
            <td>${currentDate}</td>
          </tr>
          <tr>
            <td class="label">执行人员</td>
            <td>待指定</td>
            <td class="label">预计用时</td>
            <td>2小时</td>
          </tr>
        </table>

        <div class="task-list">
          <h3>巡检项目清单</h3>
          <div class="task-item">
            <strong>1. 空调系统检查</strong><br>
            检查内容：温度、湿度、运行状态<br>
            检查位置：1F机房、2F机房<br>
            完成情况：□ 正常 □ 异常 □ 需维修
          </div>
          <div class="task-item">
            <strong>2. 电力系统检查</strong><br>
            检查内容：UPS状态、配电柜、应急电源<br>
            检查位置：配电室<br>
            完成情况：□ 正常 □ 异常 □ 需维修
          </div>
          <div class="task-item">
            <strong>3. 消防系统检查</strong><br>
            检查内容：报警器、灭火器、疏散通道<br>
            检查位置：各楼层<br>
            完成情况：□ 正常 □ 异常 □ 需维修
          </div>
          <div class="task-item">
            <strong>4. 网络设备检查</strong><br>
            检查内容：交换机、路由器、服务器<br>
            检查位置：3F网络中心<br>
            完成情况：□ 正常 □ 异常 □ 需维修
          </div>
        </div>

        <div class="signature">
          <div>
            执行人签名：<br><br>
            _______________<br>
            日期：${currentDate}
          </div>
          <div>
            检查人签名：<br><br>
            _______________<br>
            日期：${currentDate}
          </div>
        </div>
      </body>
      </html>
    `;
  };

  // 生成工单打印内容
  const generateWorkOrderContent = (workOrder) => {
    const currentDate = new Date().toLocaleDateString('zh-CN');
    const currentTime = new Date().toLocaleTimeString('zh-CN');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>工单详情 - ${workOrder.orderNumber}</title>
        <style>
          body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          .info-table td { padding: 8px; border: 1px solid #ccc; }
          .label { background-color: #f5f5f5; font-weight: bold; width: 120px; }
          .description { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
          .records { margin-top: 20px; }
          .record-item { margin-bottom: 10px; padding: 10px; border-left: 3px solid #007bff; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="title">数据中心工单</div>
          <div>打印时间：${currentDate} ${currentTime}</div>
        </div>

        <table class="info-table">
          <tr>
            <td class="label">工单编号</td>
            <td>${workOrder.orderNumber}</td>
            <td class="label">工单类型</td>
            <td>${getWorkOrderTypeText(workOrder.type)}</td>
          </tr>
          <tr>
            <td class="label">优先级</td>
            <td>${getPriorityText(workOrder.priority)}</td>
            <td class="label">当前状态</td>
            <td>${getWorkOrderStatusText(workOrder.status)}</td>
          </tr>
          <tr>
            <td class="label">设备名称</td>
            <td>${workOrder.deviceName || '无'}</td>
            <td class="label">位置</td>
            <td>${workOrder.location}</td>
          </tr>
          <tr>
            <td class="label">创建人</td>
            <td>${workOrder.creator}</td>
            <td class="label">处理人</td>
            <td>${workOrder.assignee || '未分配'}</td>
          </tr>
          <tr>
            <td class="label">创建时间</td>
            <td colspan="3">${formatDateTime(workOrder.createTime)}</td>
          </tr>
        </table>

        <div class="description">
          <strong>问题描述：</strong><br>
          ${workOrder.description}
        </div>

        <div class="records">
          <h3>处理记录</h3>
          ${workOrder.processRecords
            .map(
              (record) => `
            <div class="record-item">
              <strong>${record.action}</strong> - ${formatDateTime(record.time)}<br>
              ${record.description}<br>
              <small>操作人：${record.operator}</small>
            </div>
          `
            )
            .join('')}
        </div>
      </body>
      </html>
    `;
  };

  const printWorkOrder = (workOrder) => {
    const printContent = generateWorkOrderContent(workOrder);
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
    console.log('打印工单:', workOrder.orderNumber);
  };

  const processWorkOrder = (workOrder) => {
    workOrder.status = 'in_progress';
    workOrder.processRecords.push({
      id: workOrder.processRecords.length + 1,
      action: '开始处理',
      description: '开始处理工单',
      operator: '当前用户',
      time: new Date().toISOString(),
    });

    // 更新统计数据
    workOrderStats.pending--;

    console.log('开始处理工单:', workOrder.orderNumber);
  };
</script>
