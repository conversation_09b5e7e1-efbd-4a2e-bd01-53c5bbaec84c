<template>
  <div ref="chartRef" class="w-full h-full"></div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import * as echarts from 'echarts';
  import { getWaterMeterHistory } from '/@/api/energy/water';

  const chartRef = ref<HTMLElement | null>(null);
  let chartInstance: echarts.ECharts | null = null;

  const renderChart = (data: Record<string, any[]>) => {
    if (!chartInstance) return;

    const allDates = new Set<string>();
    Object.values(data).forEach((meterData) => {
      meterData.forEach((record) => {
        if (record.dataTime) {
          allDates.add(record.dataTime.split(' ')[0]);
        }
      });
    });

    const sortedDates = Array.from(allDates).sort();
    const series = Object.entries(data).map(([meterName, meterData]) => {
      const meterDataMap = new Map<string, number>();
      meterData.forEach((record) => {
        const date = record.dataTime.split(' ')[0];
        meterDataMap.set(date, parseFloat(record.valueData) || 0);
      });

      return {
        name: meterName,
        type: 'line',
        data: sortedDates.map((date) => meterDataMap.get(date) || 0),
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { width: 2 },
        areaStyle: { opacity: 0.2 },
      };
    });

    chartInstance.setOption({
      tooltip: { trigger: 'axis', backgroundColor: 'rgba(0,0,0,0.7)', textStyle: { color: '#fff' } },
      legend: { show: true, top: '5%', textStyle: { color: '#ccc' } },
      grid: { top: '15%', left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: { type: 'category', data: sortedDates, axisLabel: { color: '#ccc' } },
      yAxis: { type: 'value', name: '吨', axisLabel: { color: '#ccc' } },
      series,
    });
  };

  const loadData = async () => {
    const result = await getWaterMeterHistory();
    if (result && typeof result === 'object') {
      renderChart(result);
    }
  };

  onMounted(() => {
    chartInstance = echarts.init(chartRef.value!);
    loadData();
  });

  onUnmounted(() => {
    chartInstance?.dispose();
  });
</script>
