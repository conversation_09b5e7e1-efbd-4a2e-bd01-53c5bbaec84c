import * as THREE from 'three';

/**
 * 透视效果着色器
 * 使用自定义着色器实现高效的透视效果，支持边缘增强
 */
export const TransparencyShader = {
  // 顶点着色器
  vertexShader: `
    // 传递给片元着色器的变量
    varying vec3 vNormal;
    varying vec3 vViewPosition;

    void main() {
      // 计算法线和视图位置（用于边缘检测）
      vNormal = normalize(normalMatrix * normal);
      vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
      vViewPosition = -mvPosition.xyz;

      // 标准顶点变换
      gl_Position = projectionMatrix * mvPosition;
    }
  `,

  // 片元着色器
  fragmentShader: `
    uniform vec3 baseColor;
    uniform vec3 edgeColor;
    uniform float opacity;
    uniform float edgeStrength;
    uniform float transparencyEnabled;

    varying vec3 vNormal;
    varying vec3 vViewPosition;

    void main() {
      // 基本颜色
      vec3 color = baseColor;

      // 计算边缘因子（基于法线和视图方向）
      vec3 viewDir = normalize(vViewPosition);
      float edgeFactor = 1.0 - abs(dot(vNormal, viewDir));

      // 增强边缘
      edgeFactor = pow(edgeFactor, 3.0) * edgeStrength;
      color = mix(color, edgeColor, edgeFactor);

      // 应用透明度
      float finalOpacity = opacity;

      // 如果透视模式未启用，则所有对象不透明
      finalOpacity = mix(1.0, finalOpacity, transparencyEnabled);

      // 输出最终颜色
      gl_FragColor = vec4(color, finalOpacity);
    }
  `,

  // 着色器统一变量
  uniforms: {
    baseColor: { value: new THREE.Color(0x88a8c8) }, // 更柔和的蓝灰色
    edgeColor: { value: new THREE.Color(0x6a8cad) }, // 更柔和的边缘颜色
    opacity: { value: 0.15 },
    edgeStrength: { value: 1.2 },
    transparencyEnabled: { value: 0.0 },
  },
};

/**
 * 创建透视效果材质
 * @returns 自定义着色器材质
 */
export function createTransparencyMaterial(): THREE.ShaderMaterial {
  return new THREE.ShaderMaterial({
    vertexShader: TransparencyShader.vertexShader,
    fragmentShader: TransparencyShader.fragmentShader,
    uniforms: THREE.UniformsUtils.clone(TransparencyShader.uniforms),
    transparent: true,
    side: THREE.DoubleSide,
    depthWrite: false,
  });
}

/**
 * 预处理模型，标记设备和非设备模型
 * @param scene 场景对象
 */
export function preprocessModelForShader(scene: THREE.Scene): void {
  if (!scene) {
    console.error('preprocessModelForShader: 场景对象为空');
    return;
  }

  const isFloorDevice = (name: string): boolean => {
    if (!name) return false;
    return /[1-4]F/.test(name);
  };

  let deviceCount = 0;
  let nonDeviceCount = 0;
  let errorCount = 0;

  // 遍历场景中的所有网格
  scene.traverse((object) => {
    if (!(object instanceof THREE.Mesh)) return;
    if (!object.geometry) return;
    if (object.userData.shaderPreprocessed) return; // 避免重复处理

    try {
      // 判断是否为设备
      const isEquipment = isFloorDevice(object.name) || (object.parent && isFloorDevice(object.parent.name));

      // 在userData中标记是否为设备
      object.userData.isEquipment = isEquipment;

      // 标记已处理
      object.userData.shaderPreprocessed = true;

      // 统计设备和非设备模型数量
      if (isEquipment) {
        deviceCount++;
      } else {
        nonDeviceCount++;
      }
    } catch (error) {
      console.error(`预处理网格失败 (${object.name}):`, error);
      errorCount++;
    }
  });

  console.log(`[TransparencyShader] 预处理完成: 设备模型 ${deviceCount} 个, 非设备模型 ${nonDeviceCount} 个, 失败 ${errorCount} 个`);
}

/**
 * 应用透视效果材质到场景
 * @param scene 场景对象
 * @param material 透视效果材质
 */
export function applyTransparencyMaterial(scene: THREE.Scene, material: THREE.ShaderMaterial): void {
  if (!scene) {
    console.error('applyTransparencyMaterial: 场景对象为空');
    return;
  }

  if (!material) {
    console.error('applyTransparencyMaterial: 材质对象为空');
    return;
  }

  let appliedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;

  // 设备识别函数
  const isFloorDevice = (name: string): boolean => {
    if (!name) return false;
    return /[1-4]F/.test(name);
  };

  // 保存原始材质
  scene.traverse((object) => {
    if (!(object instanceof THREE.Mesh)) return;

    try {
      // 判断是否为设备
      const isEquipment = isFloorDevice(object.name) || (object.parent && isFloorDevice(object.parent.name));

      // 保存原始材质（如果尚未保存）
      if (!object.userData.originalMaterial) {
        object.userData.originalMaterial = object.material;
      }

      // 只对非设备模型应用透视材质
      if (!isEquipment) {
        // 应用透视材质
        object.material = material;
        appliedCount++;
      } else {
        // 设备模型保持原始材质不变
        skippedCount++;
      }
    } catch (error) {
      console.error(`应用材质失败 (${object.name}):`, error);
      errorCount++;
    }
  });

  console.log(`[TransparencyShader] 应用材质完成: 处理了 ${appliedCount} 个网格, 跳过 ${skippedCount} 个设备模型, 失败 ${errorCount} 个`);
}

/**
 * 恢复场景原始材质
 * @param scene 场景对象
 */
export function restoreOriginalMaterials(scene: THREE.Scene): void {
  if (!scene) {
    console.error('restoreOriginalMaterials: 场景对象为空');
    return;
  }

  let restoredCount = 0;
  let errorCount = 0;

  scene.traverse((object) => {
    if (!(object instanceof THREE.Mesh)) return;

    try {
      if (object.userData.originalMaterial) {
        // 保存当前材质的引用，以便可以安全地替换
        const currentMaterial = object.material;

        // 恢复原始材质
        object.material = object.userData.originalMaterial;

        // 清除引用，避免内存泄漏
        object.userData.originalMaterial = null;

        // 如果当前材质不是原始材质，则释放它
        if (currentMaterial !== object.material) {
          // 注意：不要释放共享材质，只记录恢复计数
        }

        restoredCount++;
      }
    } catch (error) {
      console.error(`恢复材质失败 (${object.name}):`, error);
      errorCount++;
    }
  });

  console.log(`[TransparencyShader] 恢复材质完成: 处理了 ${restoredCount} 个网格, 失败 ${errorCount} 个`);
}

/**
 * 设置透视效果启用状态
 * @param material 透视效果材质
 * @param enabled 是否启用
 */
export function setTransparencyEnabled(material: THREE.ShaderMaterial, enabled: boolean): void {
  material.uniforms.transparencyEnabled.value = enabled ? 1.0 : 0.0;
}

/**
 * 立即设置透明度状态，无过渡动画
 * @param material 透视效果材质
 * @param enabled 是否启用
 * @param duration 不再使用，保留参数以兼容现有代码
 * @param onUpdate 更新回调
 * @param onComplete 完成回调
 */
export function animateTransparency(
  material: THREE.ShaderMaterial,
  enabled: boolean,
  _duration?: number, // 不再使用，使用下划线前缀避免未使用警告
  onUpdate?: () => void,
  onComplete?: () => void
): void {
  // 立即设置最终值
  material.uniforms.transparencyEnabled.value = enabled ? 1.0 : 0.0;

  // 立即调用更新回调
  if (onUpdate) onUpdate();

  // 立即调用完成回调
  if (onComplete) {
    // 使用setTimeout确保在下一帧调用完成回调
    // 这样可以确保渲染更新已经应用
    setTimeout(() => {
      onComplete();
    }, 0);
  }
}
