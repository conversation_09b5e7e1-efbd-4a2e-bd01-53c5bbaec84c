<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <template #toolbar> </template>
    </BasicTable>
  </div>
</template>

<script setup lang="ts">
  import { BasicTable, useTable } from '/@/components/Table';
  import { getBasicColumns, getFormConfig } from './data';
  import { getRealtimeData } from '/@/api/environment';

  const [registerTable] = useTable({
    title: '实时数据',
    api: getRealtimeData,
    columns: getBasicColumns(),
    formConfig: getFormConfig(),
    useSearchForm: true,
    showTableSetting: true,
    showIndexColumn: true,
    canResize: true,
    autoCreateKey: true,
  });
</script>
