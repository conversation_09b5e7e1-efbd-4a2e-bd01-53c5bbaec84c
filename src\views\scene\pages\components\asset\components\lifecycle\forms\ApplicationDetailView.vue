<template>
  <div class="p-[1vw] space-y-[1vw]">
    <!-- 申请基本信息 -->
    <div class="bg-blue-500/10 border border-blue-500/20 rounded p-[0.8vw]">
      <h3 class="text-[0.8vw] text-blue-400 font-semibold mb-[0.6vw] flex items-center">
        <span class="mr-[0.4vw]">📋</span>
        申请编号：{{ application?.applicationCode }}
      </h3>
      <div class="grid grid-cols-3 gap-[0.8vw] text-[0.6vw]">
        <div
          ><span class="text-gray-400">申请人：</span><span class="text-white">{{ application?.applicant }}</span></div
        >
        <div
          ><span class="text-gray-400">所属部门：</span><span class="text-white">{{ application?.department }}</span></div
        >
        <div
          ><span class="text-gray-400">申请状态：</span>
          <span
            :class="[
              application?.status === 'approved'
                ? 'text-green-400'
                : application?.status === 'pending'
                  ? 'text-yellow-400'
                  : application?.status === 'allocated'
                    ? 'text-blue-400'
                    : 'text-red-400',
            ]"
            >{{ getStatusText(application?.status) }}</span
          >
        </div>
      </div>
    </div>

    <!-- 详细信息 -->
    <div class="grid grid-cols-2 gap-[1vw]">
      <!-- 申请信息 -->
      <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
        <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw] flex items-center">
          <span class="mr-[0.3vw]">📝</span>
          申请信息
        </h4>
        <div class="space-y-[0.4vw] text-[0.6vw]">
          <div class="flex justify-between">
            <span class="text-gray-400">资产类型：</span>
            <span class="text-white">{{ application?.assetType }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">申请数量：</span>
            <span class="text-white">{{ application?.quantity }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">申请时间：</span>
            <span class="text-white">{{ application?.applicationDate }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">期望时间：</span>
            <span class="text-white">{{ application?.expectedDate || '无特殊要求' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">紧急程度：</span>
            <span
              :class="[application?.urgency === 'high' ? 'text-red-400' : application?.urgency === 'medium' ? 'text-yellow-400' : 'text-green-400']"
              >{{ getUrgencyText(application?.urgency) }}</span
            >
          </div>
        </div>
      </div>

      <!-- 联系信息 -->
      <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
        <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw] flex items-center">
          <span class="mr-[0.3vw]">📞</span>
          联系信息
        </h4>
        <div class="space-y-[0.4vw] text-[0.6vw]">
          <div class="flex justify-between">
            <span class="text-gray-400">联系电话：</span>
            <span class="text-white">{{ application?.phone || '138****8888' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">邮箱地址：</span>
            <span class="text-white">{{ application?.email || '<EMAIL>' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">办公地点：</span>
            <span class="text-white">{{ application?.office || '总部大楼5F' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">工号：</span>
            <span class="text-white">{{ application?.employeeId || 'EMP001' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请原因 -->
    <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
      <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw] flex items-center">
        <span class="mr-[0.3vw]">💭</span>
        申请原因
      </h4>
      <div class="text-[0.6vw] text-gray-300 leading-relaxed">
        {{ application?.reason || '新员工入职，需要配置办公设备以便开展日常工作。' }}
      </div>
    </div>

    <!-- 规格要求 -->
    <div v-if="application?.specifications" class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
      <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw] flex items-center">
        <span class="mr-[0.3vw]">⚙️</span>
        规格要求
      </h4>
      <div class="text-[0.6vw] text-gray-300 leading-relaxed">
        {{ application?.specifications }}
      </div>
    </div>

    <!-- 审批流程 -->
    <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
      <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw] flex items-center">
        <span class="mr-[0.3vw]">🔄</span>
        审批流程
      </h4>
      <div class="space-y-[0.6vw]">
        <!-- 流程图 -->
        <div class="flex items-center justify-between">
          <div
            v-for="(step, index) in approvalSteps"
            :key="step.key"
            class="flex items-center"
            :class="index < approvalSteps.length - 1 ? 'flex-1' : ''"
          >
            <div class="flex items-center space-x-[0.4vw]">
              <div
                :class="['w-[0.8vw] h-[0.8vw] rounded-full', step.completed ? 'bg-green-500' : step.current ? 'bg-blue-500' : 'bg-gray-600']"
              ></div>
              <span :class="['text-[0.6vw]', step.completed ? 'text-green-400' : step.current ? 'text-blue-400' : 'text-gray-400']">
                {{ step.label }}
              </span>
            </div>
            <div
              v-if="index < approvalSteps.length - 1"
              :class="['flex-1 h-[0.1vw] mx-[0.8vw]', step.completed ? 'bg-green-500' : 'bg-gray-600']"
            ></div>
          </div>
        </div>

        <!-- 审批记录 -->
        <div class="space-y-[0.4vw]">
          <div v-for="record in approvalRecords" :key="record.id" class="flex justify-between items-center p-[0.4vw] bg-white/5 rounded">
            <div>
              <div class="text-[0.5vw] text-white">{{ record.step }}</div>
              <div class="text-[0.4vw] text-gray-400">{{ record.date }} - {{ record.approver }}</div>
            </div>
            <div class="text-right">
              <span
                :class="[
                  'px-[0.3vw] py-[0.1vw] rounded text-[0.4vw]',
                  record.result === 'approved'
                    ? 'bg-green-500/20 text-green-400'
                    : record.result === 'rejected'
                      ? 'bg-red-500/20 text-red-400'
                      : 'bg-yellow-500/20 text-yellow-400',
                ]"
              >
                {{ record.result === 'approved' ? '已批准' : record.result === 'rejected' ? '已拒绝' : '待处理' }}
              </span>
              <div v-if="record.comments" class="text-[0.4vw] text-gray-400 mt-[0.2vw]">
                {{ record.comments }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预算信息 -->
    <div v-if="application?.budget" class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
      <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw] flex items-center">
        <span class="mr-[0.3vw]">💰</span>
        预算信息
      </h4>
      <div class="grid grid-cols-2 gap-[0.8vw] text-[0.6vw]">
        <div class="flex justify-between">
          <span class="text-gray-400">预算金额：</span>
          <span class="text-white">¥{{ application?.budget?.toLocaleString() }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-400">成本中心：</span>
          <span class="text-white">{{ application?.costCenter || 'CC-IT-001' }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-[0.6vw] pt-[0.6vw] border-t border-white/10">
      <button
        v-if="application?.status === 'pending'"
        class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
        @click="approveApplication"
      >
        审批申请
      </button>
      <button
        v-if="application?.status === 'approved'"
        class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
        @click="allocateAssets"
      >
        分配资产
      </button>
      <button
        class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors"
        @click="printApplication"
      >
        打印申请
      </button>
      <button
        class="px-[0.8vw] py-[0.4vw] bg-purple-500 text-white text-[0.6vw] rounded hover:bg-purple-600 transition-colors"
        @click="exportApplication"
      >
        导出详情
      </button>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  const props = defineProps({
    application: { type: Object, default: null },
  });

  // 审批步骤
  const approvalSteps = computed(() => {
    const status = props.application?.status;
    return [
      { key: 'submit', label: '提交申请', completed: true, current: false },
      { key: 'dept', label: '部门审批', completed: status !== 'pending', current: status === 'pending' },
      { key: 'it', label: 'IT审批', completed: ['approved', 'allocated'].includes(status), current: false },
      { key: 'allocate', label: '资产分配', completed: status === 'allocated', current: status === 'approved' },
    ];
  });

  // 审批记录
  const approvalRecords = ref([
    {
      id: 1,
      step: '申请提交',
      date: '2024-01-15 09:30',
      approver: props.application?.applicant || '张三',
      result: 'approved',
      comments: '申请已提交，等待审批',
    },
    {
      id: 2,
      step: '部门主管审批',
      date: '2024-01-15 14:20',
      approver: '李主管',
      result: props.application?.status === 'pending' ? 'pending' : 'approved',
      comments: props.application?.status === 'pending' ? '等待审批' : '同意申请，设备确实需要',
    },
  ]);

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      pending: '待审批',
      approved: '已批准',
      rejected: '已拒绝',
      allocated: '已分配',
    };
    return statusMap[status] || status;
  };

  const getUrgencyText = (urgency) => {
    const urgencyMap = {
      low: '一般',
      medium: '紧急',
      high: '非常紧急',
    };
    return urgencyMap[urgency] || '一般';
  };

  const approveApplication = () => {
    alert('跳转到审批页面');
  };

  const allocateAssets = () => {
    alert('跳转到资产分配页面');
  };

  const printApplication = () => {
    alert('正在生成打印文件...');
  };

  const exportApplication = () => {
    alert('导出申请详情');
  };
</script>
