<template>
  <div class="w-full h-full relative">
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center z-10 bg-transparent">
      <span class="text-white text-base">加载中...</span>
    </div>
    <div ref="chartRef" class="w-full h-full" :class="{ 'opacity-40 pointer-events-none': loading }"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { debounce } from 'lodash-es';
  import * as echarts from 'echarts';
  import { getElectricityHistory } from '/@/api/energy/electricity';
  import { message } from 'ant-design-vue';

  const chartRef = ref<HTMLElement | null>(null);
  let chartInstance: echarts.ECharts | null = null;
  const loading = ref(false);

  const renderChart = (data: Record<string, any[]>) => {
    if (!chartInstance) return;

    const allDates = new Set<string>();
    Object.values(data).forEach((meterData) => {
      meterData.forEach((record) => {
        if (record.dataTime) {
          allDates.add(record.dataTime.split(' ')[0]);
        }
      });
    });

    const sortedDates = Array.from(allDates).sort();
    const series: any[] = [];

    Object.entries(data).forEach(([meterName, meterData]) => {
      const meterDataMap = new Map<string, number>();
      // 简化设备名称
      const simplifiedName =
        meterName
          .replace(/^\d+-\d+-[A-Z0-9]+-/, '') // 移除前缀格式如 "02040011-52001-B1-"
          .replace(/［.*?］/g, '') // 移除［xxx］格式的内容
          .replace(/\(.*?\)/g, '') // 移除括号内容
          .split('｜')
          .pop() || meterName; // 获取｜后面的内容，如果没有则保持原名

      meterData.forEach((record) => {
        const date = record.dataTime.split(' ')[0];
        const value = parseFloat(record.valueData) || 0;
        meterDataMap.set(date, value);
      });

      const lineData = sortedDates.map((date) => meterDataMap.get(date) || 0);

      series.push({
        name: simplifiedName,
        type: 'line',
        data: lineData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2,
        },
        areaStyle: {
          opacity: 0.2,
        },
      });
    });

    const option = {
      tooltip: { show: false },
      legend: {
        show: true,
        top: 2,
        itemWidth: 12,
        itemHeight: 8,
        textStyle: {
          color: '#ccc',
          fontSize: 9,
          lineHeight: 10,
        },
        itemGap: 8,
        padding: [0, 10],
      },
      grid: {
        top: '40%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: sortedDates,
        axisLabel: {
          color: '#ccc',
          fontSize: 10,
          interval: Math.floor(sortedDates.length / 8),
          hideOverlap: true,
        },
        axisLine: {
          lineStyle: { color: '#555' },
        },
      },
      yAxis: {
        type: 'value',
        name: 'kWh',
        nameTextStyle: {
          color: '#ccc',
          fontSize: 10,
          padding: [0, 0, 0, -30],
        },
        axisLabel: {
          color: '#ccc',
          fontSize: 10,
        },
        splitLine: {
          lineStyle: {
            color: '#333',
            type: 'dashed',
          },
        },
      },
      series,
      color: ['#F39C12', '#FAD7A0', '#B9770E'],
    };

    chartInstance.setOption(option);
  };

  const initChart = () => {
    if (chartRef.value) {
      chartInstance = echarts.init(chartRef.value);
      window.addEventListener('resize', handleResize);
    }
  };

  const handleResize = debounce(() => {
    chartInstance?.resize();
  }, 300);

  const loadData = async () => {
    if (loading.value) return;
    loading.value = true;
    try {
      const result = await getElectricityHistory();
      if (result && typeof result === 'object') {
        renderChart(result);
      } else {
        throw new Error('数据格式错误');
      }
    } catch (error) {
      console.error('电能历史数据加载错误:', error);
      message.error('加载电能历史数据失败');
      chartInstance?.clear();
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    initChart();
    loadData();
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    chartInstance?.dispose();
    chartInstance = null;
  });
</script>
