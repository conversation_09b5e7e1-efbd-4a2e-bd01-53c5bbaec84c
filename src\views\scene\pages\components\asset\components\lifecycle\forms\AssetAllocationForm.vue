<template>
  <div class="p-[1vw] space-y-[1vw]">
    <!-- 申请信息展示 -->
    <div class="bg-blue-500/10 border border-blue-500/20 rounded p-[0.8vw]">
      <h3 class="text-[0.7vw] text-blue-400 font-semibold mb-[0.4vw]">申请信息</h3>
      <div class="grid grid-cols-2 gap-[0.8vw] text-[0.6vw]">
        <div
          ><span class="text-gray-400">申请人：</span><span class="text-white">{{ application?.applicant }}</span></div
        >
        <div
          ><span class="text-gray-400">部门：</span><span class="text-white">{{ application?.department }}</span></div
        >
        <div
          ><span class="text-gray-400">资产类型：</span><span class="text-white">{{ application?.assetType }}</span></div
        >
        <div
          ><span class="text-gray-400">申请数量：</span><span class="text-white">{{ application?.quantity }}</span></div
        >
      </div>
    </div>

    <!-- 可分配资产列表 -->
    <div>
      <h3 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">可分配资产</h3>
      <div class="bg-black/20 rounded border border-white/10 max-h-[15vw] overflow-auto">
        <table class="w-full text-[0.6vw]">
          <thead class="bg-blue-500/20 sticky top-0">
            <tr>
              <th class="text-left p-[0.4vw] text-gray-300 border-b border-white/10">
                <input type="checkbox" @change="toggleSelectAll" />
              </th>
              <th class="text-left p-[0.4vw] text-gray-300 border-b border-white/10">资产编号</th>
              <th class="text-left p-[0.4vw] text-gray-300 border-b border-white/10">资产名称</th>
              <th class="text-left p-[0.4vw] text-gray-300 border-b border-white/10">型号</th>
              <th class="text-left p-[0.4vw] text-gray-300 border-b border-white/10">状态</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="asset in availableAssets" :key="asset.id" class="hover:bg-white/5">
              <td class="p-[0.4vw] border-b border-white/5">
                <input type="checkbox" :checked="formData.selectedAssets?.includes(asset.id)" @change="toggleAssetSelection(asset.id)" />
              </td>
              <td class="p-[0.4vw] text-white border-b border-white/5">{{ asset.code }}</td>
              <td class="p-[0.4vw] text-white border-b border-white/5">{{ asset.name }}</td>
              <td class="p-[0.4vw] text-gray-300 border-b border-white/5">{{ asset.model }}</td>
              <td class="p-[0.4vw] border-b border-white/5">
                <span class="px-[0.3vw] py-[0.1vw] rounded text-[0.5vw] bg-green-500/20 text-green-400"> 可用 </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分配详情 -->
    <div class="grid grid-cols-2 gap-[1vw]">
      <div>
        <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">分配给</label>
        <input
          v-model="formData.assignTo"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          :placeholder="application?.applicant"
          readonly
        />
      </div>

      <div>
        <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">分配日期</label>
        <input
          v-model="formData.allocateDate"
          type="date"
          class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
        />
      </div>
    </div>

    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">分配备注</label>
      <textarea
        v-model="formData.notes"
        rows="3"
        class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400 resize-none"
        placeholder="请输入分配备注"
      ></textarea>
    </div>

    <!-- 已选择的资产 -->
    <div v-if="formData.selectedAssets?.length > 0">
      <h3 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]"> 已选择资产 ({{ formData.selectedAssets.length }}) </h3>
      <div class="flex flex-wrap gap-[0.4vw]">
        <span
          v-for="assetId in formData.selectedAssets"
          :key="assetId"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500/20 text-blue-400 rounded text-[0.5vw]"
        >
          {{ getAssetName(assetId) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch, computed } from 'vue';

  const props = defineProps({
    formData: { type: Object, default: () => ({}) },
    application: { type: Object, default: null },
  });

  const emit = defineEmits(['update:formData']);

  const formData = ref({
    selectedAssets: [],
    assignTo: props.application?.applicant || '',
    allocateDate: new Date().toISOString().split('T')[0],
    notes: '',
    ...props.formData,
  });

  // 模拟可分配资产数据
  const availableAssets = ref([
    { id: 1, code: 'IT-2024-001', name: 'Dell笔记本电脑', model: 'Latitude 5520', status: 'available' },
    { id: 2, code: 'IT-2024-002', name: 'HP笔记本电脑', model: 'EliteBook 840', status: 'available' },
    { id: 3, code: 'IT-2024-003', name: 'Lenovo台式机', model: 'ThinkCentre M720', status: 'available' },
    { id: 4, code: 'IT-2024-004', name: 'Dell显示器', model: 'U2419H', status: 'available' },
    { id: 5, code: 'IT-2024-005', name: 'HP打印机', model: 'LaserJet Pro', status: 'available' },
  ]);

  watch(
    formData,
    (newValue) => {
      emit('update:formData', newValue);
    },
    { deep: true }
  );

  const toggleSelectAll = (event) => {
    if (event.target.checked) {
      formData.value.selectedAssets = availableAssets.value.map((asset) => asset.id);
    } else {
      formData.value.selectedAssets = [];
    }
  };

  const toggleAssetSelection = (assetId) => {
    const index = formData.value.selectedAssets.indexOf(assetId);
    if (index > -1) {
      formData.value.selectedAssets.splice(index, 1);
    } else {
      formData.value.selectedAssets.push(assetId);
    }
  };

  const getAssetName = (assetId) => {
    const asset = availableAssets.value.find((a) => a.id === assetId);
    return asset ? asset.name : '';
  };
</script>
