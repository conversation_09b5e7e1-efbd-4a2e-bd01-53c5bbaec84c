import * as THREE from 'three';
import { throttle } from 'lodash-es';

// 透明状态缓存接口
interface TransparencyState {
  isTransparent: boolean;
  originalMaterials: Map<string, THREE.Material | THREE.Material[]>;
  edgeMeshes: Map<string, THREE.LineSegments>;
  lastAppliedTime: number;
}

// 透明效果配置
interface TransparencyConfig {
  opacity: number;
  color: THREE.Color;
  emissiveIntensity: number;
  edgeColor: THREE.Color;
  edgeOpacity: number;
  transitionDuration: number;
  batchSize: number;
  maxBatchSize: number;
  minBatchSize: number;
  useInstancing: boolean;
  useSharedMaterials: boolean;
  useTransition: boolean;
}

/**
 * 透明效果管理器 - 负责高效处理大量网格的透明效果
 */
export class TransparencyManager {
  private static instance: TransparencyManager | null = null;
  
  // 状态缓存
  private state: TransparencyState = {
    isTransparent: false,
    originalMaterials: new Map(),
    edgeMeshes: new Map(),
    lastAppliedTime: 0
  };
  
  // 共享材质
  private transparentMaterial: THREE.MeshPhongMaterial;
  private edgeMaterial: THREE.LineBasicMaterial;
  
  // 材质实例缓存 - 用于不同透明度的过渡效果
  private materialInstances: Map<number, THREE.MeshPhongMaterial> = new Map();
  
  // 处理队列
  private processingQueue: THREE.Mesh[] = [];
  private isProcessing: boolean = false;
  private processingStartTime: number = 0;
  
  // 配置
  private config: TransparencyConfig = {
    opacity: 0.2,
    color: new THREE.Color(0x157dec),
    emissiveIntensity: 0.2,
    edgeColor: new THREE.Color(0x3498db),
    edgeOpacity: 0.6,
    transitionDuration: 300, // 过渡动画时长(毫秒)
    batchSize: 100,          // 初始批处理大小
    maxBatchSize: 500,       // 最大批处理大小
    minBatchSize: 50,        // 最小批处理大小
    useInstancing: true,     // 是否使用实例化渲染
    useSharedMaterials: true, // 是否使用共享材质
    useTransition: true      // 是否使用过渡动画
  };
  
  // 性能监控
  private performanceMetrics = {
    lastBatchTime: 0,
    averageBatchTime: 0,
    batchesProcessed: 0,
    totalItemsProcessed: 0,
    startTime: 0
  };
  
  // 回调函数
  private onProgressCallback: ((progress: number) => void) | null = null;
  private onCompleteCallback: (() => void) | null = null;
  
  // 渲染更新请求
  private requestRenderUpdate: () => void;

  private constructor(renderUpdateFn: () => void) {
    // 初始化共享材质
    this.transparentMaterial = new THREE.MeshPhongMaterial({
      color: this.config.color,
      transparent: true,
      opacity: this.config.opacity,
      emissive: this.config.color,
      emissiveIntensity: this.config.emissiveIntensity,
      side: THREE.DoubleSide,
      depthWrite: false,
      polygonOffset: true,
      polygonOffsetFactor: 1,
      polygonOffsetUnits: 1,
    });
    
    this.edgeMaterial = new THREE.LineBasicMaterial({
      color: this.config.edgeColor,
      transparent: true,
      opacity: this.config.edgeOpacity,
      depthWrite: false,
      depthTest: false,
    });
    
    this.requestRenderUpdate = renderUpdateFn || (() => {});
  }

  /**
   * 获取单例实例
   */
  public static getInstance(renderUpdateFn?: () => void): TransparencyManager {
    if (!TransparencyManager.instance) {
      TransparencyManager.instance = new TransparencyManager(renderUpdateFn);
    } else if (renderUpdateFn) {
      TransparencyManager.instance.requestRenderUpdate = renderUpdateFn;
    }
    return TransparencyManager.instance;
  }

  /**
   * 应用透明效果到网格集合
   * @param meshes 要处理的网格数组
   * @param makeTransparent 是否应用透明效果
   * @returns Promise 处理完成后的Promise
   */
  public async applyTransparency(meshes: THREE.Mesh[], makeTransparent: boolean): Promise<void> {
    if (!meshes || meshes.length === 0) return Promise.resolve();
    
    // 如果当前正在处理，先取消
    if (this.isProcessing) {
      this.cancelProcessing();
    }
    
    // 更新状态
    this.state.isTransparent = makeTransparent;
    this.state.lastAppliedTime = Date.now();
    
    // 重置性能指标
    this.resetPerformanceMetrics();
    
    // 准备处理队列
    this.processingQueue = [...meshes];
    this.isProcessing = true;
    
    // 开始批处理
    return this.processBatches();
  }

  /**
   * 批量处理网格
   */
  private async processBatches(): Promise<void> {
    return new Promise((resolve) => {
      // 记录开始时间
      this.performanceMetrics.startTime = performance.now();
      this.processingStartTime = Date.now();
      
      // 定义批处理函数
      const processBatch = () => {
        if (!this.isProcessing || this.processingQueue.length === 0) {
          // 处理完成
          this.isProcessing = false;
          if (this.onCompleteCallback) {
            this.onCompleteCallback();
          }
          this.requestRenderUpdate();
          resolve();
          return;
        }
        
        const batchStartTime = performance.now();
        
        // 动态调整批处理大小
        this.adjustBatchSize();
        
        // 获取当前批次
        const batch = this.processingQueue.splice(0, this.config.batchSize);
        
        // 处理当前批次
        this.processMeshBatch(batch, this.state.isTransparent);
        
        // 更新性能指标
        const batchEndTime = performance.now();
        this.updatePerformanceMetrics(batch.length, batchStartTime, batchEndTime);
        
        // 更新进度
        if (this.onProgressCallback) {
          const progress = this.performanceMetrics.totalItemsProcessed / 
                          (this.performanceMetrics.totalItemsProcessed + this.processingQueue.length);
          this.onProgressCallback(Math.floor(progress * 100));
        }
        
        // 请求渲染更新
        this.requestRenderUpdate();
        
        // 使用requestAnimationFrame安排下一批处理
        // 如果批处理时间过长，使用setTimeout给主线程一些喘息空间
        if (this.performanceMetrics.lastBatchTime > 16) {
          setTimeout(() => requestAnimationFrame(processBatch), 0);
        } else {
          requestAnimationFrame(processBatch);
        }
      };
      
      // 开始批处理
      requestAnimationFrame(processBatch);
    });
  }

  /**
   * 处理单个批次的网格
   */
  private processMeshBatch(meshes: THREE.Mesh[], makeTransparent: boolean): void {
    for (const mesh of meshes) {
      if (!mesh || !mesh.isMesh) continue;
      
      const meshId = mesh.uuid;
      
      if (makeTransparent) {
        // 保存原始材质
        if (!this.state.originalMaterials.has(meshId)) {
          this.state.originalMaterials.set(
            meshId, 
            Array.isArray(mesh.material) 
              ? mesh.material.map(m => m.clone()) 
              : mesh.material.clone()
          );
        }
        
        // 应用透明材质
        mesh.material = this.transparentMaterial;
        mesh.renderOrder = -1;
        
        // 添加边缘线
        if (!this.state.edgeMeshes.has(meshId) && mesh.geometry) {
          try {
            // 使用预计算的边缘几何体或创建新的
            const edgeGeometry = mesh.userData.precomputedEdgeGeometry || 
                                new THREE.EdgesGeometry(mesh.geometry, 30);
            
            const edgeMesh = new THREE.LineSegments(edgeGeometry, this.edgeMaterial);
            edgeMesh.visible = true;
            edgeMesh.renderOrder = -1;
            
            mesh.add(edgeMesh);
            this.state.edgeMeshes.set(meshId, edgeMesh);
          } catch (error) {
            console.debug(`创建边缘线失败: ${mesh.name}`);
          }
        } else if (this.state.edgeMeshes.has(meshId)) {
          const edgeMesh = this.state.edgeMeshes.get(meshId);
          if (edgeMesh) edgeMesh.visible = true;
        }
      } else {
        // 恢复原始材质
        if (this.state.originalMaterials.has(meshId)) {
          mesh.material = this.state.originalMaterials.get(meshId)!;
          mesh.renderOrder = 0;
        }
        
        // 隐藏边缘线
        if (this.state.edgeMeshes.has(meshId)) {
          const edgeMesh = this.state.edgeMeshes.get(meshId);
          if (edgeMesh) edgeMesh.visible = false;
        }
      }
    }
  }

  /**
   * 动态调整批处理大小
   */
  private adjustBatchSize(): void {
    if (this.performanceMetrics.batchesProcessed < 3) return;
    
    // 根据上一批次的处理时间调整批处理大小
    if (this.performanceMetrics.lastBatchTime < 8) {
      // 处理很快，增加批处理大小
      this.config.batchSize = Math.min(
        this.config.batchSize * 1.5, 
        this.config.maxBatchSize
      );
    } else if (this.performanceMetrics.lastBatchTime > 20) {
      // 处理较慢，减小批处理大小
      this.config.batchSize = Math.max(
        Math.floor(this.config.batchSize * 0.8), 
        this.config.minBatchSize
      );
    }
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(itemsProcessed: number, startTime: number, endTime: number): void {
    this.performanceMetrics.lastBatchTime = endTime - startTime;
    this.performanceMetrics.totalItemsProcessed += itemsProcessed;
    this.performanceMetrics.batchesProcessed++;
    
    // 更新平均处理时间
    this.performanceMetrics.averageBatchTime = 
      (this.performanceMetrics.averageBatchTime * (this.performanceMetrics.batchesProcessed - 1) + 
       this.performanceMetrics.lastBatchTime) / this.performanceMetrics.batchesProcessed;
  }

  /**
   * 重置性能指标
   */
  private resetPerformanceMetrics(): void {
    this.performanceMetrics = {
      lastBatchTime: 0,
      averageBatchTime: 0,
      batchesProcessed: 0,
      totalItemsProcessed: 0,
      startTime: 0
    };
  }

  /**
   * 取消当前处理
   */
  public cancelProcessing(): void {
    this.isProcessing = false;
    this.processingQueue = [];
  }

  /**
   * 设置进度回调
   */
  public onProgress(callback: (progress: number) => void): void {
    this.onProgressCallback = callback;
  }

  /**
   * 设置完成回调
   */
  public onComplete(callback: () => void): void {
    this.onCompleteCallback = callback;
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.cancelProcessing();
    
    // 清理材质
    this.transparentMaterial.dispose();
    this.edgeMaterial.dispose();
    
    // 清理材质实例
    this.materialInstances.forEach(material => material.dispose());
    this.materialInstances.clear();
    
    // 重置状态
    this.state.originalMaterials.clear();
    this.state.edgeMeshes.clear();
    
    TransparencyManager.instance = null;
  }
}
