import { FormProps } from '/@/components/Table';
import { BasicColumn } from '/@/components/Table/src/types/table';

export function getBasicColumns(): BasicColumn[] {
  return [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      width: 150,
    },
    {
      title: '信号名称',
      dataIndex: 'signalName',
      width: 150,
      customRender: ({ text, record }) => {
        // 当信号名称为"开机"或"关机"时，显示"状态"
        if (record.type === 1 && (text === '开机' || text === '关机')) {
          return '状态';
        }
        return text;
      },
    },
    {
      title: '数据类型',
      dataIndex: 'type',
      width: 100,
      customRender: ({ text }) => {
        const typeMap: Record<number, string> = {
          0: '告警',
          1: '数字输出量(遥控)',
          2: '模拟输出量(遥调)',
          3: '模拟输入量(遥测)',
          4: '数字输入量(遥信)',
          5: '设备',
          6: '机房',
          7: '站点',
          8: '区域',
        };
        return typeMap[text as number] || text;
      },
    },
    {
      title: '数值',
      dataIndex: 'valueData',
      width: 120,
      customRender: ({ record }) => {
        // 如果没有valueData，返回空字符串
        if (record.valueData === undefined) return '';

        const value = parseFloat(record.valueData);

        // 处理特殊情况：describe字段包含映射信息（如"1&开 0&关"）
        if (record.describe && record.describe.includes('&')) {
          const mappings = record.describe.split(' ');

          // 查找匹配的映射
          for (const mapping of mappings) {
            const [mapValue, mapText] = mapping.split('&');
            if (parseFloat(mapValue) === value) {
              return mapText;
            }
          }
        }

        // 处理特殊情况：type=1（数字输出量/遥控）且signalName为"开机"或"关机"
        if (record.type === 1 && (record.signalName === '开机' || record.signalName === '关机')) {
          // 显示"开机"或"关机"状态
          if (record.signalName === '开机') {
            return value > 0 ? '开机' : '关机';
          } else {
            return value > 0 ? '关机' : '开机';
          }
        }

        // 默认情况：显示值+单位
        return `${record.valueData}${record.describe || ''}`;
      },
    },
    {
      title: '时间',
      dataIndex: 'dataTime',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      customRender: ({ text }) => {
        const statusMap: Record<number, string> = {
          0: '正常数据',
          1: '一级告警',
          2: '二级告警',
          3: '三级告警',
          4: '四级告警',
          5: '操作事件',
          6: '无效数据',
        };
        return statusMap[text as number] || text;
      },
    },
  ];
}

export function getFormConfig(): Partial<FormProps> {
  return {
    labelWidth: 80,
    layout: 'inline',
    showActionButtonGroup: true,
    actionColOptions: {
      span: 6,
    },
    rowProps: {
      gutter: 16,
    },
    schemas: [
      {
        field: 'deviceName',
        label: '设备名称',
        component: 'Input',
        colProps: { span: 9 },
      },
      {
        field: 'signalName',
        label: '信号名称',
        component: 'Input',
        colProps: { span: 9 },
      },
    ],
  };
}
