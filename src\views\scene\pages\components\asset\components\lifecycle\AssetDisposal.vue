<template>
  <div class="h-full flex flex-col">
    <!-- 操作工具栏 -->
    <div class="flex justify-between items-center mb-[1vw]">
      <div class="flex items-center space-x-[0.8vw]">
        <button
          class="px-[0.8vw] py-[0.4vw] bg-red-500 text-white text-[0.6vw] rounded hover:bg-red-600 transition-colors"
          @click="showDisposalDialog = true"
        >
          <DeleteOutlined class="mr-[0.2vw]" />
          申请报废
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
          @click="batchApprove"
          :disabled="selectedDisposals.length === 0"
        >
          <CheckOutlined class="mr-[0.2vw]" />
          批量审批
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors"
          @click="exportDisposals"
        >
          <DownloadOutlined class="mr-[0.2vw]" />
          导出记录
        </button>
      </div>

      <div class="flex items-center space-x-[0.6vw]">
        <select v-model="statusFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部状态</option>
          <option value="pending">待审批</option>
          <option value="approved">已批准</option>
          <option value="rejected">已拒绝</option>
          <option value="disposed">已处置</option>
        </select>
        <select v-model="reasonFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部原因</option>
          <option value="damaged">设备损坏</option>
          <option value="obsolete">技术淘汰</option>
          <option value="expired">超期使用</option>
          <option value="upgrade">设备升级</option>
        </select>
        <input
          v-model="searchQuery"
          placeholder="搜索报废单号、资产名称..."
          class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none w-[15vw]"
        />
        <button
          class="px-[0.6vw] py-[0.3vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors"
          @click="searchDisposals"
        >
          <SearchOutlined />
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-black/20 rounded border border-white/10 overflow-hidden">
      <div class="overflow-auto h-full">
        <table class="w-full text-[0.6vw]">
          <thead class="bg-blue-500/20 sticky top-0">
            <tr>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">
                <input type="checkbox" @change="toggleSelectAll" class="mr-[0.4vw]" />
                报废单号
              </th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">资产名称</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">资产编号</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">报废原因</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">申请人</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">申请时间</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">状态</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="disposal in paginatedDisposals" :key="disposal.id" class="hover:bg-white/5 transition-colors">
              <td class="p-[0.6vw] text-white border-b border-white/5">
                <input
                  type="checkbox"
                  :checked="selectedDisposals.includes(disposal.id)"
                  @change="toggleSelectDisposal(disposal.id)"
                  class="mr-[0.4vw]"
                />
                {{ disposal.disposalCode }}
              </td>
              <td class="p-[0.6vw] text-white border-b border-white/5">{{ disposal.assetName }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ disposal.assetCode }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ getReasonText(disposal.reason) }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ disposal.applicant }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ disposal.applicationDate }}</td>
              <td class="p-[0.6vw] border-b border-white/5">
                <span
                  :class="[
                    'px-[0.4vw] py-[0.1vw] rounded text-[0.5vw]',
                    disposal.status === 'pending'
                      ? 'bg-yellow-500/20 text-yellow-400'
                      : disposal.status === 'approved'
                        ? 'bg-green-500/20 text-green-400'
                        : disposal.status === 'rejected'
                          ? 'bg-red-500/20 text-red-400'
                          : 'bg-gray-500/20 text-gray-400',
                  ]"
                >
                  {{ getStatusText(disposal.status) }}
                </span>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex space-x-[0.4vw]">
                  <button
                    v-if="disposal.status === 'pending'"
                    class="text-green-400 hover:text-green-300 text-[0.5vw] bg-transparent"
                    @click="approveDisposal(disposal)"
                  >
                    审批
                  </button>
                  <button
                    v-if="disposal.status === 'approved'"
                    class="text-blue-400 hover:text-blue-300 text-[0.5vw] bg-transparent"
                    @click="executeDisposal(disposal)"
                  >
                    执行
                  </button>
                  <button class="text-orange-400 hover:text-orange-300 text-[0.5vw] bg-transparent" @click="viewDisposalDetail(disposal)">
                    详情
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-between items-center mt-[0.8vw] text-[0.6vw] text-gray-400">
      <div>共 {{ filteredDisposals.length }} 条记录</div>
      <div class="flex items-center space-x-[0.4vw]">
        <button
          :disabled="currentPage === 1"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="text-white">{{ currentPage }} / {{ totalPages }}</span>
        <button
          :disabled="currentPage === totalPages"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage++"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 申请报废弹窗 -->
    <ModalDialog
      v-model:visible="showDisposalDialog"
      title="申请资产报废"
      width="60vw"
      :show-footer="true"
      @confirm="confirmDisposal"
      @cancel="showDisposalDialog = false"
    >
      <AssetDisposalForm v-model:form-data="disposalForm" />
    </ModalDialog>

    <!-- 审批弹窗 -->
    <ModalDialog
      v-model:visible="showApprovalDialog"
      title="审批报废申请"
      width="50vw"
      :show-footer="true"
      @confirm="confirmApproval"
      @cancel="showApprovalDialog = false"
    >
      <DisposalApprovalForm v-model:form-data="approvalForm" :disposal="selectedDisposal" />
    </ModalDialog>

    <!-- 执行报废弹窗 -->
    <ModalDialog
      v-model:visible="showExecutionDialog"
      title="执行资产报废"
      width="60vw"
      :show-footer="true"
      @confirm="confirmExecution"
      @cancel="showExecutionDialog = false"
    >
      <DisposalExecutionForm v-model:form-data="executionForm" :disposal="selectedDisposal" />
    </ModalDialog>

    <!-- 报废详情弹窗 -->
    <ModalDialog v-model:visible="showDetailDialog" title="报废详情" width="70vw" :show-footer="false" @cancel="showDetailDialog = false">
      <DisposalDetailView :disposal="selectedDisposal" />
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { DeleteOutlined, CheckOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import AssetDisposalForm from './forms/AssetDisposalForm.vue';
  import DisposalApprovalForm from './forms/DisposalApprovalForm.vue';
  import DisposalExecutionForm from './forms/DisposalExecutionForm.vue';
  import DisposalDetailView from './forms/DisposalDetailView.vue';

  // 响应式数据
  const disposals = ref([]);
  const selectedDisposals = ref([]);
  const selectedDisposal = ref(null);
  const searchQuery = ref('');
  const statusFilter = ref('');
  const reasonFilter = ref('');
  const currentPage = ref(1);
  const pageSize = ref(20);
  const showDisposalDialog = ref(false);
  const showApprovalDialog = ref(false);
  const showExecutionDialog = ref(false);
  const showDetailDialog = ref(false);
  const disposalForm = ref({});
  const approvalForm = ref({});
  const executionForm = ref({});

  // 计算属性
  const filteredDisposals = computed(() => {
    let result = disposals.value;

    if (statusFilter.value) {
      result = result.filter((disposal) => disposal.status === statusFilter.value);
    }

    if (reasonFilter.value) {
      result = result.filter((disposal) => disposal.reason === reasonFilter.value);
    }

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(
        (disposal) =>
          disposal.disposalCode.toLowerCase().includes(query) ||
          disposal.assetName.toLowerCase().includes(query) ||
          disposal.assetCode.toLowerCase().includes(query)
      );
    }

    return result;
  });

  const totalPages = computed(() => Math.ceil(filteredDisposals.value.length / pageSize.value));

  const paginatedDisposals = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return filteredDisposals.value.slice(start, end);
  });

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      pending: '待审批',
      approved: '已批准',
      rejected: '已拒绝',
      disposed: '已处置',
    };
    return statusMap[status] || status;
  };

  const getReasonText = (reason) => {
    const reasonMap = {
      damaged: '设备损坏',
      obsolete: '技术淘汰',
      expired: '超期使用',
      upgrade: '设备升级',
    };
    return reasonMap[reason] || reason;
  };

  const toggleSelectAll = (event) => {
    if (event.target.checked) {
      selectedDisposals.value = paginatedDisposals.value.map((disposal) => disposal.id);
    } else {
      selectedDisposals.value = [];
    }
  };

  const toggleSelectDisposal = (disposalId) => {
    const index = selectedDisposals.value.indexOf(disposalId);
    if (index > -1) {
      selectedDisposals.value.splice(index, 1);
    } else {
      selectedDisposals.value.push(disposalId);
    }
  };

  const searchDisposals = () => {
    currentPage.value = 1;
  };

  const approveDisposal = (disposal) => {
    selectedDisposal.value = disposal;
    showApprovalDialog.value = true;
  };

  const executeDisposal = (disposal) => {
    selectedDisposal.value = disposal;
    showExecutionDialog.value = true;
  };

  const viewDisposalDetail = (disposal) => {
    selectedDisposal.value = disposal;
    showDetailDialog.value = true;
  };

  const batchApprove = () => {
    if (selectedDisposals.value.length === 0) {
      alert('请选择要批量审批的报废申请');
      return;
    }

    const pendingDisposals = disposals.value.filter((disposal) => selectedDisposals.value.includes(disposal.id) && disposal.status === 'pending');

    if (pendingDisposals.length === 0) {
      alert('所选记录中没有待审批的报废申请');
      return;
    }

    pendingDisposals.forEach((disposal) => {
      disposal.status = 'approved';
      disposal.approveDate = new Date().toISOString().split('T')[0];
    });

    selectedDisposals.value = [];
    alert(`批量审批成功！共审批 ${pendingDisposals.length} 条报废申请`);
  };

  const exportDisposals = () => {
    const exportData = filteredDisposals.value.map((disposal) => ({
      报废单号: disposal.disposalCode,
      资产名称: disposal.assetName,
      资产编号: disposal.assetCode,
      报废原因: getReasonText(disposal.reason),
      申请人: disposal.applicant,
      申请时间: disposal.applicationDate,
      状态: getStatusText(disposal.status),
    }));

    const headers = Object.keys(exportData[0] || {});
    const csvContent = [headers.join(','), ...exportData.map((row) => headers.map((header) => `"${row[header] || ''}"`).join(','))].join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `报废数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert('报废数据导出成功！');
  };

  const confirmDisposal = () => {
    if (!disposalForm.value.assetName || !disposalForm.value.reason) {
      alert('请填写必填字段：资产名称、报废原因');
      return;
    }

    const newDisposal = {
      id: Date.now(),
      disposalCode: `DIS-${new Date().getFullYear()}-${String(disposals.value.length + 1).padStart(3, '0')}`,
      assetName: disposalForm.value.assetName,
      assetCode: disposalForm.value.assetCode || `IT-${Date.now()}`,
      reason: disposalForm.value.reason,
      applicant: disposalForm.value.applicant || '当前用户',
      applicationDate: new Date().toISOString().split('T')[0],
      status: 'pending',
      description: disposalForm.value.description,
    };

    disposals.value.unshift(newDisposal);
    disposalForm.value = {};
    showDisposalDialog.value = false;

    alert('报废申请提交成功！');
  };

  const confirmApproval = () => {
    if (!approvalForm.value.result) {
      alert('请选择审批结果');
      return;
    }

    if (selectedDisposal.value) {
      selectedDisposal.value.status = approvalForm.value.result;
      selectedDisposal.value.approveDate = new Date().toISOString().split('T')[0];
      selectedDisposal.value.approveComments = approvalForm.value.comments;
    }

    approvalForm.value = {};
    showApprovalDialog.value = false;

    alert('审批完成！');
  };

  const confirmExecution = () => {
    if (selectedDisposal.value) {
      selectedDisposal.value.status = 'disposed';
      selectedDisposal.value.executeDate = new Date().toISOString().split('T')[0];
    }

    executionForm.value = {};
    showExecutionDialog.value = false;

    alert('报废执行完成！');
  };

  // 初始化数据
  onMounted(() => {
    disposals.value = [
      {
        id: 1,
        disposalCode: 'DIS-2024-001',
        assetName: '旧服务器',
        assetCode: 'IT-2020-001',
        reason: 'damaged',
        applicant: '张三',
        applicationDate: '2024-01-15',
        status: 'pending',
        description: '服务器主板损坏，无法修复',
      },
      {
        id: 2,
        disposalCode: 'DIS-2024-002',
        assetName: '老式打印机',
        assetCode: 'IT-2019-005',
        reason: 'obsolete',
        applicant: '李四',
        applicationDate: '2024-01-16',
        status: 'approved',
        description: '设备技术落后，已无维修价值',
      },
    ];
  });
</script>
