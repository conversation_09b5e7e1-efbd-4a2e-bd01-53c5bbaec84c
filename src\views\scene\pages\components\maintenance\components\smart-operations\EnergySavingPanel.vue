<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 能耗概览 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-chart-area mr-[0.4vw] text-blue-400"></i>
        能耗监控概览
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in energyStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.trend" class="text-[0.5vw] mt-[0.2vw]" :class="stat.trendClass">
            {{ stat.trend }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：节能措施效果 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-leaf mr-[0.4vw] text-blue-400"></i>
            节能措施效果
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="measure in measureTypes"
              :key="measure.key"
              @click="activeMeasureType = measure.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activeMeasureType === measure.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30',
              ]"
            >
              {{ measure.label }}
            </button>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div v-for="measure in filteredMeasures" :key="measure.id" class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all">
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <i :class="measure.icon" class="mr-[0.6vw] text-green-400"></i>
                  <span class="text-[0.65vw] text-white font-medium">{{ measure.name }}</span>
                </div>
                <span class="text-[0.6vw]" :class="getEffectClass(measure.effect)">
                  {{ measure.effect }}
                </span>
              </div>
              <div class="text-[0.6vw] text-gray-400 mb-[0.3vw]">{{ measure.description }}</div>
              <div class="flex justify-between text-[0.6vw]">
                <span class="text-gray-400">节能量：{{ measure.savings }}</span>
                <span class="text-gray-400">实施时间：{{ measure.implementDate }}</span>
              </div>
              <div class="mt-[0.3vw]">
                <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                  <div class="bg-green-400 h-[0.3vw] rounded-full transition-all" :style="{ width: measure.progress + '%' }"></div>
                </div>
                <div class="text-[0.5vw] text-gray-400 mt-[0.2vw]">实施进度：{{ measure.progress }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：环保指标和能耗分析 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 环保指标 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-globe-americas mr-[0.4vw] text-blue-400"></i>
            环保指标监控
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="indicator in environmentalIndicators" :key="indicator.name" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ indicator.name }}</span>
                <span class="text-[0.6vw]" :class="getIndicatorClass(indicator.status)">{{ indicator.value }}</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>目标：{{ indicator.target }}</span>
                <span>{{ indicator.comparison }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备能耗排行 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-ranking-star mr-[0.4vw] text-blue-400"></i>
            设备能耗排行
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="(device, index) in deviceEnergyRanking" :key="device.name" class="flex items-center justify-between">
              <div class="flex items-center">
                <div
                  class="w-[1.2vw] h-[1.2vw] rounded-full flex items-center justify-center text-[0.5vw] font-bold mr-[0.4vw]"
                  :class="getRankingClass(index)"
                >
                  {{ index + 1 }}
                </div>
                <span class="text-[0.6vw] text-gray-400">{{ device.name }}</span>
              </div>
              <span class="text-[0.6vw] text-white font-medium">{{ device.consumption }}</span>
            </div>
          </div>
        </div>

        <!-- 节能建议 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-lightbulb mr-[0.4vw] text-blue-400"></i>
            智能节能建议
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="suggestion in energySuggestions" :key="suggestion.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex items-start">
                <div class="w-[0.6vw] h-[0.6vw] rounded-full bg-yellow-400 mt-[0.3vw] mr-[0.4vw] flex-shrink-0"></div>
                <div class="flex-1">
                  <div class="text-[0.6vw] text-white mb-[0.2vw]">{{ suggestion.title }}</div>
                  <div class="text-[0.5vw] text-gray-400">{{ suggestion.description }}</div>
                  <div class="text-[0.5vw] text-green-400 mt-[0.2vw]">预计节能：{{ suggestion.expectedSavings }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 能耗统计数据
  const energyStats = ref([
    {
      label: '总能耗',
      value: '1,245 kWh',
      valueClass: 'text-blue-400',
      trend: '↓ 8.5%',
      trendClass: 'text-green-400',
    },
    {
      label: '今日能耗',
      value: '156 kWh',
      valueClass: 'text-yellow-400',
      trend: '↓ 12%',
      trendClass: 'text-green-400',
    },
    {
      label: 'PUE值',
      value: '1.42',
      valueClass: 'text-green-400',
      trend: '↓ 0.08',
      trendClass: 'text-green-400',
    },
    {
      label: '碳排放',
      value: '0.85 t',
      valueClass: 'text-orange-400',
      trend: '↓ 15%',
      trendClass: 'text-green-400',
    },
    {
      label: '节能率',
      value: '18.5%',
      valueClass: 'text-green-400',
      trend: '↑ 3.2%',
      trendClass: 'text-green-400',
    },
  ]);

  // 措施类型
  const measureTypes = ref([
    { key: 'all', label: '全部' },
    { key: 'cooling', label: '制冷优化' },
    { key: 'lighting', label: '照明节能' },
    { key: 'power', label: '电力管理' },
  ]);

  const activeMeasureType = ref('all');

  // 节能措施
  const energyMeasures = ref([
    {
      id: 1,
      name: '智能温控系统',
      type: 'cooling',
      icon: 'fas fa-thermometer-half',
      description: '根据负载自动调节空调温度，优化制冷效率',
      effect: '节能15%',
      savings: '180 kWh/月',
      implementDate: '2024-01-15',
      progress: 95,
    },
    {
      id: 2,
      name: 'LED照明改造',
      type: 'lighting',
      icon: 'fas fa-lightbulb',
      description: '将传统照明设备更换为高效LED灯具',
      effect: '节能40%',
      savings: '120 kWh/月',
      implementDate: '2024-02-01',
      progress: 100,
    },
    {
      id: 3,
      name: '变频器应用',
      type: 'power',
      icon: 'fas fa-cog',
      description: '在风机、水泵等设备上应用变频技术',
      effect: '节能25%',
      savings: '200 kWh/月',
      implementDate: '2024-01-20',
      progress: 88,
    },
    {
      id: 4,
      name: '余热回收系统',
      type: 'cooling',
      icon: 'fas fa-recycle',
      description: '回收服务器废热用于办公区域供暖',
      effect: '节能30%',
      savings: '300 kWh/月',
      implementDate: '2024-02-10',
      progress: 75,
    },
    {
      id: 5,
      name: '智能照明控制',
      type: 'lighting',
      icon: 'fas fa-magic',
      description: '基于人员活动自动调节照明亮度和开关',
      effect: '节能35%',
      savings: '90 kWh/月',
      implementDate: '2024-02-15',
      progress: 92,
    },
  ]);

  // 筛选后的措施
  const filteredMeasures = computed(() => {
    if (activeMeasureType.value === 'all') {
      return energyMeasures.value;
    }
    return energyMeasures.value.filter((measure) => measure.type === activeMeasureType.value);
  });

  // 环保指标
  const environmentalIndicators = ref([
    { name: 'CO₂排放量', value: '0.85 t', target: '< 1.0 t', status: 'good', comparison: '达标' },
    { name: '能源利用率', value: '87.5%', target: '> 85%', status: 'good', comparison: '优秀' },
    { name: '可再生能源占比', value: '12.3%', target: '> 10%', status: 'good', comparison: '达标' },
    { name: '废热回收率', value: '68.2%', target: '> 60%', status: 'good', comparison: '良好' },
    { name: '水资源利用率', value: '92.1%', target: '> 90%', status: 'good', comparison: '优秀' },
  ]);

  // 设备能耗排行
  const deviceEnergyRanking = ref([
    { name: '空调系统', consumption: '456 kWh' },
    { name: '服务器机柜', consumption: '342 kWh' },
    { name: 'UPS系统', consumption: '189 kWh' },
    { name: '照明系统', consumption: '123 kWh' },
    { name: '网络设备', consumption: '89 kWh' },
  ]);

  // 节能建议
  const energySuggestions = ref([
    {
      id: 1,
      title: '优化空调运行策略',
      description: '建议在夜间和周末降低空调设定温度',
      expectedSavings: '50 kWh/月',
    },
    {
      id: 2,
      title: '升级老旧UPS设备',
      description: '更换效率较低的UPS设备，提升电源转换效率',
      expectedSavings: '80 kWh/月',
    },
    {
      id: 3,
      title: '实施服务器虚拟化',
      description: '通过虚拟化技术减少物理服务器数量',
      expectedSavings: '120 kWh/月',
    },
  ]);

  // 获取效果样式
  const getEffectClass = (effect) => {
    if (effect.includes('节能')) {
      return 'text-green-400';
    }
    return 'text-blue-400';
  };

  // 获取指标状态样式
  const getIndicatorClass = (status) => {
    switch (status) {
      case 'good':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'danger':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取排行样式
  const getRankingClass = (index) => {
    switch (index) {
      case 0:
        return 'bg-yellow-400 text-black';
      case 1:
        return 'bg-gray-400 text-black';
      case 2:
        return 'bg-orange-400 text-black';
      default:
        return 'bg-blue-400/30 text-blue-400';
    }
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
