import * as THREE from 'three';
import { SceneManager } from './SceneManager';
import { WeatherType } from '../types/weather';

// 定义粒子系统接口
interface ParticleSystems {
  [key: string]: THREE.Points;
}

// 定义云层数据接口
interface CloudData extends THREE.Mesh {
  material: THREE.MeshBasicMaterial;
}

export class WeatherManager {
  private static instance: WeatherManager | null = null;
  private scene: THREE.Scene = SceneManager.getInstance().scene;
  private camera: THREE.Camera | null = null;
  private currentWeather: WeatherType = 'sunny';
  private fogColor: THREE.Color = new THREE.Color(0xaaaaaa);
  private volumetricFog: THREE.Mesh | null = null;
  private cloudOpacity: number = 0;
  private clouds: CloudData[] = [];

  constructor() {
    if (WeatherManager.instance) {
      return WeatherManager.instance;
    }

    WeatherManager.instance = this;
  }

  public static getInstance(): WeatherManager {
    if (!WeatherManager.instance) {
      WeatherManager.instance = new WeatherManager();
    }
    return WeatherManager.instance;
  }

  public init(scene: THREE.Scene, camera: THREE.Camera): void {
    this.scene = scene;
    this.camera = camera;
    this.initClouds();
  }

  protected initClouds(): void {
    const cloudTexture = new THREE.TextureLoader().load('/src/assets/images/cloud.png');
    this.clouds = [];

    for (let i = 0; i < 3; i++) {
      const cloudGeometry = new THREE.PlaneGeometry(500, 500);
      const cloudMaterial = new THREE.MeshBasicMaterial({
        map: cloudTexture,
        transparent: true,
        opacity: 0,
        depthWrite: false,
      });

      const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial) as CloudData;
      cloud.position.y = 200 + i * 20;
      cloud.rotation.x = -Math.PI / 2;
      this.clouds.push(cloud);
      this.scene.add(cloud);
    }
  }

  private initVolumetricFog(): void {
    const fogGeometry = new THREE.BoxGeometry(2000, 100, 2000);
    const fogMaterial = new THREE.MeshPhongMaterial({
      color: this.fogColor,
      transparent: true,
      opacity: 0.15,
      depthWrite: false,
    });

    this.volumetricFog = new THREE.Mesh(fogGeometry, fogMaterial);
    this.volumetricFog.position.y = 50;
    this.volumetricFog.visible = false;
    this.scene.add(this.volumetricFog);
  }

  public dispose(): void {
    // 清理云朵
    this.clouds.forEach((cloud) => {
      this.scene.remove(cloud);
      const material = cloud.material as THREE.Material;
      if (material) {
        material.dispose();
      }
      if (cloud.geometry) {
        cloud.geometry.dispose();
      }
    });
    this.clouds = [];

    if (this.volumetricFog) {
      this.scene.remove(this.volumetricFog);
      const material = this.volumetricFog.material as THREE.Material;
      material.dispose();
      this.volumetricFog.geometry.dispose();
      this.volumetricFog = null;
    }

    WeatherManager.instance = null;
    this.scene.fog = null;
  }

  /**
   * 更新云朵系统
   */
  public updateParticles(): void {
    // 更新云层位置
    this.clouds.forEach((cloud, index) => {
      cloud.position.x += 0.1 + index * 0.05; // 云层移动速度
      if (cloud.position.x > 500) {
        cloud.position.x = -500; // 循环移动
      }
    });
  }

  /**
   * 设置天气类型
   * @param type 天气类型
   */
  public setWeather(type: WeatherType): void {
    this.currentWeather = 'sunny';

    // 重置所有天气效果
    this.scene.fog = null;
    if (this.volumetricFog) {
      this.volumetricFog.visible = false;
    }
    this.scene.background = null; // 恢复默认背景

    // 显示云朵
    this.clouds.forEach((cloud) => {
      cloud.material.opacity = 0.6;
      cloud.material.color.set(0xffffff);
    });
  }
}
