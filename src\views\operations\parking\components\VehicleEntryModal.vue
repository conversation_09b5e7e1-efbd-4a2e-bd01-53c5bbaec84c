<template>
  <a-modal v-model:visible="visible" title="车辆入场" width="500px" @ok="handleSubmit" @cancel="handleCancel">
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-form-item label="车牌号" name="vehicleNumber">
        <a-input v-model:value="formData.vehicleNumber" placeholder="请输入车牌号" style="text-transform: uppercase" />
      </a-form-item>

      <a-form-item label="车主姓名" name="ownerName">
        <a-input v-model:value="formData.ownerName" placeholder="请输入车主姓名" />
      </a-form-item>

      <a-form-item label="联系电话" name="ownerPhone">
        <a-input v-model:value="formData.ownerPhone" placeholder="请输入联系电话" />
      </a-form-item>

      <a-form-item label="指定车位" name="spaceNumber">
        <a-select v-model:value="formData.spaceNumber" placeholder="选择车位（可选）" allow-clear show-search>
          <a-select-option v-for="space in availableSpaces" :key="space.spaceNumber" :value="space.spaceNumber">
            {{ space.spaceNumber }} - {{ space.floor }} {{ space.area }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { vehicleEntry, getParkingSpaces, type ParkingSpace } from '/@/api/operations/parking';

  // Props
  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    'update:visible': [value: boolean];
    success: [];
  }>();

  // 响应式数据
  const formRef = ref();
  const loading = ref(false);
  const availableSpaces = ref<ParkingSpace[]>([]);

  const formData = reactive({
    vehicleNumber: '',
    ownerName: '',
    ownerPhone: '',
    spaceNumber: '',
  });

  // 表单验证规则
  const rules = {
    vehicleNumber: [
      { required: true, message: '请输入车牌号', trigger: 'blur' },
      {
        pattern: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/,
        message: '请输入正确的车牌号',
        trigger: 'blur',
      },
    ],
    ownerName: [{ required: true, message: '请输入车主姓名', trigger: 'blur' }],
    ownerPhone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  };

  // 计算属性
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  // 方法
  const loadAvailableSpaces = async () => {
    try {
      const response = await getParkingSpaces({
        status: 'available',
        pageSize: 100,
      });
      availableSpaces.value = response.records;
    } catch (error) {
      console.error('获取可用车位失败:', error);
    }
  };

  const resetForm = () => {
    Object.assign(formData, {
      vehicleNumber: '',
      ownerName: '',
      ownerPhone: '',
      spaceNumber: '',
    });
    formRef.value?.resetFields();
  };

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();
      loading.value = true;

      await vehicleEntry({
        vehicleNumber: formData.vehicleNumber.toUpperCase(),
        ownerName: formData.ownerName,
        ownerPhone: formData.ownerPhone,
        spaceNumber: formData.spaceNumber || undefined,
      });

      message.success('车辆入场登记成功');
      emit('success');
      resetForm();
    } catch (error) {
      if (error.errorFields) {
        return;
      }
      message.error('入场登记失败，请重试');
    } finally {
      loading.value = false;
    }
  };

  const handleCancel = () => {
    resetForm();
    visible.value = false;
  };

  // 生命周期
  onMounted(() => {
    loadAvailableSpaces();
  });

  // 监听弹窗打开，刷新可用车位
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        loadAvailableSpaces();
      } else {
        resetForm();
      }
    }
  );
</script>
