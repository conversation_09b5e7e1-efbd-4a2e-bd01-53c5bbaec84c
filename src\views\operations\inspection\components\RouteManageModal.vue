<template>
  <a-modal v-model:visible="visible" title="巡检路线管理" width="800px" :footer="null">
    <div class="mb-4">
      <a-button type="primary" @click="showCreateRoute">
        <PlusOutlined />
        新建路线
      </a-button>
    </div>

    <a-table :columns="columns" :data-source="routes" :loading="loading" :pagination="false" size="small">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'isActive'">
          <a-switch v-model:checked="record.isActive" @change="toggleRoute(record)" />
        </template>
        <template v-if="column.key === 'difficulty'">
          <a-tag :color="getDifficultyColor(record.difficulty)">
            {{ getDifficultyText(record.difficulty) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button size="small" @click="editRoute(record)">编辑</a-button>
            <a-button size="small" danger @click="deleteRoute(record)">删除</a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { getInspectionRoutes, type InspectionRoute } from '/@/api/operations/inspection';

  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{ 'update:visible': [value: boolean]; success: [] }>();

  const loading = ref(false);
  const routes = ref<InspectionRoute[]>([]);

  const columns = [
    { title: '路线名称', dataIndex: 'name', key: 'name' },
    { title: '描述', dataIndex: 'description', key: 'description' },
    { title: '检查点数量', dataIndex: 'checkPoints', key: 'checkPoints', customRender: ({ record }) => record.checkPoints?.length || 0 },
    { title: '预计时长', dataIndex: 'estimatedDuration', key: 'estimatedDuration', customRender: ({ record }) => record.estimatedDuration + '分钟' },
    { title: '难度', key: 'difficulty' },
    { title: '频率', dataIndex: 'frequency', key: 'frequency' },
    { title: '状态', key: 'isActive' },
    { title: '操作', key: 'action', width: 150 },
  ];

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const loadRoutes = async () => {
    loading.value = true;
    try {
      routes.value = await getInspectionRoutes();
    } catch (error) {
      message.error('获取路线列表失败');
    } finally {
      loading.value = false;
    }
  };

  const showCreateRoute = () => {
    message.info('创建路线功能开发中...');
  };

  const editRoute = (route: InspectionRoute) => {
    message.info('编辑路线功能开发中...');
  };

  const deleteRoute = (route: InspectionRoute) => {
    message.info('删除路线功能开发中...');
  };

  const toggleRoute = (route: InspectionRoute) => {
    message.success(`路线${route.isActive ? '启用' : '禁用'}成功`);
  };

  const getDifficultyColor = (difficulty: string) => {
    const colors = { easy: 'green', medium: 'orange', hard: 'red' };
    return colors[difficulty] || 'gray';
  };

  const getDifficultyText = (difficulty: string) => {
    const texts = { easy: '简单', medium: '中等', hard: '困难' };
    return texts[difficulty] || difficulty;
  };

  onMounted(() => {
    if (props.visible) {
      loadRoutes();
    }
  });

  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        loadRoutes();
      }
    }
  );
</script>
