# PPT演示功能测试指南

## 测试前准备

### 1. 启动应用
```bash
npm run dev
```
应用将在 http://localhost:3101 启动

### 2. 进入3D场景
1. 访问应用首页
2. 导航到3D场景页面
3. 确保切换到**内景模式**（这是PPT演示功能的前提条件）

## 功能测试步骤

### 第一步：启动PPT演示
1. 在内景模式下，查看底部导航栏
2. 找到"PPT演示"菜单项（应该在"评测服务"旁边）
3. 点击"PPT演示"按钮
4. **预期结果**：
   - 页面自动切换为分屏模式
   - 左侧50%显示3D场景
   - 右侧50%显示PPT播放器
   - 显示"DCIM平台介绍演示"标题

### 第二步：测试PPT播放功能
1. **基本导航测试**：
   - 点击PPT右侧的箭头按钮（下一页）
   - 点击PPT左侧的箭头按钮（上一页）
   - 观察页码变化（应显示"X / 10"格式）

2. **键盘快捷键测试**：
   - 按`→`键或`PageDown`：下一页
   - 按`←`键或`PageUp`：上一页
   - 按`空格键`：切换自动播放

3. **进度条测试**：
   - 拖拽底部进度条到不同位置
   - 观察PPT内容是否相应切换

4. **页面跳转测试**：
   - 在页码输入框输入数字（1-10）
   - 按回车键确认跳转

### 第三步：测试3D场景联动
1. **视角绑定测试**：
   - 点击"视角绑定"按钮
   - 在弹出的模态框中选择一个预设视角
   - 点击"绑定当前3D视角"
   - 关闭模态框

2. **联动效果测试**：
   - 切换到不同的PPT页面
   - 观察3D场景是否自动切换视角
   - 检查视角切换是否平滑（应有1秒过渡动画）

3. **预设视角测试**：
   - 重新打开"视角绑定"界面
   - 点击不同预设视角的"预览"按钮
   - 观察3D场景视角变化

### 第四步：测试自动播放功能
1. 点击"自动播放"按钮
2. 观察PPT是否自动翻页
3. 测试不同的播放间隔（3秒、5秒、8秒、10秒）
4. 点击"暂停"停止自动播放

### 第五步：测试退出功能
1. **方法一**：点击PPT播放器右上角的关闭按钮
2. **方法二**：点击左上角的"退出演示"按钮
3. **方法三**：按`Esc`键
4. **预期结果**：
   - 返回正常的全屏3D场景模式
   - 侧边栏恢复显示
   - 3D场景恢复正常尺寸

## 高级功能测试

### 视角绑定管理
1. **绑定多个视角**：
   - 为不同PPT页面绑定不同的3D视角
   - 测试页面切换时的视角联动

2. **编辑绑定**：
   - 在视角绑定界面点击"编辑"按钮
   - 修改绑定名称和位置参数
   - 保存并测试效果

3. **删除绑定**：
   - 点击"删除"按钮移除视角绑定
   - 确认绑定已被移除

4. **配置导出/导入**：
   - 点击"导出配置"保存视角配置文件
   - 点击"导入配置"加载之前保存的配置

### 3D场景交互测试
在PPT演示模式下，测试3D场景的基本交互：
1. **鼠标拖拽**：旋转视角
2. **滚轮缩放**：放大/缩小场景
3. **右键拖拽**：平移视角
4. 确认这些操作不会影响PPT播放

## 错误情况测试

### 1. 非内景模式测试
1. 切换到外景模式
2. 点击"PPT演示"按钮
3. **预期结果**：显示警告消息"PPT演示功能仅在内景模式下可用"

### 2. 功能冲突测试
1. 在启动PPT演示前，先启动其他功能（如巡检、播放等）
2. 启动PPT演示
3. **预期结果**：其他功能应自动停止

### 3. 浏览器兼容性测试
在不同浏览器中测试：
- Chrome
- Firefox
- Safari
- Edge

## 性能测试

### 1. 响应速度测试
- PPT页面切换响应时间应小于500ms
- 3D视角切换应在1秒内完成
- 自动播放切换应准确按设定间隔执行

### 2. 内存使用测试
- 长时间使用PPT演示功能
- 观察浏览器内存使用情况
- 退出演示后检查内存是否正确释放

### 3. 大量操作测试
- 快速连续切换PPT页面
- 频繁进入/退出演示模式
- 确保功能稳定性

## 预期问题和解决方案

### 常见问题
1. **3D场景显示异常**：
   - 刷新页面重试
   - 检查浏览器控制台错误信息

2. **PPT内容不显示**：
   - 当前使用模拟内容，应显示标题和文本
   - 如需真实PPT，需要配置PPT文件路径

3. **视角切换不生效**：
   - 确认已正确绑定视角
   - 检查3D场景是否正常加载

### 调试方法
1. 打开浏览器开发者工具
2. 查看Console标签页的错误信息
3. 检查Network标签页的网络请求
4. 观察Elements标签页的DOM结构变化

## 测试完成标准

✅ 所有基本功能正常工作
✅ 3D场景与PPT联动正确
✅ 键盘快捷键响应正常
✅ 视角绑定功能完整
✅ 进入/退出演示模式流畅
✅ 错误处理机制有效
✅ 性能表现良好
✅ 多浏览器兼容性良好

## 反馈和改进

如果在测试过程中发现问题，请记录：
1. 问题描述
2. 重现步骤
3. 预期结果 vs 实际结果
4. 浏览器和版本信息
5. 控制台错误信息（如有）

这些信息将帮助进一步优化PPT演示功能。
