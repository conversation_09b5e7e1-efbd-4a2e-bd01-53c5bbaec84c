<template>
  <div class="h-full flex flex-col">
    <!-- 操作工具栏 -->
    <div class="flex justify-between items-center mb-[1vw]">
      <div class="flex items-center space-x-[0.8vw]">
        <button
          class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
          @click="showDeploymentDialog = true"
        >
          <RocketOutlined class="mr-[0.2vw]" />
          新建部署
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors bg-transparent"
          @click="batchDeploy"
          :disabled="selectedDeployments.length === 0"
        >
          <CheckOutlined class="mr-[0.2vw]" />
          批量部署
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors bg-transparent"
          @click="showTopologyView"
        >
          <NodeIndexOutlined class="mr-[0.2vw]" />
          拓扑视图
        </button>
      </div>

      <div class="flex items-center space-x-[0.6vw]">
        <select v-model="statusFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部状态</option>
          <option value="planning">规划中</option>
          <option value="deploying">部署中</option>
          <option value="deployed">已部署</option>
          <option value="failed">部署失败</option>
        </select>
        <select
          v-model="locationFilter"
          class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none"
        >
          <option value="">全部位置</option>
          <option value="datacenter-a">数据中心A</option>
          <option value="datacenter-b">数据中心B</option>
          <option value="office-1f">办公楼1F</option>
          <option value="office-2f">办公楼2F</option>
        </select>
        <input
          v-model="searchQuery"
          placeholder="搜索资产名称、位置..."
          class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none w-[15vw]"
        />
        <button
          class="px-[0.6vw] py-[0.3vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors"
          @click="searchDeployments"
        >
          <SearchOutlined />
        </button>
      </div>
    </div>

    <!-- 部署统计卡片 -->
    <div class="grid grid-cols-4 gap-[0.8vw] mb-[1vw]">
      <div v-for="stat in deploymentStats" :key="stat.label" class="bg-black/20 rounded border border-white/10 p-[0.6vw]">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-[0.5vw] text-gray-400">{{ stat.label }}</div>
            <div :class="['text-[0.8vw] font-semibold', stat.valueClass]">{{ stat.value }}</div>
          </div>
          <div :class="['text-[1vw]', stat.iconClass]">{{ stat.icon }}</div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-black/20 rounded border border-white/10 overflow-hidden">
      <div class="overflow-auto h-full">
        <table class="w-full text-[0.6vw]">
          <thead class="bg-blue-500/20 sticky top-0">
            <tr>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">
                <input type="checkbox" @change="toggleSelectAll" class="mr-[0.4vw]" />
                资产名称
              </th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">资产编号</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">部署位置</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">IP地址</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">负责人</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">部署时间</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">状态</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="deployment in paginatedDeployments" :key="deployment.id" class="hover:bg-white/5 transition-colors">
              <td class="p-[0.6vw] text-white border-b border-white/5">
                <input
                  type="checkbox"
                  :checked="selectedDeployments.includes(deployment.id)"
                  @change="toggleSelectDeployment(deployment.id)"
                  class="mr-[0.4vw]"
                />
                {{ deployment.assetName }}
              </td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ deployment.assetCode }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ deployment.location }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ deployment.ipAddress }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ deployment.owner }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ deployment.deployDate }}</td>
              <td class="p-[0.6vw] border-b border-white/5">
                <span
                  :class="[
                    'px-[0.4vw] py-[0.1vw] rounded text-[0.5vw]',
                    deployment.status === 'planning'
                      ? 'bg-blue-500/20 text-blue-400'
                      : deployment.status === 'deploying'
                        ? 'bg-yellow-500/20 text-yellow-400'
                        : deployment.status === 'deployed'
                          ? 'bg-green-500/20 text-green-400'
                          : 'bg-red-500/20 text-red-400',
                  ]"
                >
                  {{ getStatusText(deployment.status) }}
                </span>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex space-x-[0.4vw]">
                  <button
                    v-if="deployment.status === 'planning'"
                    class="text-green-400 hover:text-green-300 text-[0.5vw] bg-transparent"
                    @click="startDeployment(deployment)"
                  >
                    开始
                  </button>
                  <button
                    v-if="deployment.status === 'deploying'"
                    class="text-blue-400 hover:text-blue-300 text-[0.5vw] bg-transparent"
                    @click="completeDeployment(deployment)"
                  >
                    完成
                  </button>
                  <button class="text-orange-400 hover:text-orange-300 text-[0.5vw] bg-transparent" @click="viewDeploymentDetail(deployment)">
                    详情
                  </button>
                  <button class="text-purple-400 hover:text-purple-300 text-[0.5vw] bg-transparent" @click="editDeployment(deployment)">
                    编辑
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-between items-center mt-[0.8vw] text-[0.6vw] text-gray-400">
      <div>共 {{ filteredDeployments.length }} 条记录</div>
      <div class="flex items-center space-x-[0.4vw]">
        <button
          :disabled="currentPage === 1"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="text-white">{{ currentPage }} / {{ totalPages }}</span>
        <button
          :disabled="currentPage === totalPages"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage++"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 新建部署弹窗 -->
    <ModalDialog
      v-model:visible="showDeploymentDialog"
      title="新建资产部署"
      width="70vw"
      :show-footer="true"
      @confirm="confirmDeployment"
      @cancel="showDeploymentDialog = false"
    >
      <AssetDeploymentForm v-model:form-data="deploymentForm" />
    </ModalDialog>

    <!-- 拓扑视图弹窗 -->
    <ModalDialog
      v-model:visible="showTopologyDialog"
      title="资产部署拓扑图"
      width="90vw"
      height="80vh"
      :show-footer="false"
      @cancel="showTopologyDialog = false"
    >
      <DeploymentTopologyView :deployments="deployments" />
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { RocketOutlined, CheckOutlined, NodeIndexOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import AssetDeploymentForm from './forms/AssetDeploymentForm.vue';
  import DeploymentTopologyView from './forms/DeploymentTopologyView.vue';

  // 响应式数据
  const deployments = ref([]);
  const selectedDeployments = ref([]);
  const searchQuery = ref('');
  const statusFilter = ref('');
  const locationFilter = ref('');
  const currentPage = ref(1);
  const pageSize = ref(20);
  const showDeploymentDialog = ref(false);
  const showTopologyDialog = ref(false);
  const deploymentForm = ref({});

  // 部署统计
  const deploymentStats = ref([
    { label: '总部署数', value: '156', valueClass: 'text-white', icon: '🚀', iconClass: 'text-blue-400' },
    { label: '运行中', value: '142', valueClass: 'text-green-400', icon: '✅', iconClass: 'text-green-400' },
    { label: '部署中', value: '8', valueClass: 'text-yellow-400', icon: '⏳', iconClass: 'text-yellow-400' },
    { label: '故障', value: '6', valueClass: 'text-red-400', icon: '❌', iconClass: 'text-red-400' },
  ]);

  // 计算属性
  const filteredDeployments = computed(() => {
    let result = deployments.value;

    if (statusFilter.value) {
      result = result.filter((deployment) => deployment.status === statusFilter.value);
    }

    if (locationFilter.value) {
      result = result.filter((deployment) => deployment.location.includes(locationFilter.value));
    }

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(
        (deployment) =>
          deployment.assetName.toLowerCase().includes(query) ||
          deployment.location.toLowerCase().includes(query) ||
          deployment.assetCode.toLowerCase().includes(query)
      );
    }

    return result;
  });

  const totalPages = computed(() => Math.ceil(filteredDeployments.value.length / pageSize.value));

  const paginatedDeployments = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return filteredDeployments.value.slice(start, end);
  });

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      planning: '规划中',
      deploying: '部署中',
      deployed: '已部署',
      failed: '部署失败',
    };
    return statusMap[status] || status;
  };

  const toggleSelectAll = (event) => {
    if (event.target.checked) {
      selectedDeployments.value = paginatedDeployments.value.map((deployment) => deployment.id);
    } else {
      selectedDeployments.value = [];
    }
  };

  const toggleSelectDeployment = (deploymentId) => {
    const index = selectedDeployments.value.indexOf(deploymentId);
    if (index > -1) {
      selectedDeployments.value.splice(index, 1);
    } else {
      selectedDeployments.value.push(deploymentId);
    }
  };

  const searchDeployments = () => {
    currentPage.value = 1;
  };

  const startDeployment = (deployment) => {
    deployment.status = 'deploying';
    deployment.startDate = new Date().toISOString().split('T')[0];
  };

  const completeDeployment = (deployment) => {
    deployment.status = 'deployed';
    deployment.completeDate = new Date().toISOString().split('T')[0];
  };

  const viewDeploymentDetail = (deployment) => {
    alert(
      `部署详情：\n\n资产名称：${deployment.assetName}\n资产编号：${deployment.assetCode}\n部署位置：${deployment.location}\nIP地址：${deployment.ipAddress}\n负责人：${deployment.owner}\n部署时间：${deployment.deployDate}\n状态：${getStatusText(deployment.status)}`
    );
  };

  const editDeployment = (deployment) => {
    console.log('编辑部署:', deployment);
  };

  const batchDeploy = () => {
    console.log('批量部署:', selectedDeployments.value);
  };

  const showTopologyView = () => {
    showTopologyDialog.value = true;
  };

  const confirmDeployment = () => {
    console.log('新建部署:', deploymentForm.value);
    showDeploymentDialog.value = false;
  };

  // 初始化数据
  onMounted(() => {
    deployments.value = [
      {
        id: 1,
        assetName: 'Web服务器-01',
        assetCode: 'SRV-2024-001',
        location: '数据中心A-机柜A01',
        ipAddress: '*************',
        owner: '张三',
        deployDate: '2024-01-15',
        status: 'deployed',
      },
      {
        id: 2,
        assetName: '数据库服务器-01',
        assetCode: 'SRV-2024-002',
        location: '数据中心A-机柜A02',
        ipAddress: '*************',
        owner: '李四',
        deployDate: '2024-01-16',
        status: 'deploying',
      },
    ];
  });
</script>
