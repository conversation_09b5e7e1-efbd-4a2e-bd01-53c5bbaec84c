<template>
  <a-modal v-model:visible="visible" title="访客预约" width="600px" @ok="handleSubmit" @cancel="handleCancel">
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="访客姓名" name="name">
            <a-input v-model:value="formData.name" placeholder="请输入访客姓名" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系电话" name="phone">
            <a-input v-model:value="formData.phone" placeholder="请输入联系电话" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="身份证号" name="idCard">
            <a-input v-model:value="formData.idCard" placeholder="请输入身份证号" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="所属公司" name="company">
            <a-input v-model:value="formData.company" placeholder="请输入所属公司" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="被访人" name="visitee">
            <a-input v-model:value="formData.visitee" placeholder="请输入被访人姓名" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="被访人电话" name="visiteePhone">
            <a-input v-model:value="formData.visiteePhone" placeholder="请输入被访人电话" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="访问目的" name="visitPurpose">
        <a-select v-model:value="formData.visitPurpose" placeholder="请选择访问目的">
          <a-select-option value="business">商务洽谈</a-select-option>
          <a-select-option value="meeting">会议参加</a-select-option>
          <a-select-option value="interview">面试</a-select-option>
          <a-select-option value="maintenance">设备维护</a-select-option>
          <a-select-option value="delivery">物品配送</a-select-option>
          <a-select-option value="inspection">检查巡视</a-select-option>
          <a-select-option value="other">其他</a-select-option>
        </a-select>
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="预约访问时间" name="visitTime">
            <a-date-picker v-model:value="formData.visitTime" show-time format="YYYY-MM-DD HH:mm" placeholder="选择访问时间" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="预计离开时间" name="expectedLeaveTime">
            <a-date-picker
              v-model:value="formData.expectedLeaveTime"
              show-time
              format="YYYY-MM-DD HH:mm"
              placeholder="选择离开时间"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="访客照片" name="photo">
        <a-upload
          v-model:file-list="fileList"
          name="photo"
          list-type="picture-card"
          :show-upload-list="false"
          :before-upload="beforeUpload"
          @change="handlePhotoChange"
        >
          <div v-if="formData.photo">
            <img :src="formData.photo" alt="访客照片" style="width: 100%; height: 100%; object-fit: cover" />
          </div>
          <div v-else>
            <PlusOutlined />
            <div style="margin-top: 8px">上传照片</div>
          </div>
        </a-upload>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, watch, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { createVisitorAppointment } from '/@/api/operations/visitor';

  // Props
  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    'update:visible': [value: boolean];
    success: [];
  }>();

  // 响应式数据
  const formRef = ref();
  const fileList = ref([]);
  const loading = ref(false);

  const formData = reactive({
    name: '',
    phone: '',
    idCard: '',
    company: '',
    visitPurpose: '',
    visitee: '',
    visiteePhone: '',
    visitTime: null as Dayjs | null,
    expectedLeaveTime: null as Dayjs | null,
    photo: '',
  });

  // 表单验证规则
  const rules = {
    name: [
      { required: true, message: '请输入访客姓名', trigger: 'blur' },
      { min: 2, max: 20, message: '姓名长度在2-20个字符', trigger: 'blur' },
    ],
    phone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
    ],
    idCard: [
      { required: true, message: '请输入身份证号', trigger: 'blur' },
      { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' },
    ],
    company: [{ required: true, message: '请输入所属公司', trigger: 'blur' }],
    visitPurpose: [{ required: true, message: '请选择访问目的', trigger: 'change' }],
    visitee: [{ required: true, message: '请输入被访人姓名', trigger: 'blur' }],
    visiteePhone: [
      { required: true, message: '请输入被访人电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
    ],
    visitTime: [{ required: true, message: '请选择访问时间', trigger: 'change' }],
    expectedLeaveTime: [{ required: true, message: '请选择预计离开时间', trigger: 'change' }],
  };

  // 计算属性
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  // 方法
  const resetForm = () => {
    Object.assign(formData, {
      name: '',
      phone: '',
      idCard: '',
      company: '',
      visitPurpose: '',
      visitee: '',
      visiteePhone: '',
      visitTime: null,
      expectedLeaveTime: null,
      photo: '',
    });
    fileList.value = [];
    formRef.value?.resetFields();
  };

  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传 JPG/PNG 格式的图片!');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB!');
      return false;
    }
    return false; // 阻止自动上传
  };

  const handlePhotoChange = (info: any) => {
    if (info.file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        formData.photo = e.target?.result as string;
      };
      reader.readAsDataURL(info.file);
    }
  };

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();

      // 验证时间逻辑
      if (formData.visitTime && formData.expectedLeaveTime) {
        if (formData.visitTime.isAfter(formData.expectedLeaveTime)) {
          message.error('访问时间不能晚于预计离开时间');
          return;
        }

        if (formData.visitTime.isBefore(dayjs())) {
          message.error('访问时间不能早于当前时间');
          return;
        }
      }

      loading.value = true;

      const submitData = {
        name: formData.name,
        phone: formData.phone,
        idCard: formData.idCard,
        company: formData.company,
        visitPurpose: formData.visitPurpose,
        visitee: formData.visitee,
        visiteePhone: formData.visiteePhone,
        visitTime: formData.visitTime!.format('YYYY-MM-DD HH:mm:ss'),
        expectedLeaveTime: formData.expectedLeaveTime!.format('YYYY-MM-DD HH:mm:ss'),
        photo: formData.photo,
      };

      await createVisitorAppointment(submitData);
      message.success('访客预约提交成功，等待审批');
      emit('success');
      resetForm();
    } catch (error) {
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('提交失败，请重试');
    } finally {
      loading.value = false;
    }
  };

  const handleCancel = () => {
    resetForm();
    visible.value = false;
  };

  // 监听弹窗关闭，重置表单
  watch(
    () => props.visible,
    (newVal) => {
      if (!newVal) {
        resetForm();
      }
    }
  );
</script>

<style scoped>
  :deep(.ant-upload-select-picture-card) {
    width: 100px;
    height: 100px;
  }
</style>
