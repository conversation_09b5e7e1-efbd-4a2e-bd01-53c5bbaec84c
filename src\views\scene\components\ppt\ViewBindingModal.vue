<template>
  <a-modal :visible="props.visible" title="PPT视角绑定管理" width="800px" :footer="null" @update:visible="$emit('update:visible', $event)">
    <div class="view-binding-content">
      <!-- 当前页面信息 -->
      <div class="mb-4 p-3 bg-blue-50 rounded">
        <div class="flex items-center justify-between">
          <div>
            <span class="text-blue-600 font-medium">当前页面：第 {{ currentSlide + 1 }} 页</span>
            <span class="ml-4 text-gray-600">共 {{ totalSlides }} 页</span>
          </div>
          <a-button type="primary" @click="bindCurrentView" :disabled="!canBindCurrentView">
            <EyeOutlined />
            绑定当前3D视角
          </a-button>
        </div>
      </div>

      <!-- 预设视角选择 -->
      <div class="mb-6">
        <h4 class="text-gray-700 font-medium mb-3">预设3D视角</h4>
        <div class="grid grid-cols-2 gap-3">
          <div
            v-for="preset in presetViews"
            :key="preset.name"
            class="preset-view-card p-3 border rounded cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors"
            :class="{ 'border-blue-400 bg-blue-50': selectedPreset === preset.name }"
            @click="selectPreset(preset)"
          >
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-800">{{ preset.name }}</div>
                <div class="text-sm text-gray-500">{{ preset.description }}</div>
              </div>
              <a-button size="small" type="text" @click.stop="previewView(preset)">
                <EyeOutlined />
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 已绑定视角列表 -->
      <div class="mb-4">
        <h4 class="text-gray-700 font-medium mb-3">已绑定视角</h4>
        <div class="max-h-60 overflow-y-auto custom-scrollbar">
          <div v-if="viewBindings.length === 0" class="text-center text-gray-500 py-8"> 暂无绑定的视角 </div>
          <div v-for="binding in sortedBindings" :key="binding.slideIndex" class="binding-item p-3 border rounded mb-2 hover:bg-gray-50">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center">
                  <span class="font-medium text-gray-800">第 {{ binding.slideIndex + 1 }} 页</span>
                  <span class="ml-2 text-sm text-gray-600">{{ binding.name }}</span>
                  <a-tag v-if="binding.slideIndex === currentSlide" color="blue" size="small" class="ml-2"> 当前页 </a-tag>
                </div>
                <div class="text-xs text-gray-500 mt-1">
                  位置: ({{ formatCoordinate(binding.cameraPosition.x) }}, {{ formatCoordinate(binding.cameraPosition.y) }},
                  {{ formatCoordinate(binding.cameraPosition.z) }})
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <a-button size="small" type="text" @click="previewBinding(binding)">
                  <EyeOutlined />
                  预览
                </a-button>
                <a-button size="small" type="text" @click="editBinding(binding)">
                  <EditOutlined />
                  编辑
                </a-button>
                <a-button size="small" type="text" danger @click="removeBinding(binding.slideIndex)">
                  <DeleteOutlined />
                  删除
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-between">
        <div>
          <a-button @click="clearAllBindings" danger :disabled="viewBindings.length === 0"> 清空所有绑定 </a-button>
        </div>
        <div class="space-x-2">
          <a-button @click="exportBindings" :disabled="viewBindings.length === 0"> 导出配置 </a-button>
          <a-button @click="importBindings"> 导入配置 </a-button>
          <a-button type="primary" @click="closeModal"> 完成 </a-button>
        </div>
      </div>
    </div>

    <!-- 编辑绑定模态框 -->
    <a-modal v-model:visible="editModalVisible" title="编辑视角绑定" width="500px" @ok="saveEditBinding" @cancel="cancelEditBinding">
      <a-form :model="editForm" layout="vertical">
        <a-form-item label="绑定名称">
          <a-input v-model:value="editForm.name" placeholder="请输入绑定名称" />
        </a-form-item>
        <a-form-item label="相机位置">
          <div class="grid grid-cols-3 gap-2">
            <a-input-number v-model:value="editForm.cameraPosition.x" placeholder="X" :precision="2" style="width: 100%" />
            <a-input-number v-model:value="editForm.cameraPosition.y" placeholder="Y" :precision="2" style="width: 100%" />
            <a-input-number v-model:value="editForm.cameraPosition.z" placeholder="Z" :precision="2" style="width: 100%" />
          </div>
        </a-form-item>
        <a-form-item label="目标位置">
          <div class="grid grid-cols-3 gap-2">
            <a-input-number v-model:value="editForm.cameraTarget.x" placeholder="X" :precision="2" style="width: 100%" />
            <a-input-number v-model:value="editForm.cameraTarget.y" placeholder="Y" :precision="2" style="width: 100%" />
            <a-input-number v-model:value="editForm.cameraTarget.z" placeholder="Z" :precision="2" style="width: 100%" />
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { EyeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { useGlobalThreeStore } from '../../store/globalThreeStore';
  import type { PPTViewBinding } from '../../store/globalThreeStore';
  import { CameraController } from '../../lib/CameraController';
  import { message } from 'ant-design-vue';

  // Props
  interface Props {
    visible: boolean;
    currentSlide: number;
    totalSlides: number;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    'update:visible': [visible: boolean];
    'bind-view': [binding: PPTViewBinding];
    'remove-binding': [slideIndex: number];
  }>();

  // Store
  const globalThreeStore = useGlobalThreeStore();

  // 响应式数据
  const selectedPreset = ref('');
  const editModalVisible = ref(false);
  const editForm = ref<PPTViewBinding>({
    slideIndex: 0,
    cameraPosition: { x: 0, y: 0, z: 0 },
    cameraTarget: { x: 0, y: 0, z: 0 },
    name: '',
  });

  // 预设视角
  const presetViews = ref([
    {
      name: '全景俯视',
      description: '从上方俯视整个场景',
      cameraPosition: { x: 0, y: 100, z: 0 },
      cameraTarget: { x: 0, y: 0, z: 0 },
    },
    {
      name: '正面视角',
      description: '从正面观察场景',
      cameraPosition: { x: 0, y: 20, z: 80 },
      cameraTarget: { x: 0, y: 10, z: 0 },
    },
    {
      name: '侧面视角',
      description: '从侧面观察场景',
      cameraPosition: { x: 80, y: 20, z: 0 },
      cameraTarget: { x: 0, y: 10, z: 0 },
    },
    {
      name: '斜角视角',
      description: '从斜角观察场景',
      cameraPosition: { x: -45, y: 45, z: -70 },
      cameraTarget: { x: 0, y: 10, z: 0 },
    },
    {
      name: '近距离观察',
      description: '近距离观察设备细节',
      cameraPosition: { x: 10, y: 15, z: 30 },
      cameraTarget: { x: 0, y: 10, z: 0 },
    },
    {
      name: '设备巡视',
      description: '设备巡视视角',
      cameraPosition: { x: -20, y: 25, z: 40 },
      cameraTarget: { x: 0, y: 15, z: 0 },
    },
  ]);

  // 计算属性
  const viewBindings = computed(() => globalThreeStore.pptDemonstration.viewBindings);

  const sortedBindings = computed(() => {
    return [...viewBindings.value].sort((a, b) => a.slideIndex - b.slideIndex);
  });

  const canBindCurrentView = computed(() => {
    const cameraController = CameraController.getInstance();
    return !!cameraController;
  });

  // 方法
  const selectPreset = (preset: any) => {
    selectedPreset.value = preset.name;
  };

  const previewView = (preset: any) => {
    const cameraController = CameraController.getInstance();
    if (cameraController) {
      cameraController.moveToPosition(preset.cameraPosition, preset.cameraTarget, 1000);
    }
  };

  const previewBinding = (binding: PPTViewBinding) => {
    const cameraController = CameraController.getInstance();
    if (cameraController) {
      cameraController.moveToPosition(binding.cameraPosition, binding.cameraTarget, 1000);
    }
  };

  const bindCurrentView = () => {
    const cameraController = CameraController.getInstance();
    if (!cameraController) {
      message.error('无法获取当前相机状态');
      return;
    }

    let binding: PPTViewBinding;

    if (selectedPreset.value) {
      // 使用选中的预设视角
      const preset = presetViews.value.find((p) => p.name === selectedPreset.value);
      if (preset) {
        binding = {
          slideIndex: props.currentSlide,
          cameraPosition: preset.cameraPosition,
          cameraTarget: preset.cameraTarget,
          name: `${preset.name} - 第${props.currentSlide + 1}页`,
        };
      } else {
        message.error('未找到选中的预设视角');
        return;
      }
    } else {
      // 使用当前相机位置
      const position = cameraController.getCameraPosition();
      const target = cameraController.currentTarget || { x: 0, y: 0, z: 0 };

      binding = {
        slideIndex: props.currentSlide,
        cameraPosition: { x: position.x, y: position.y, z: position.z },
        cameraTarget: { x: target.x, y: target.y, z: target.z },
        name: `自定义视角 - 第${props.currentSlide + 1}页`,
      };
    }

    emit('bind-view', binding);
    selectedPreset.value = '';
    message.success('视角绑定成功');
  };

  const editBinding = (binding: PPTViewBinding) => {
    editForm.value = { ...binding };
    editModalVisible.value = true;
  };

  const saveEditBinding = () => {
    emit('bind-view', editForm.value);
    editModalVisible.value = false;
    message.success('视角绑定已更新');
  };

  const cancelEditBinding = () => {
    editModalVisible.value = false;
  };

  const removeBinding = (slideIndex: number) => {
    emit('remove-binding', slideIndex);
    message.success('视角绑定已删除');
  };

  const clearAllBindings = () => {
    globalThreeStore.clearPPTViewBindings();
    message.success('已清空所有视角绑定');
  };

  const exportBindings = () => {
    const data = JSON.stringify(viewBindings.value, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ppt-view-bindings.json';
    a.click();
    URL.revokeObjectURL(url);
    message.success('配置已导出');
  };

  const importBindings = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const bindings = JSON.parse(e.target?.result as string);
            bindings.forEach((binding: PPTViewBinding) => {
              globalThreeStore.addPPTViewBinding(binding);
            });
            message.success('配置已导入');
          } catch (error) {
            message.error('配置文件格式错误');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const closeModal = () => {
    emit('update:visible', false);
  };

  const formatCoordinate = (value: number) => {
    return value.toFixed(2);
  };
</script>

<style scoped>
  .view-binding-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .preset-view-card {
    transition: all 0.2s ease;
  }

  .binding-item {
    transition: all 0.2s ease;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
  }
</style>
