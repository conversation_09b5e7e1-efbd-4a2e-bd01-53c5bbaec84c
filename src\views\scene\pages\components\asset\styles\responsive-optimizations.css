/* 机柜U位管理和维保功能响应式优化样式 */

/* 自定义滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

/* 响应式断点优化 */
@media (max-width: 1920px) {
  /* 大屏幕优化 */
  .asset-manager-container {
    font-size: clamp(0.75rem, 0.8vw, 1rem);
  }

  .cabinet-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 1440px) {
  /* 中等屏幕优化 */
  .asset-manager-container {
    padding: clamp(0.8rem, 1vw, 1.2rem);
    gap: clamp(0.8rem, 1vw, 1.2rem);
  }

  .cabinet-management-panel {
    padding: clamp(0.6rem, 0.8vw, 1rem);
  }

  .cabinet-stats-item {
    padding: clamp(0.4rem, 0.6vw, 0.8rem);
  }

  .cabinet-stats-value {
    font-size: clamp(1rem, 1.2vw, 1.4rem);
  }

  .cabinet-stats-label {
    font-size: clamp(0.6rem, 0.65vw, 0.7rem);
  }
}

@media (max-width: 1024px) {
  /* 小屏幕优化 */
  .asset-manager-layout {
    flex-direction: column;
  }

  .cabinet-management-panel {
    margin-bottom: 1rem;
  }

  .cabinet-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .asset-list-buttons {
    flex-wrap: wrap;
    gap: 0.3rem;
  }

  .asset-list-button {
    padding: 0.3rem 0.6rem;
    font-size: 0.6rem;
  }

  /* U位管理响应式 */
  .uspace-manager-layout {
    flex-direction: column;
    gap: 1rem;
  }

  .uspace-3d-panel {
    width: 100%;
    height: 300px;
  }

  .uspace-table-container {
    overflow-x: auto;
  }

  .uspace-table {
    min-width: 600px;
  }

  /* 维保管理响应式 */
  .maintenance-manager-layout {
    flex-direction: column;
    gap: 1rem;
  }

  .maintenance-sidebar {
    width: 100%;
    max-height: 400px;
  }

  .maintenance-table-container {
    overflow-x: auto;
  }

  .maintenance-table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  /* 移动设备优化 */
  .asset-manager-container {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .cabinet-management-panel {
    padding: 0.5rem;
  }

  .cabinet-management-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .cabinet-management-buttons {
    width: 100%;
    justify-content: stretch;
  }

  .cabinet-management-button {
    flex: 1;
    min-width: 0;
    padding: 0.5rem;
    font-size: 0.7rem;
  }

  .cabinet-stats-grid {
    grid-template-columns: 1fr;
    gap: 0.4rem;
  }

  .asset-list-item {
    padding: 0.5rem;
  }

  .asset-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }

  .asset-list-buttons {
    width: 100%;
    justify-content: stretch;
  }

  .asset-list-button {
    flex: 1;
    min-width: 0;
    padding: 0.3rem;
    font-size: 0.6rem;
  }

  /* 表格移动端优化 */
  .responsive-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .responsive-table thead,
  .responsive-table tbody,
  .responsive-table th,
  .responsive-table td,
  .responsive-table tr {
    display: block;
  }

  .responsive-table thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .responsive-table tr {
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.5rem;
    background: rgba(0, 0, 0, 0.2);
  }

  .responsive-table td {
    border: none;
    position: relative;
    padding-left: 50% !important;
    padding-top: 0.3rem;
    padding-bottom: 0.3rem;
  }

  .responsive-table td:before {
    content: attr(data-label) ': ';
    position: absolute;
    left: 6px;
    width: 45%;
    padding-right: 10px;
    white-space: nowrap;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.7);
  }
}

/* 动画和过渡效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .cabinet-management-panel,
  .asset-list-item,
  .uspace-panel,
  .maintenance-panel {
    border-width: 2px;
    border-color: rgba(255, 255, 255, 0.5);
  }

  .cabinet-stats-item,
  .maintenance-stats-item {
    border-width: 1px;
    border-color: rgba(255, 255, 255, 0.3);
  }

  .button-primary,
  .button-secondary {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  .asset-manager-container {
    background: white !important;
    color: black !important;
  }

  .cabinet-management-panel,
  .asset-list-container,
  .uspace-panel,
  .maintenance-panel {
    background: white !important;
    border: 1px solid black !important;
    box-shadow: none !important;
  }

  .button-primary,
  .button-secondary {
    display: none !important;
  }

  .scrollbar-container {
    overflow: visible !important;
    height: auto !important;
  }
}
