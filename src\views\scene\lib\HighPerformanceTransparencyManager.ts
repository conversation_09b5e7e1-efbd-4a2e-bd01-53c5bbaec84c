import * as THREE from 'three';
import { ModelLoaderManager } from './load/ModelLoaderManager';
import {
  createTransparencyMaterial,
  preprocessModelForShader,
  applyTransparencyMaterial,
  restoreOriginalMaterials,
  // setTransparencyEnabled, // 不再使用
  animateTransparency,
} from './shaders/TransparencyShader';

/**
 * 高性能透视管理器
 * 使用自定义着色器实现高效的透视效果，适用于复杂模型
 */
export class HighPerformanceTransparencyManager {
  private static instance: HighPerformanceTransparencyManager | null = null;

  // 状态
  private isTransparent: boolean = false;
  private isProcessing: boolean = false;
  private isPreprocessed: boolean = false;

  // 材质
  private shaderMaterial: THREE.ShaderMaterial;

  // 回调函数
  private onProgressCallback: ((progress: number) => void) | null = null;
  private onCompleteCallback: (() => void) | null = null;

  // 渲染更新请求
  private requestRenderUpdate: () => void;

  // 配置
  private config = {
    transitionDuration: 0, // 禁用过渡动画
    baseColor: new THREE.Color(0x88a8c8), // 更柔和的蓝灰色
    edgeColor: new THREE.Color(0x6a8cad), // 更柔和的边缘颜色
    opacity: 0.15,
    edgeStrength: 1.2,
  };

  private constructor(renderUpdateFn: () => void) {
    // 创建着色器材质
    this.shaderMaterial = createTransparencyMaterial();

    // 设置材质参数
    this.shaderMaterial.uniforms.baseColor.value.copy(this.config.baseColor);
    this.shaderMaterial.uniforms.edgeColor.value.copy(this.config.edgeColor);
    this.shaderMaterial.uniforms.opacity.value = this.config.opacity;
    this.shaderMaterial.uniforms.edgeStrength.value = this.config.edgeStrength;

    // 设置渲染更新函数
    this.requestRenderUpdate = renderUpdateFn || (() => {});

    console.log('[HighPerformanceTransparencyManager] 初始化完成，透视效果将只应用于非设备模型');
  }

  /**
   * 获取单例实例
   */
  public static getInstance(renderUpdateFn?: () => void): HighPerformanceTransparencyManager {
    if (!HighPerformanceTransparencyManager.instance) {
      HighPerformanceTransparencyManager.instance = new HighPerformanceTransparencyManager(renderUpdateFn || (() => {}));
    } else if (renderUpdateFn) {
      HighPerformanceTransparencyManager.instance.requestRenderUpdate = renderUpdateFn;
    }
    return HighPerformanceTransparencyManager.instance;
  }

  /**
   * 预处理模型
   * 标记设备和非设备模型，为透视效果做准备
   * @param forceReset 是否强制重新处理，即使已经预处理过
   */
  public async preprocessModel(forceReset: boolean = false): Promise<boolean> {
    // 如果已经预处理过且不需要强制重置，则直接返回成功
    if (this.isPreprocessed && !forceReset) {
      console.log('[HighPerformanceTransparencyManager] 模型已预处理，跳过');
      return true;
    }

    // 如果正在处理中，等待一小段时间后重试
    if (this.isProcessing) {
      console.warn('[HighPerformanceTransparencyManager] 检测到处理状态冲突，尝试重置状态');
      // 强制重置处理状态
      this.isProcessing = false;
      // 等待一小段时间确保状态已重置
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // 设置处理状态
    this.isProcessing = true;

    try {
      // 获取场景
      const modelLoader = ModelLoaderManager.getInstance();
      console.log('[HighPerformanceTransparencyManager] 预处理 - 获取ModelLoader实例:', !!modelLoader);

      const scene = modelLoader.getScene();
      console.log('[HighPerformanceTransparencyManager] 预处理 - 获取场景:', !!scene);

      if (!scene) {
        console.warn('[HighPerformanceTransparencyManager] 预处理失败：场景未找到');
        this.isProcessing = false;
        return false;
      }

      // 显示进度
      if (this.onProgressCallback) {
        this.onProgressCallback(10);
      }

      // 使用setTimeout让UI有机会更新
      await new Promise((resolve) => setTimeout(resolve, 0));

      // 预处理模型
      console.log('[HighPerformanceTransparencyManager] 开始预处理模型...');

      // 检查场景中是否有网格
      let hasMeshes = false;
      scene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          hasMeshes = true;
        }
      });

      if (!hasMeshes) {
        console.warn('[HighPerformanceTransparencyManager] 预处理失败：场景中没有网格对象');
        return false;
      }

      try {
        preprocessModelForShader(scene);
        console.log('[HighPerformanceTransparencyManager] 预处理模型成功');
      } catch (preprocessError) {
        console.error('[HighPerformanceTransparencyManager] 预处理模型时出错:', preprocessError);
        throw preprocessError;
      }

      // 更新进度
      if (this.onProgressCallback) {
        this.onProgressCallback(100);
      }

      this.isPreprocessed = true;
      console.log('[HighPerformanceTransparencyManager] 模型预处理完成');

      // 调用完成回调
      if (this.onCompleteCallback) {
        this.onCompleteCallback();
      }

      return true;
    } catch (error) {
      console.error('[HighPerformanceTransparencyManager] 预处理错误:', error);
      return false;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 切换透视效果
   * @param makeTransparent 是否启用透视
   * @param timeout 超时时间(毫秒)，默认10秒
   * @returns 是否成功
   */
  public async toggleTransparency(makeTransparent: boolean, timeout: number = 10000): Promise<boolean> {
    // 如果已经在处理中，尝试等待一段时间
    if (this.isProcessing) {
      console.warn('[HighPerformanceTransparencyManager] 检测到处理状态冲突，尝试等待解决...');

      // 等待处理状态释放，最多等待1秒
      const startTime = Date.now();
      while (this.isProcessing && Date.now() - startTime < 1000) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // 如果等待后仍在处理中，则强制重置状态
      if (this.isProcessing) {
        console.warn('[HighPerformanceTransparencyManager] 强制重置处理状态');
        this.isProcessing = false;
      }
    }

    // 添加整体超时处理
    const timeoutPromise = new Promise<boolean>((resolve) => {
      setTimeout(() => {
        console.error('[HighPerformanceTransparencyManager] 操作超时');
        this.isProcessing = false;
        resolve(false);
      }, timeout);
    });

    // 实际处理逻辑
    const processingPromise = this._doToggleTransparency(makeTransparent);

    // 使用Promise.race实现超时处理
    return Promise.race([processingPromise, timeoutPromise]);
  }

  /**
   * 实际执行透明效果切换的内部方法
   * @param makeTransparent 是否启用透视
   * @returns 是否成功
   */
  private async _doToggleTransparency(makeTransparent: boolean): Promise<boolean> {
    // 标记为处理中
    this.isProcessing = true;

    try {
      // 获取场景
      const modelLoader = ModelLoaderManager.getInstance();
      console.log('[HighPerformanceTransparencyManager] 获取ModelLoader实例:', !!modelLoader);

      const scene = modelLoader.getScene();
      console.log('[HighPerformanceTransparencyManager] 获取场景:', !!scene);

      if (!scene) {
        console.warn('[HighPerformanceTransparencyManager] 切换透视失败：场景未找到');
        this.isProcessing = false;
        return false;
      }

      // 如果未预处理，先进行预处理
      if (!this.isPreprocessed) {
        console.log('[HighPerformanceTransparencyManager] 开始预处理模型');

        // 使用强制重置选项进行预处理
        const success = await this.preprocessModel(true);

        console.log('[HighPerformanceTransparencyManager] 预处理结果:', success);
        if (!success) {
          console.error('[HighPerformanceTransparencyManager] 预处理失败');
          this.isProcessing = false;
          return false;
        }
      }

      // 如果是首次应用透视效果，应用着色器材质
      if (this.isTransparent !== makeTransparent && !scene.userData.usingShaderMaterial) {
        console.log('[HighPerformanceTransparencyManager] 应用着色器材质');
        try {
          applyTransparencyMaterial(scene, this.shaderMaterial);
          scene.userData.usingShaderMaterial = true;
          console.log('[HighPerformanceTransparencyManager] 着色器材质应用成功');
        } catch (materialError) {
          console.error('[HighPerformanceTransparencyManager] 应用着色器材质失败:', materialError);
          throw materialError;
        }
      }

      // 如果是关闭透视效果
      if (!makeTransparent && this.isTransparent) {
        console.log('[HighPerformanceTransparencyManager] 关闭透视效果');

        try {
          // 立即设置为不透明
          console.log('[HighPerformanceTransparencyManager] 立即设置为不透明');

          // 直接调用animateTransparency，现在它会立即设置透明度而不是动画过渡
          animateTransparency(
            this.shaderMaterial,
            false, // 设置为不透明
            0,
            this.requestRenderUpdate,
            undefined
          );

          // 立即恢复原始材质
          if (scene.userData.usingShaderMaterial) {
            console.log('[HighPerformanceTransparencyManager] 恢复原始材质');
            restoreOriginalMaterials(scene);
            scene.userData.usingShaderMaterial = false;
          }

          // 调用完成回调
          if (this.onCompleteCallback) {
            this.onCompleteCallback();
          }
        } catch (error) {
          console.error('[HighPerformanceTransparencyManager] 设置透明度失败（关闭）:', error);

          // 即使失败，也尝试恢复原始材质
          if (scene.userData.usingShaderMaterial) {
            restoreOriginalMaterials(scene);
            scene.userData.usingShaderMaterial = false;
          }
        }
      }
      // 如果是开启透视效果
      else if (makeTransparent && !this.isTransparent) {
        console.log('[HighPerformanceTransparencyManager] 开启透视效果');

        try {
          // 立即设置为透明
          console.log('[HighPerformanceTransparencyManager] 立即设置为透明');

          // 直接调用animateTransparency，现在它会立即设置透明度而不是动画过渡
          animateTransparency(
            this.shaderMaterial,
            true, // 设置为透明
            0,
            this.requestRenderUpdate,
            undefined
          );

          // 调用完成回调
          if (this.onCompleteCallback) {
            this.onCompleteCallback();
          }
        } catch (error) {
          console.error('[HighPerformanceTransparencyManager] 设置透明度失败（开启）:', error);
          throw error;
        }
      }

      // 更新状态
      this.isTransparent = makeTransparent;

      console.log('[HighPerformanceTransparencyManager] 透视效果切换成功');
      return true;
    } catch (error) {
      console.error('[HighPerformanceTransparencyManager] 切换透视错误:', error);
      return false;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 重置透视效果
   * @param forceReset 是否强制重置，即使当前不是透明状态
   * @returns 是否成功重置
   */
  public reset(forceReset: boolean = false): boolean {
    try {
      console.log('[HighPerformanceTransparencyManager] 开始重置透视效果');

      // 如果当前不是透明状态且不是强制重置，则无需操作
      if (!this.isTransparent && !forceReset) {
        console.log('[HighPerformanceTransparencyManager] 当前不是透明状态，无需重置');
        return true;
      }

      // 获取场景
      const modelLoader = ModelLoaderManager.getInstance();
      const scene = modelLoader.getScene();

      if (!scene) {
        console.warn('[HighPerformanceTransparencyManager] 重置失败：场景未找到');
        return false;
      }

      // 先确保透明度设置为0
      if (this.shaderMaterial && this.shaderMaterial.uniforms) {
        console.log('[HighPerformanceTransparencyManager] 设置透明度为0');
        this.shaderMaterial.uniforms.transparencyEnabled.value = 0.0;
      }

      // 恢复原始材质
      if (scene.userData.usingShaderMaterial) {
        console.log('[HighPerformanceTransparencyManager] 恢复原始材质');
        restoreOriginalMaterials(scene);
        scene.userData.usingShaderMaterial = false;
      } else {
        console.log('[HighPerformanceTransparencyManager] 场景未使用着色器材质，尝试强制恢复');
        // 即使没有标记，也尝试恢复原始材质
        restoreOriginalMaterials(scene);
      }

      // 重置状态
      this.isTransparent = false;
      this.isProcessing = false;

      // 请求渲染更新
      this.requestRenderUpdate();

      console.log('[HighPerformanceTransparencyManager] 透视效果重置完成');

      // 调用完成回调
      if (this.onCompleteCallback) {
        this.onCompleteCallback();
      }
      return true;
    } catch (error) {
      console.error('[HighPerformanceTransparencyManager] 重置错误:', error);

      // 即使出错，也重置状态
      this.isTransparent = false;
      this.isProcessing = false;

      return false;
    }
  }

  /**
   * 设置进度回调
   */
  public onProgress(callback: (progress: number) => void): void {
    this.onProgressCallback = callback;
  }

  /**
   * 设置完成回调
   */
  public onComplete(callback: () => void): void {
    this.onCompleteCallback = callback;
  }

  /**
   * 获取当前状态
   */
  public getState(): { isTransparent: boolean; isProcessing: boolean; isPreprocessed: boolean } {
    return {
      isTransparent: this.isTransparent,
      isProcessing: this.isProcessing,
      isPreprocessed: this.isPreprocessed,
    };
  }

  /**
   * 紧急恢复函数 - 用于处理极端情况
   * 强制恢复所有材质和状态，不考虑动画和其他因素
   */
  public emergencyRestore(): void {
    console.warn('[HighPerformanceTransparencyManager] 执行紧急恢复');

    try {
      // 获取场景
      const modelLoader = ModelLoaderManager.getInstance();
      const scene = modelLoader.getScene();

      if (scene) {
        // 遍历场景中的所有对象
        scene.traverse((object) => {
          if (object instanceof THREE.Mesh) {
            // 如果有原始材质，恢复它
            if (object.userData.originalMaterial) {
              object.material = object.userData.originalMaterial;
              object.userData.originalMaterial = null;
            }

            // 重置渲染顺序
            object.renderOrder = 0;
          }
        });

        // 重置场景标记
        scene.userData.usingShaderMaterial = false;
      }

      // 重置着色器材质
      if (this.shaderMaterial && this.shaderMaterial.uniforms) {
        this.shaderMaterial.uniforms.transparencyEnabled.value = 0.0;
      }

      // 重置所有状态
      this.isTransparent = false;
      this.isProcessing = false;
      this.isPreprocessed = false;

      // 请求渲染更新
      this.requestRenderUpdate();

      console.log('[HighPerformanceTransparencyManager] 紧急恢复完成');

      // 调用完成回调
      if (this.onCompleteCallback) {
        this.onCompleteCallback();
      }
    } catch (error) {
      console.error('[HighPerformanceTransparencyManager] 紧急恢复失败:', error);
    }
  }

  /**
   * 销毁实例
   */
  public dispose(): void {
    try {
      this.reset();
    } catch (error) {
      console.error('[HighPerformanceTransparencyManager] 销毁时重置失败:', error);
      this.emergencyRestore();
    }

    if (this.shaderMaterial) {
      this.shaderMaterial.dispose();
    }

    HighPerformanceTransparencyManager.instance = null;
  }
}
