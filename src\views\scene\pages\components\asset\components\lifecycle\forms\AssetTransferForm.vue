<template>
  <div class="p-[1vw] space-y-[1vw]">
    <div class="grid grid-cols-2 gap-[1vw]">
      <!-- 基本信息 -->
      <div class="space-y-[0.8vw]">
        <h3 class="text-[0.8vw] text-white font-semibold mb-[0.6vw]">基本信息</h3>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">资产名称 *</label>
          <select
            v-model="formData.assetName"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="">请选择要调拨的资产</option>
            <option v-for="asset in availableAssets" :key="asset.id" :value="asset.name"> {{ asset.name }} ({{ asset.code }}) </option>
          </select>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">调拨类型 *</label>
          <select
            v-model="formData.type"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="">请选择调拨类型</option>
            <option value="department">部门调拨</option>
            <option value="location">位置调拨</option>
            <option value="person">人员调拨</option>
          </select>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">申请人</label>
          <input
            v-model="formData.requester"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入申请人姓名"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">申请部门</label>
          <select
            v-model="formData.department"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="">请选择部门</option>
            <option value="IT">IT部门</option>
            <option value="HR">人力资源部</option>
            <option value="Finance">财务部</option>
            <option value="Operations">运营部</option>
          </select>
        </div>
      </div>

      <!-- 调拨信息 -->
      <div class="space-y-[0.8vw]">
        <h3 class="text-[0.8vw] text-white font-semibold mb-[0.6vw]">调拨信息</h3>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">调出方 *</label>
          <input
            v-model="formData.fromLocation"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入调出位置/部门"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">调入方 *</label>
          <input
            v-model="formData.toLocation"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入调入位置/部门"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">计划调拨时间</label>
          <input
            v-model="formData.plannedDate"
            type="date"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">紧急程度</label>
          <select
            v-model="formData.urgency"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="low">一般</option>
            <option value="medium">紧急</option>
            <option value="high">非常紧急</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 调拨原因 -->
    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">调拨原因 *</label>
      <textarea
        v-model="formData.reason"
        rows="4"
        class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400 resize-none"
        placeholder="请详细说明调拨原因和必要性"
      ></textarea>
    </div>

    <!-- 接收确认 -->
    <div class="bg-yellow-500/10 border border-yellow-500/20 rounded p-[0.8vw]">
      <h3 class="text-[0.7vw] text-yellow-400 font-semibold mb-[0.4vw]">接收确认</h3>
      <div class="grid grid-cols-2 gap-[0.8vw]">
        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">接收人</label>
          <input
            v-model="formData.receiver"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入接收人姓名"
          />
        </div>
        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">接收人联系方式</label>
          <input
            v-model="formData.receiverContact"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入联系电话或邮箱"
          />
        </div>
      </div>
    </div>

    <!-- 附件上传 -->
    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">相关附件</label>
      <div class="border-2 border-dashed border-white/20 rounded p-[1vw] text-center">
        <div class="text-[0.6vw] text-gray-400 mb-[0.4vw]"> 上传调拨相关文件（如调拨申请、审批文件等） </div>
        <div class="text-[0.5vw] text-gray-500"> 支持 PDF、DOC、XLS、JPG、PNG 格式 </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    formData: { type: Object, default: () => ({}) },
  });

  const emit = defineEmits(['update:formData']);

  const formData = ref({
    assetName: '',
    type: '',
    requester: '',
    department: '',
    fromLocation: '',
    toLocation: '',
    plannedDate: new Date().toISOString().split('T')[0],
    urgency: 'low',
    reason: '',
    receiver: '',
    receiverContact: '',
    attachments: [],
    ...props.formData,
  });

  // 模拟可调拨资产数据
  const availableAssets = ref([
    { id: 1, code: 'IT-2024-001', name: 'Dell PowerEdge R740服务器' },
    { id: 2, code: 'IT-2024-002', name: 'HP ProLiant DL380服务器' },
    { id: 3, code: 'IT-2024-003', name: '华为核心交换机' },
    { id: 4, code: 'IT-2024-004', name: 'Dell存储阵列' },
    { id: 5, code: 'IT-2024-005', name: 'UPS电源设备' },
  ]);

  watch(
    formData,
    (newValue) => {
      emit('update:formData', newValue);
    },
    { deep: true }
  );
</script>
