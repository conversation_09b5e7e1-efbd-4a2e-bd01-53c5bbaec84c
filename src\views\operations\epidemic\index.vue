<template>
  <div class="p-4">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <SafetyOutlined class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">绿码人数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.greenCodeCount }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <WarningOutlined class="h-8 w-8 text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">黄码人数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.yellowCodeCount }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <ExclamationCircleOutlined class="h-8 w-8 text-red-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">红码人数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.redCodeCount }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <FireOutlined class="h-8 w-8 text-orange-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">体温异常</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.abnormalTemperature }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 疫苗接种和核酸检测统计 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- 疫苗接种统计 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <MedicineBoxOutlined class="mr-2" />
          疫苗接种统计
        </h3>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">已接种人数</span>
            <span class="font-semibold">{{ stats.vaccinatedCount }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">接种率</span>
            <span class="font-semibold text-green-600">
              {{ ((stats.vaccinatedCount / stats.totalChecked) * 100).toFixed(1) }}%
            </span>
          </div>
        </div>
      </div>

      <!-- 核酸检测统计 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <ExperimentOutlined class="mr-2" />
          核酸检测统计
        </h3>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">已检测人数</span>
            <span class="font-semibold">{{ stats.nucleicTestCount }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">检测率</span>
            <span class="font-semibold text-blue-600">
              {{ ((stats.nucleicTestCount / stats.totalChecked) * 100).toFixed(1) }}%
            </span>
          </div>
        </div>
      </div>

      <!-- 风险人员统计 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <AlertOutlined class="mr-2" />
          风险人员统计
        </h3>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-500">风险人员</span>
            <span class="font-semibold text-red-600">{{ stats.riskPersonCount }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">告警数量</span>
            <span class="font-semibold text-orange-600">{{ stats.alertCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮和筛选 -->
    <div class="mb-4 flex justify-between items-center">
      <div class="flex space-x-2">
        <a-button type="primary" @click="showHealthCheckModal">
          <PlusOutlined />
          健康登记
        </a-button>
        <a-button @click="showTemperatureCheckModal">
          <FireOutlined />
          体温检测
        </a-button>
        <a-button @click="showControlMeasuresModal">
          <SettingOutlined />
          防控设置
        </a-button>
        <a-button @click="refreshData">
          <ReloadOutlined />
          刷新数据
        </a-button>
      </div>
      
      <div class="flex space-x-2">
        <a-input-search
          v-model:value="searchText"
          placeholder="搜索姓名或电话"
          style="width: 200px"
          @search="handleSearch"
        />
        <a-select
          v-model:value="healthCodeFilter"
          placeholder="健康码筛选"
          style="width: 120px"
          @change="handleSearch"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="green">绿码</a-select-option>
          <a-select-option value="yellow">黄码</a-select-option>
          <a-select-option value="red">红码</a-select-option>
        </a-select>
        <a-select
          v-model:value="employeeFilter"
          placeholder="人员类型"
          style="width: 120px"
          @change="handleSearch"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="true">员工</a-select-option>
          <a-select-option value="false">访客</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 健康检查记录列表 -->
    <div class="bg-white rounded-lg shadow">
      <a-table
        :columns="columns"
        :data-source="healthList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'healthCode'">
            <a-tag :color="getHealthCodeColor(record.healthCode)">
              {{ getHealthCodeText(record.healthCode) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'temperature'">
            <span :class="record.temperature > 37.3 ? 'text-red-500 font-bold' : 'text-green-500'">
              {{ record.temperature }}°C
            </span>
          </template>
          
          <template v-if="column.key === 'vaccineStatus'">
            <a-tag :color="getVaccineStatusColor(record.vaccineStatus)">
              {{ getVaccineStatusText(record.vaccineStatus) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'nucleicTestResult'">
            <a-tag v-if="record.nucleicTestResult" :color="getNucleicTestColor(record.nucleicTestResult)">
              {{ getNucleicTestText(record.nucleicTestResult) }}
            </a-tag>
            <span v-else>-</span>
          </template>
          
          <template v-if="column.key === 'isEmployee'">
            <a-tag :color="record.isEmployee ? 'blue' : 'green'">
              {{ record.isEmployee ? '员工' : '访客' }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'riskLevel'">
            <a-tag v-if="getRiskLevel(record)" :color="getRiskLevelColor(record)">
              {{ getRiskLevel(record) }}
            </a-tag>
            <span v-else class="text-green-500">正常</span>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button
                size="small"
                @click="viewHealthDetail(record)"
              >
                详情
              </a-button>
              
              <a-button
                v-if="getRiskLevel(record)"
                type="primary"
                danger
                size="small"
                @click="handleAlert(record)"
              >
                处理告警
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 健康登记弹窗 -->
    <HealthCheckModal
      v-model:visible="healthCheckModalVisible"
      @success="handleHealthCheckSuccess"
    />

    <!-- 体温检测弹窗 -->
    <TemperatureCheckModal
      v-model:visible="temperatureCheckModalVisible"
      @success="handleTemperatureCheckSuccess"
    />

    <!-- 防控措施设置弹窗 -->
    <ControlMeasuresModal
      v-model:visible="controlMeasuresModalVisible"
      @success="handleControlMeasuresSuccess"
    />

    <!-- 健康详情弹窗 -->
    <HealthDetailModal
      v-model:visible="healthDetailModalVisible"
      :health-info="selectedHealthInfo"
    />

    <!-- 告警处理弹窗 -->
    <AlertHandleModal
      v-model:visible="alertHandleModalVisible"
      :health-info="selectedHealthInfo"
      @success="handleAlertSuccess"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import {
    SafetyOutlined,
    WarningOutlined,
    ExclamationCircleOutlined,
    FireOutlined,
    MedicineBoxOutlined,
    ExperimentOutlined,
    AlertOutlined,
    PlusOutlined,
    SettingOutlined,
    ReloadOutlined,
  } from '@ant-design/icons-vue';
  import {
    getHealthList,
    getEpidemicStats,
    handleEpidemicAlert,
    type HealthInfo,
    type EpidemicStats,
  } from '/@/api/operations/epidemic';
  import HealthCheckModal from './components/HealthCheckModal.vue';
  import TemperatureCheckModal from './components/TemperatureCheckModal.vue';
  import ControlMeasuresModal from './components/ControlMeasuresModal.vue';
  import HealthDetailModal from './components/HealthDetailModal.vue';
  import AlertHandleModal from './components/AlertHandleModal.vue';

  // 响应式数据
  const loading = ref(false);
  const healthList = ref<HealthInfo[]>([]);
  const stats = ref<EpidemicStats>({
    totalChecked: 0,
    greenCodeCount: 0,
    yellowCodeCount: 0,
    redCodeCount: 0,
    abnormalTemperature: 0,
    vaccinatedCount: 0,
    nucleicTestCount: 0,
    riskPersonCount: 0,
    todayEntries: 0,
    alertCount: 0,
  });

  // 搜索和筛选
  const searchText = ref('');
  const healthCodeFilter = ref('');
  const employeeFilter = ref('');

  // 分页
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 弹窗状态
  const healthCheckModalVisible = ref(false);
  const temperatureCheckModalVisible = ref(false);
  const controlMeasuresModalVisible = ref(false);
  const healthDetailModalVisible = ref(false);
  const alertHandleModalVisible = ref(false);
  const selectedHealthInfo = ref<HealthInfo | null>(null);

  // 表格列定义
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
    },
    {
      title: '人员类型',
      key: 'isEmployee',
    },
    {
      title: '健康码',
      key: 'healthCode',
    },
    {
      title: '体温',
      key: 'temperature',
    },
    {
      title: '疫苗状态',
      key: 'vaccineStatus',
    },
    {
      title: '核酸结果',
      key: 'nucleicTestResult',
    },
    {
      title: '风险等级',
      key: 'riskLevel',
    },
    {
      title: '检查时间',
      dataIndex: 'checkTime',
      key: 'checkTime',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
    },
  ];

  // 方法
  const loadHealthList = async () => {
    loading.value = true;
    try {
      const params = {
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
        name: searchText.value || undefined,
        healthCode: healthCodeFilter.value || undefined,
        isEmployee: employeeFilter.value ? employeeFilter.value === 'true' : undefined,
      };
      
      const response = await getHealthList(params);
      healthList.value = response.records;
      pagination.total = response.total;
    } catch (error) {
      message.error('获取健康记录失败');
    } finally {
      loading.value = false;
    }
  };

  const loadStats = async () => {
    try {
      stats.value = await getEpidemicStats();
    } catch (error) {
      message.error('获取统计数据失败');
    }
  };

  const refreshData = () => {
    loadHealthList();
    loadStats();
  };

  const handleSearch = () => {
    pagination.current = 1;
    loadHealthList();
  };

  const handleTableChange = (pag: any) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    loadHealthList();
  };

  const showHealthCheckModal = () => {
    healthCheckModalVisible.value = true;
  };

  const showTemperatureCheckModal = () => {
    temperatureCheckModalVisible.value = true;
  };

  const showControlMeasuresModal = () => {
    controlMeasuresModalVisible.value = true;
  };

  const viewHealthDetail = (healthInfo: HealthInfo) => {
    selectedHealthInfo.value = healthInfo;
    healthDetailModalVisible.value = true;
  };

  const handleAlert = (healthInfo: HealthInfo) => {
    selectedHealthInfo.value = healthInfo;
    alertHandleModalVisible.value = true;
  };

  // 状态颜色和文本方法
  const getHealthCodeColor = (code: string) => {
    const colors = { green: 'green', yellow: 'orange', red: 'red' };
    return colors[code] || 'gray';
  };

  const getHealthCodeText = (code: string) => {
    const texts = { green: '绿码', yellow: '黄码', red: '红码' };
    return texts[code] || code;
  };

  const getVaccineStatusColor = (status: string) => {
    const colors = { none: 'red', partial: 'orange', full: 'green', booster: 'blue' };
    return colors[status] || 'gray';
  };

  const getVaccineStatusText = (status: string) => {
    const texts = { none: '未接种', partial: '部分接种', full: '完全接种', booster: '加强针' };
    return texts[status] || status;
  };

  const getNucleicTestColor = (result: string) => {
    const colors = { negative: 'green', positive: 'red', pending: 'orange' };
    return colors[result] || 'gray';
  };

  const getNucleicTestText = (result: string) => {
    const texts = { negative: '阴性', positive: '阳性', pending: '待出结果' };
    return texts[result] || result;
  };

  const getRiskLevel = (healthInfo: HealthInfo) => {
    if (healthInfo.healthCode === 'red' || healthInfo.temperature > 37.3) {
      return '高风险';
    }
    if (healthInfo.healthCode === 'yellow' || healthInfo.nucleicTestResult === 'positive') {
      return '中风险';
    }
    if (healthInfo.riskArea || (healthInfo.symptoms && healthInfo.symptoms.length > 0)) {
      return '低风险';
    }
    return '';
  };

  const getRiskLevelColor = (healthInfo: HealthInfo) => {
    const level = getRiskLevel(healthInfo);
    const colors = { '高风险': 'red', '中风险': 'orange', '低风险': 'yellow' };
    return colors[level] || 'gray';
  };

  // 事件处理方法
  const handleHealthCheckSuccess = () => {
    healthCheckModalVisible.value = false;
    refreshData();
  };

  const handleTemperatureCheckSuccess = () => {
    temperatureCheckModalVisible.value = false;
    refreshData();
  };

  const handleControlMeasuresSuccess = () => {
    controlMeasuresModalVisible.value = false;
    message.success('防控措施设置成功');
  };

  const handleAlertSuccess = () => {
    alertHandleModalVisible.value = false;
    refreshData();
  };

  // 生命周期
  onMounted(() => {
    refreshData();
  });
</script>
