<template>
  <div class="h-full flex flex-col">
    <!-- 操作工具栏 -->
    <div class="flex justify-between items-center mb-[1vw]">
      <div class="flex items-center space-x-[0.8vw]">
        <button
          class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
          @click="showApplicationDialog = true"
        >
          <PlusOutlined class="mr-[0.2vw]" />
          新建申请
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
          @click="batchApprove"
          :disabled="selectedApplications.length === 0"
        >
          <CheckOutlined class="mr-[0.2vw]" />
          批量审批
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors"
          @click="exportApplications"
        >
          <DownloadOutlined class="mr-[0.2vw]" />
          导出申请
        </button>
      </div>

      <div class="flex items-center space-x-[0.6vw]">
        <select v-model="statusFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部状态</option>
          <option value="pending">待审批</option>
          <option value="approved">已批准</option>
          <option value="rejected">已拒绝</option>
          <option value="allocated">已分配</option>
        </select>
        <select
          v-model="departmentFilter"
          class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none"
        >
          <option value="">全部部门</option>
          <option value="IT">IT部门</option>
          <option value="HR">人力资源部</option>
          <option value="Finance">财务部</option>
          <option value="Operations">运营部</option>
        </select>
        <input
          v-model="searchQuery"
          placeholder="搜索申请人、资产名称..."
          class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none w-[15vw]"
        />
        <button
          class="px-[0.6vw] py-[0.3vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors"
          @click="searchApplications"
        >
          <SearchOutlined />
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-black/20 rounded border border-white/10 overflow-hidden">
      <div class="overflow-auto h-full">
        <table class="w-full text-[0.6vw]">
          <thead class="bg-blue-500/20 sticky top-0">
            <tr>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">
                <input type="checkbox" @change="toggleSelectAll" class="mr-[0.4vw]" />
                申请编号
              </th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">申请人</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">部门</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">资产类型</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">申请数量</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">申请时间</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">状态</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="application in paginatedApplications" :key="application.id" class="hover:bg-white/5 transition-colors">
              <td class="p-[0.6vw] text-white border-b border-white/5">
                <input
                  type="checkbox"
                  :checked="selectedApplications.includes(application.id)"
                  @change="toggleSelectApplication(application.id)"
                  class="mr-[0.4vw]"
                />
                {{ application.applicationCode }}
              </td>
              <td class="p-[0.6vw] text-white border-b border-white/5">{{ application.applicant }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ application.department }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ application.assetType }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ application.quantity }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ application.applicationDate }}</td>
              <td class="p-[0.6vw] border-b border-white/5">
                <span
                  :class="[
                    'px-[0.4vw] py-[0.1vw] rounded text-[0.5vw]',
                    application.status === 'pending'
                      ? 'bg-yellow-500/20 text-yellow-400'
                      : application.status === 'approved'
                        ? 'bg-green-500/20 text-green-400'
                        : application.status === 'rejected'
                          ? 'bg-red-500/20 text-red-400'
                          : 'bg-blue-500/20 text-blue-400',
                  ]"
                >
                  {{ getStatusText(application.status) }}
                </span>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex space-x-[0.4vw]">
                  <button
                    v-if="application.status === 'pending'"
                    class="text-green-400 hover:text-green-300 text-[0.5vw] bg-transparent"
                    @click="approveApplication(application)"
                  >
                    审批
                  </button>
                  <button
                    v-if="application.status === 'approved'"
                    class="text-blue-400 hover:text-blue-300 text-[0.5vw] bg-transparent"
                    @click="allocateAssets(application)"
                  >
                    分配
                  </button>
                  <button class="text-orange-400 hover:text-orange-300 text-[0.5vw] bg-transparent" @click="viewApplicationDetail(application)">
                    详情
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-between items-center mt-[0.8vw] text-[0.6vw] text-gray-400">
      <div>共 {{ filteredApplications.length }} 条记录</div>
      <div class="flex items-center space-x-[0.4vw]">
        <button
          :disabled="currentPage === 1"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="text-white">{{ currentPage }} / {{ totalPages }}</span>
        <button
          :disabled="currentPage === totalPages"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage++"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 新建申请弹窗 -->
    <ModalDialog
      v-model:visible="showApplicationDialog"
      title="新建资产申请"
      width="60vw"
      :show-footer="true"
      @confirm="confirmApplication"
      @cancel="showApplicationDialog = false"
    >
      <AssetApplicationForm v-model:form-data="applicationForm" />
    </ModalDialog>

    <!-- 审批弹窗 -->
    <ModalDialog
      v-model:visible="showApprovalDialog"
      title="审批资产申请"
      width="50vw"
      :show-footer="true"
      @confirm="confirmApproval"
      @cancel="showApprovalDialog = false"
    >
      <ApprovalForm v-model:form-data="approvalForm" :application="selectedApplication" />
    </ModalDialog>

    <!-- 资产分配弹窗 -->
    <ModalDialog
      v-model:visible="showAllocationDialog"
      title="分配资产"
      width="70vw"
      :show-footer="true"
      @confirm="confirmAllocation"
      @cancel="showAllocationDialog = false"
    >
      <AssetAllocationForm v-model:form-data="allocationForm" :application="selectedApplication" />
    </ModalDialog>

    <!-- 申请详情弹窗 -->
    <ModalDialog v-model:visible="showDetailDialog" title="申请详情" width="70vw" :show-footer="false" @cancel="showDetailDialog = false">
      <ApplicationDetailView :application="selectedApplication" />
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { PlusOutlined, CheckOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import AssetApplicationForm from './forms/AssetApplicationForm.vue';
  import ApprovalForm from './forms/ApprovalForm.vue';
  import AssetAllocationForm from './forms/AssetAllocationForm.vue';
  import ApplicationDetailView from './forms/ApplicationDetailView.vue';

  // 响应式数据
  const applications = ref([]);
  const selectedApplications = ref([]);
  const selectedApplication = ref(null);
  const searchQuery = ref('');
  const statusFilter = ref('');
  const departmentFilter = ref('');
  const currentPage = ref(1);
  const pageSize = ref(20);
  const showApplicationDialog = ref(false);
  const showApprovalDialog = ref(false);
  const showAllocationDialog = ref(false);
  const showDetailDialog = ref(false);
  const applicationForm = ref({});
  const approvalForm = ref({});
  const allocationForm = ref({});

  // 计算属性
  const filteredApplications = computed(() => {
    let result = applications.value;

    if (statusFilter.value) {
      result = result.filter((app) => app.status === statusFilter.value);
    }

    if (departmentFilter.value) {
      result = result.filter((app) => app.department === departmentFilter.value);
    }

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(
        (app) =>
          app.applicant.toLowerCase().includes(query) ||
          app.assetType.toLowerCase().includes(query) ||
          app.applicationCode.toLowerCase().includes(query)
      );
    }

    return result;
  });

  const totalPages = computed(() => Math.ceil(filteredApplications.value.length / pageSize.value));

  const paginatedApplications = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return filteredApplications.value.slice(start, end);
  });

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      pending: '待审批',
      approved: '已批准',
      rejected: '已拒绝',
      allocated: '已分配',
    };
    return statusMap[status] || status;
  };

  const toggleSelectAll = (event) => {
    if (event.target.checked) {
      selectedApplications.value = paginatedApplications.value.map((app) => app.id);
    } else {
      selectedApplications.value = [];
    }
  };

  const toggleSelectApplication = (applicationId) => {
    const index = selectedApplications.value.indexOf(applicationId);
    if (index > -1) {
      selectedApplications.value.splice(index, 1);
    } else {
      selectedApplications.value.push(applicationId);
    }
  };

  const searchApplications = () => {
    currentPage.value = 1;
  };

  const approveApplication = (application) => {
    selectedApplication.value = application;
    showApprovalDialog.value = true;
  };

  const allocateAssets = (application) => {
    selectedApplication.value = application;
    showAllocationDialog.value = true;
  };

  const viewApplicationDetail = (application) => {
    selectedApplication.value = application;
    showDetailDialog.value = true;
  };

  const batchApprove = () => {
    if (selectedApplications.value.length === 0) {
      alert('请选择要批量审批的申请');
      return;
    }

    const pendingApplications = applications.value.filter((app) => selectedApplications.value.includes(app.id) && app.status === 'pending');

    if (pendingApplications.length === 0) {
      alert('所选申请中没有待审批的记录');
      return;
    }

    // 批量更新状态
    pendingApplications.forEach((app) => {
      app.status = 'approved';
      app.approveDate = new Date().toISOString().split('T')[0];
    });

    selectedApplications.value = [];
    alert(`批量审批成功！共审批 ${pendingApplications.length} 条申请`);
  };

  const exportApplications = () => {
    const exportData = filteredApplications.value.map((app) => ({
      申请编号: app.applicationCode,
      申请人: app.applicant,
      部门: app.department,
      资产类型: app.assetType,
      申请数量: app.quantity,
      申请时间: app.applicationDate,
      状态: getStatusText(app.status),
    }));

    const headers = Object.keys(exportData[0] || {});
    const csvContent = [headers.join(','), ...exportData.map((row) => headers.map((header) => `"${row[header] || ''}"`).join(','))].join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `资产申请数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert('申请数据导出成功！');
  };

  const confirmApplication = () => {
    // 表单验证
    if (!applicationForm.value.applicant || !applicationForm.value.department || !applicationForm.value.assetType || !applicationForm.value.reason) {
      alert('请填写必填字段：申请人、部门、资产类型、申请原因');
      return;
    }

    // 生成新的申请记录
    const newApplication = {
      id: Date.now(),
      applicationCode: `APP-${new Date().getFullYear()}-${String(applications.value.length + 1).padStart(3, '0')}`,
      applicant: applicationForm.value.applicant,
      department: applicationForm.value.department,
      assetType: applicationForm.value.assetType,
      quantity: applicationForm.value.quantity || 1,
      applicationDate: applicationForm.value.applicationDate,
      status: 'pending',
      reason: applicationForm.value.reason,
    };

    applications.value.unshift(newApplication);
    applicationForm.value = {};
    showApplicationDialog.value = false;

    alert('申请提交成功！');
  };

  const confirmApproval = () => {
    if (!approvalForm.value.result) {
      alert('请选择审批结果');
      return;
    }

    if (selectedApplication.value) {
      selectedApplication.value.status = approvalForm.value.result;
      selectedApplication.value.approveDate = new Date().toISOString().split('T')[0];
      selectedApplication.value.approveComments = approvalForm.value.comments;
    }

    approvalForm.value = {};
    showApprovalDialog.value = false;

    alert('审批完成！');
  };

  const confirmAllocation = () => {
    if (selectedApplication.value) {
      selectedApplication.value.status = 'allocated';
      selectedApplication.value.allocateDate = new Date().toISOString().split('T')[0];
    }

    allocationForm.value = {};
    showAllocationDialog.value = false;

    alert('资产分配完成！');
  };

  // 初始化数据
  onMounted(() => {
    applications.value = [
      {
        id: 1,
        applicationCode: 'APP-2024-001',
        applicant: '张三',
        department: 'IT',
        assetType: '笔记本电脑',
        quantity: 2,
        applicationDate: '2024-01-15',
        status: 'pending',
        reason: '新员工入职需要',
      },
      {
        id: 2,
        applicationCode: 'APP-2024-002',
        applicant: '李四',
        department: 'HR',
        assetType: '打印机',
        quantity: 1,
        applicationDate: '2024-01-16',
        status: 'approved',
        reason: '部门打印机损坏需要更换',
      },
    ];
  });
</script>
