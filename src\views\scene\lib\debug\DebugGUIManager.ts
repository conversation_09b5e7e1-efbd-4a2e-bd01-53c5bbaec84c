import * as THREE from 'three';
import * as dat from 'dat.gui';
import { SceneManager } from '../SceneManager';
import { CameraController } from '../CameraController';
import { RenderingPipeline } from '../RenderingPipeline';
import { LightingManager } from '../LightingManager';
import { SkyManager } from '../SkyManager';

// 定义调试配置接口
export interface DebugConfig {
  // 光照属性
  lighting: {
    ambient: {
      color: string;
      intensity: number;
    };
    directional: {
      color: string;
      intensity: number;
      position: {
        x: number;
        y: number;
        z: number;
      };
    };
  };
  // 相机设置
  camera: {
    fov: number;
    near: number;
    far: number;
    position: {
      x: number;
      y: number;
      z: number;
    };
  };
  // 渲染选项
  rendering: {
    shadows: boolean;
    shadowMapType: number;
    pixelRatio: number;
    antialias: boolean;
    toneMapping: number;
    toneMappingExposure: number;
  };
  // 性能选项
  performance: {
    frameRateLimit: number;
    frustumCulling: boolean;
  };
  // 天空设置
  sky: {
    visible: boolean;
    topColor: string;
    bottomColor: string;
    exponent: number;
  };
}

// 定义性能指标接口
export interface PerformanceMetrics {
  fps: number;
  renderTime: number;
  drawCalls: number;
  triangles: number;
  points: number;
  lines: number;
}

/**
 * 调试GUI管理器 - 用于调试和微调3D场景参数
 */
export class DebugGUIManager {
  private static instance: DebugGUIManager | null = null;
  private gui: dat.GUI | null = null;
  private sceneManager: SceneManager;
  private cameraController: CameraController;
  private renderingPipeline: RenderingPipeline;
  private lightingManager: LightingManager | null = null;
  private skyManager: SkyManager | null = null;

  // 当前场景类型
  private sceneType: 'interior' | 'exterior' = 'exterior';

  // 性能指标
  private performanceMetrics: PerformanceMetrics = {
    fps: 0,
    renderTime: 0,
    drawCalls: 0,
    triangles: 0,
    points: 0,
    lines: 0,
  };

  // 性能监控
  private statsUpdateInterval: number | null = null;

  // 配置对象
  private config: DebugConfig;

  // 文件夹引用
  private folders: {
    lighting: dat.GUI | null;
    camera: dat.GUI | null;
    rendering: dat.GUI | null;
    performance: dat.GUI | null;
    sky: dat.GUI | null;
  } = {
    lighting: null,
    camera: null,
    rendering: null,
    performance: null,
    sky: null,
  };

  // 控制器引用
  private controllers: {
    [key: string]: dat.GUIController | undefined;
  } = {};

  private constructor() {
    try {
      this.sceneManager = SceneManager.getInstance();

      // 检查场景是否已经初始化
      if (!this.sceneManager || !this.sceneManager.scene) {
        throw new Error('场景尚未初始化');
      }

      this.cameraController = CameraController.getInstance();
      this.renderingPipeline = RenderingPipeline.getInstance();
      this.lightingManager = LightingManager.getInstance();
      this.skyManager = SkyManager.getInstance();

      // 初始化配置，尝试从 localStorage 读取保存的配置
      this.config = this.getSavedOrDefaultConfig();
    } catch (error) {
      console.error('DebugGUIManager初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): DebugGUIManager {
    if (!DebugGUIManager.instance) {
      try {
        // 检查场景是否已经初始化
        const sceneManager = SceneManager.getInstance();
        if (!sceneManager || !sceneManager.scene) {
          console.warn('场景尚未初始化，无法创建DebugGUIManager实例');
          throw new Error('场景尚未初始化');
        }

        DebugGUIManager.instance = new DebugGUIManager();
      } catch (error) {
        console.error('创建DebugGUIManager实例失败:', error);
        throw error;
      }
    }
    return DebugGUIManager.instance;
  }

  /**
   * 获取保存的配置或默认配置
   * 优先从 localStorage 读取，并与默认配置合并，确保配置完整性
   */
  private getSavedOrDefaultConfig(): DebugConfig {
    const configKey = `debug_gui_config_${this.sceneType}`;
    const savedConfigString = localStorage.getItem(configKey);
    const defaultConfig = this.getDefaultConfig();

    if (savedConfigString) {
      try {
        const parsedConfig = JSON.parse(savedConfigString);
        // 深层合并以确保所有层级的配置都得到正确处理
        const mergedConfig: DebugConfig = {
          ...defaultConfig,
          ...parsedConfig,
          lighting: {
            ...defaultConfig.lighting,
            ...(parsedConfig.lighting || {}),
            ambient: { ...defaultConfig.lighting.ambient, ...(parsedConfig.lighting?.ambient || {}) },
            directional: {
              ...defaultConfig.lighting.directional,
              ...(parsedConfig.lighting?.directional || {}),
              position: { ...defaultConfig.lighting.directional.position, ...(parsedConfig.lighting?.directional?.position || {}) },
            },
          },
          camera: {
            ...defaultConfig.camera,
            ...(parsedConfig.camera || {}),
            position: { ...defaultConfig.camera.position, ...(parsedConfig.camera?.position || {}) },
          },
          rendering: { ...defaultConfig.rendering, ...(parsedConfig.rendering || {}) },
          performance: { ...defaultConfig.performance, ...(parsedConfig.performance || {}) },
          sky: { ...defaultConfig.sky, ...(parsedConfig.sky || {}) },
        };
        console.log(`[DebugGUIManager] 已从 localStorage 加载并合并 ${this.sceneType} 场景配置`);
        return mergedConfig;
      } catch (error) {
        console.error(`[DebugGUIManager] 解析或合并已保存的 ${this.sceneType} 配置失败:`, error);
        return defaultConfig;
      }
    }
    return defaultConfig;
  }

  /**
   * 初始化GUI
   */
  public init(): HTMLElement {
    if (this.gui) {
      this.destroy();
    }

    // 创建GUI实例
    this.gui = new dat.GUI({ autoPlace: false, width: 300 });
    this.gui.domElement.id = 'debug-gui';
    this.gui.domElement.style.position = 'absolute';
    this.gui.domElement.style.top = '60px';
    this.gui.domElement.style.right = '10px';
    this.gui.domElement.style.zIndex = '1000';

    // 确保配置基于当前的 sceneType 正确加载
    this.config = this.getSavedOrDefaultConfig();

    // 立即应用配置到场景
    this.applyConfigToScene();

    // 添加场景类型选择
    this.controllers.sceneType = this.gui
      .add({ sceneType: this.sceneType }, 'sceneType', ['interior', 'exterior'])
      .name('场景类型')
      .onChange((value) => {
        this.sceneType = value;
        this.updateGUIForSceneType();
      });

    // 创建文件夹
    this.createFolders();

    // 添加控制器
    this.addControllers();

    // 添加保存和加载按钮
    this.addSaveLoadButtons();

    // 开始性能监控
    this.startPerformanceMonitoring();

    // 返回GUI DOM元素
    return this.gui.domElement;
  }

  /**
   * 获取GUI DOM元素
   */
  public getDomElement(): HTMLElement | null {
    return this.gui ? this.gui.domElement : null;
  }

  /**
   * 销毁GUI
   */
  public destroy(): void {
    if (this.gui) {
      this.gui.destroy();
      this.gui = null;
    }

    // 停止性能监控
    if (this.statsUpdateInterval !== null) {
      window.clearInterval(this.statsUpdateInterval);
      this.statsUpdateInterval = null;
    }

    // 清空文件夹引用
    this.folders = {
      lighting: null,
      camera: null,
      rendering: null,
      performance: null,
      sky: null,
    };

    // 清空控制器引用
    this.controllers = {};
  }

  /**
   * 切换可见性
   */
  public toggle(): void {
    if (this.gui) {
      const isHidden = this.gui.domElement.style.display === 'none';
      this.gui.domElement.style.display = isHidden ? 'block' : 'none';
    }
  }

  /**
   * 设置场景类型
   */
  public setSceneType(type: 'interior' | 'exterior'): void {
    this.sceneType = type;
    if (this.controllers.sceneType) {
      (this.controllers.sceneType as dat.GUIController).setValue(type);
    }
    this.updateGUIForSceneType();
  }

  /**
   * 根据场景类型更新GUI
   */
  private updateGUIForSceneType(): void {
    // 根据场景类型尝试加载相应的配置
    const configKey = `debug_gui_config_${this.sceneType}`;
    const savedConfigString = localStorage.getItem(configKey);
    const defaultConfig = this.getDefaultConfig();

    if (savedConfigString) {
      try {
        const parsedConfig = JSON.parse(savedConfigString);
        // 深层合并
        const mergedConfig: DebugConfig = {
          ...defaultConfig,
          ...parsedConfig,
          lighting: {
            ...defaultConfig.lighting,
            ...(parsedConfig.lighting || {}),
            ambient: { ...defaultConfig.lighting.ambient, ...(parsedConfig.lighting?.ambient || {}) },
            directional: {
              ...defaultConfig.lighting.directional,
              ...(parsedConfig.lighting?.directional || {}),
              position: { ...defaultConfig.lighting.directional.position, ...(parsedConfig.lighting?.directional?.position || {}) },
            },
          },
          camera: {
            ...defaultConfig.camera,
            ...(parsedConfig.camera || {}),
            position: { ...defaultConfig.camera.position, ...(parsedConfig.camera?.position || {}) },
          },
          rendering: { ...defaultConfig.rendering, ...(parsedConfig.rendering || {}) },
          performance: { ...defaultConfig.performance, ...(parsedConfig.performance || {}) },
          sky: { ...defaultConfig.sky, ...(parsedConfig.sky || {}) },
        };
        this.config = mergedConfig;
        console.log(`[DebugGUIManager] 已加载并合并 ${this.sceneType} 场景配置`);
      } catch (error) {
        console.error(`[DebugGUIManager] 加载或合并 ${this.sceneType} 场景配置失败:`, error);
        this.config = defaultConfig;
      }
    } else {
      this.config = defaultConfig;
      console.log(`[DebugGUIManager] 未找到 ${this.sceneType} 场景配置，使用默认值`);
    }

    // 更新控制器值
    this.updateControllersFromConfig();

    // 应用配置到场景
    this.applyConfigToScene();

    // 更新天空控制器可见性
    if (this.folders.sky) {
      if (this.sceneType === 'exterior') {
        this.folders.sky.domElement.style.display = 'block';
      } else {
        this.folders.sky.domElement.style.display = 'none';
      }
    }

    console.log(`[DebugGUIManager] 已切换到${this.sceneType === 'interior' ? '室内' : '室外'}场景`);
  }

  /**
   * 更新相机位置
   */
  private updateCameraPosition(): void {
    // 获取当前相机位置
    const camera = this.cameraController.camera;

    // 更新配置中的相机位置
    this.config.camera.position.x = camera.position.x;
    this.config.camera.position.y = camera.position.y;
    this.config.camera.position.z = camera.position.z;
  }

  /**
   * 开始性能监控
   */
  private startPerformanceMonitoring(): void {
    // 清除之前的定时器
    if (this.statsUpdateInterval !== null) {
      window.clearInterval(this.statsUpdateInterval);
    }

    // 设置定时器，每秒更新一次性能指标
    this.statsUpdateInterval = window.setInterval(() => {
      // 更新FPS
      this.performanceMetrics.fps = this.renderingPipeline.getCurrentFps();

      // 获取渲染器信息
      const renderer = this.renderingPipeline.getRenderer();
      const info = renderer.info;

      // 更新渲染时间（假设值）
      this.performanceMetrics.renderTime = Math.round(1000 / this.performanceMetrics.fps);

      // 更新绘制调用次数
      this.performanceMetrics.drawCalls = info.render.calls;

      // 更新三角形数量
      this.performanceMetrics.triangles = info.render.triangles;

      // 更新点数量
      this.performanceMetrics.points = info.render.points;

      // 更新线数量
      this.performanceMetrics.lines = info.render.lines;

      // 更新相机位置（只读显示）
      const camera = this.cameraController.camera;
      this.config.camera.position.x = Math.round(camera.position.x * 100) / 100;
      this.config.camera.position.y = Math.round(camera.position.y * 100) / 100;
      this.config.camera.position.z = Math.round(camera.position.z * 100) / 100;
    }, 1000);
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): DebugConfig {
    return {
      lighting: {
        ambient: {
          color: '#ffffff',
          intensity: 0.8,
        },
        directional: {
          color: '#ffffff',
          intensity: 1.0,
          position: {
            x: 10,
            y: 10,
            z: 10,
          },
        },
      },
      camera: {
        fov: 75,
        near: 0.1,
        far: 1000,
        position: {
          x: 0,
          y: 0,
          z: 0,
        },
      },
      rendering: {
        shadows: false,
        shadowMapType: THREE.PCFSoftShadowMap,
        pixelRatio: window.devicePixelRatio,
        antialias: true,
        toneMapping: THREE.ACESFilmicToneMapping,
        toneMappingExposure: 0.9,
      },
      performance: {
        frameRateLimit: 60,
        frustumCulling: true,
      },
      sky: {
        visible: true,
        topColor: '#0077ff',
        bottomColor: '#e8f0ff',
        exponent: 0.6,
      },
    };
  }

  /**
   * 创建文件夹
   */
  private createFolders(): void {
    if (!this.gui) return;

    // 创建光照文件夹
    this.folders.lighting = this.gui.addFolder('光照属性');

    // 创建相机文件夹
    this.folders.camera = this.gui.addFolder('相机设置');

    // 创建渲染文件夹
    this.folders.rendering = this.gui.addFolder('渲染选项');

    // 创建性能文件夹
    this.folders.performance = this.gui.addFolder('性能指标');

    // 创建天空文件夹
    this.folders.sky = this.gui.addFolder('天空设置');

    // 默认展开光照和性能文件夹
    this.folders.lighting.open();
    this.folders.performance.open();
  }

  /**
   * 添加控制器
   */
  private addControllers(): void {
    this.addLightingControllers();
    this.addCameraControllers();
    this.addRenderingControllers();
    this.addPerformanceControllers();
    this.addSkyControllers();
  }

  /**
   * 添加光照控制器
   */
  private addLightingControllers(): void {
    if (!this.folders.lighting) return;

    // 环境光颜色
    this.controllers.ambientColor = this.folders.lighting
      .addColor(this.config.lighting.ambient, 'color')
      .name('环境光颜色')
      .onChange((value) => {
        if (this.sceneType === 'interior') {
          this.sceneManager.interiorLights.ambient.color.set(value);
        } else {
          this.sceneManager.exteriorLights.ambient.color.set(value);
        }
      });

    // 环境光强度
    this.controllers.ambientIntensity = this.folders.lighting
      .add(this.config.lighting.ambient, 'intensity', 0, 2)
      .name('环境光强度')
      .onChange((value) => {
        if (this.sceneType === 'interior') {
          this.sceneManager.interiorLights.ambient.intensity = value;
        } else {
          this.sceneManager.exteriorLights.ambient.intensity = value;
        }
      });

    // 方向光颜色
    this.controllers.directionalColor = this.folders.lighting
      .addColor(this.config.lighting.directional, 'color')
      .name('方向光颜色')
      .onChange((value) => {
        if (this.sceneType === 'interior') {
          this.sceneManager.interiorLights.directional.color.set(value);
        } else {
          this.sceneManager.exteriorLights.directional.color.set(value);
        }
      });

    // 方向光强度
    this.controllers.directionalIntensity = this.folders.lighting
      .add(this.config.lighting.directional, 'intensity', 0, 2)
      .name('方向光强度')
      .onChange((value) => {
        if (this.sceneType === 'interior') {
          this.sceneManager.interiorLights.directional.intensity = value;
        } else {
          this.sceneManager.exteriorLights.directional.intensity = value;
        }
      });

    // 方向光位置X
    this.controllers.directionalPosX = this.folders.lighting
      .add(this.config.lighting.directional.position, 'x', -100, 100)
      .name('方向光位置X')
      .onChange((value) => {
        this.updateDirectionalLightPosition();
      });

    // 方向光位置Y
    this.controllers.directionalPosY = this.folders.lighting
      .add(this.config.lighting.directional.position, 'y', 0, 100)
      .name('方向光位置Y')
      .onChange((value) => {
        this.updateDirectionalLightPosition();
      });

    // 方向光位置Z
    this.controllers.directionalPosZ = this.folders.lighting
      .add(this.config.lighting.directional.position, 'z', -100, 100)
      .name('方向光位置Z')
      .onChange((value) => {
        this.updateDirectionalLightPosition();
      });
  }

  /**
   * 更新方向光位置
   */
  private updateDirectionalLightPosition(): void {
    const pos = this.config.lighting.directional.position;
    if (this.sceneType === 'interior') {
      this.sceneManager.interiorLights.directional.position.set(pos.x, pos.y, pos.z);
    } else {
      this.sceneManager.exteriorLights.directional.position.set(pos.x, pos.y, pos.z);
    }
  }

  /**
   * 添加相机控制器
   */
  private addCameraControllers(): void {
    if (!this.folders.camera) return;

    // 视场角
    this.controllers.fov = this.folders.camera
      .add(this.config.camera, 'fov', 30, 120)
      .name('视场角(FOV)')
      .onChange((value) => {
        this.cameraController.camera.fov = value;
        this.cameraController.camera.updateProjectionMatrix();
      });

    // 近裁剪面
    this.controllers.near = this.folders.camera
      .add(this.config.camera, 'near', 0.01, 10)
      .name('近裁剪面')
      .onChange((value) => {
        this.cameraController.camera.near = value;
        this.cameraController.camera.updateProjectionMatrix();
      });

    // 远裁剪面
    this.controllers.far = this.folders.camera
      .add(this.config.camera, 'far', 100, 5000)
      .name('远裁剪面')
      .onChange((value) => {
        this.cameraController.camera.far = value;
        this.cameraController.camera.updateProjectionMatrix();
      });

    // 相机位置信息（只读）
    const cameraPositionFolder = this.folders.camera.addFolder('相机位置(只读)');

    this.controllers.cameraPosX = cameraPositionFolder.add(this.config.camera.position, 'x').name('X').listen();

    this.controllers.cameraPosY = cameraPositionFolder.add(this.config.camera.position, 'y').name('Y').listen();

    this.controllers.cameraPosZ = cameraPositionFolder.add(this.config.camera.position, 'z').name('Z').listen();
  }

  /**
   * 添加渲染控制器
   */
  private addRenderingControllers(): void {
    if (!this.folders.rendering) return;

    // 阴影开关
    this.controllers.shadows = this.folders.rendering
      .add(this.config.rendering, 'shadows')
      .name('启用阴影')
      .onChange((value) => {
        const renderer = this.renderingPipeline.getRenderer();
        renderer.shadowMap.enabled = value;

        // 更新场景中的光源阴影设置
        if (this.sceneType === 'interior') {
          this.sceneManager.interiorLights.directional.castShadow = value;
        } else {
          this.sceneManager.exteriorLights.directional.castShadow = value;
        }
      });

    // 阴影类型
    this.controllers.shadowMapType = this.folders.rendering
      .add(this.config.rendering, 'shadowMapType', {
        基础阴影: THREE.BasicShadowMap,
        PCF阴影: THREE.PCFShadowMap,
        PCF软阴影: THREE.PCFSoftShadowMap,
        VSM阴影: THREE.VSMShadowMap,
      })
      .name('阴影类型')
      .onChange((value) => {
        const renderer = this.renderingPipeline.getRenderer();
        renderer.shadowMap.type = value;
      });

    // 像素比例
    this.controllers.pixelRatio = this.folders.rendering
      .add(this.config.rendering, 'pixelRatio', 0.5, 2, 0.1)
      .name('像素比例')
      .onChange((value) => {
        const renderer = this.renderingPipeline.getRenderer();
        renderer.setPixelRatio(value);
      });

    // 色调映射
    this.controllers.toneMapping = this.folders.rendering
      .add(this.config.rendering, 'toneMapping', {
        无: THREE.NoToneMapping,
        线性: THREE.LinearToneMapping,
        Reinhard: THREE.ReinhardToneMapping,
        Cineon: THREE.CineonToneMapping,
        ACESFilmic: THREE.ACESFilmicToneMapping,
      })
      .name('色调映射')
      .onChange((value) => {
        const renderer = this.renderingPipeline.getRenderer();
        renderer.toneMapping = value;
      });

    // 曝光度
    this.controllers.toneMappingExposure = this.folders.rendering
      .add(this.config.rendering, 'toneMappingExposure', 0.1, 2)
      .name('曝光度')
      .onChange((value: number) => {
        // Explicitly declare the type of value
        const renderer = this.renderingPipeline.getRenderer();
        renderer.toneMappingExposure = value;
        this.config.rendering.toneMappingExposure = value; // Ensure the config is updated
      });
  }

  /**
   * 添加性能控制器
   */
  private addPerformanceControllers(): void {
    if (!this.folders.performance) return;

    // 帧率限制
    this.controllers.frameRateLimit = this.folders.performance
      .add(this.config.performance, 'frameRateLimit', [30, 60, 90, 120, 144])
      .name('帧率限制')
      .onChange((value) => {
        this.renderingPipeline.setFrameRateLimit(value);
      });

    // 视锥体剔除
    this.controllers.frustumCulling = this.folders.performance
      .add(this.config.performance, 'frustumCulling')
      .name('视锥体剔除')
      .onChange((value) => {
        // 在RenderingPipeline中应用视锥体剔除设置
        // 这里假设RenderingPipeline有一个方法来控制视锥体剔除
        // 如果没有，可以添加一个
      });

    // 性能指标（只读）
    const statsFolder = this.folders.performance.addFolder('性能指标(只读)');

    this.controllers.fps = statsFolder.add(this.performanceMetrics, 'fps').name('FPS').listen();

    this.controllers.renderTime = statsFolder.add(this.performanceMetrics, 'renderTime').name('渲染时间(ms)').listen();

    this.controllers.drawCalls = statsFolder.add(this.performanceMetrics, 'drawCalls').name('绘制调用').listen();

    this.controllers.triangles = statsFolder.add(this.performanceMetrics, 'triangles').name('三角形数').listen();

    // 默认展开性能指标文件夹
    statsFolder.open();
  }

  /**
   * 添加天空控制器
   */
  private addSkyControllers(): void {
    if (!this.folders.sky || !this.skyManager) return;

    // 天空可见性
    this.controllers.skyVisible = this.folders.sky
      .add(this.config.sky, 'visible')
      .name('显示天空')
      .onChange((value) => {
        if (value) {
          this.skyManager.showSky();
        } else {
          this.skyManager.hideSky();
        }
      });

    // 天空顶部颜色
    this.controllers.skyTopColor = this.folders.sky
      .addColor(this.config.sky, 'topColor')
      .name('顶部颜色')
      .onChange((value) => {
        if (this.skyManager.skyMat && this.skyManager.skyMat.uniforms) {
          this.skyManager.skyMat.uniforms.topColor.value.set(value);
        }
      });

    // 天空底部颜色
    this.controllers.skyBottomColor = this.folders.sky
      .addColor(this.config.sky, 'bottomColor')
      .name('底部颜色')
      .onChange((value) => {
        if (this.skyManager.skyMat && this.skyManager.skyMat.uniforms) {
          this.skyManager.skyMat.uniforms.bottomColor.value.set(value);
        }
      });

    // 天空渐变指数
    this.controllers.skyExponent = this.folders.sky
      .add(this.config.sky, 'exponent', 0.1, 1.0)
      .name('渐变指数')
      .onChange((value) => {
        if (this.skyManager.skyMat && this.skyManager.skyMat.uniforms) {
          this.skyManager.skyMat.uniforms.exponent.value = value;
        }
      });
  }

  /**
   * 添加保存和加载按钮
   */
  private addSaveLoadButtons(): void {
    if (!this.gui) return;

    // 保存配置按钮
    this.controllers.saveConfig = this.gui.add({ saveConfig: () => this.saveConfig() }, 'saveConfig').name('保存当前配置');

    // 加载配置按钮
    this.controllers.loadConfig = this.gui.add({ loadConfig: () => this.loadConfig() }, 'loadConfig').name('加载保存的配置');

    // 重置配置按钮
    this.controllers.resetConfig = this.gui.add({ resetConfig: () => this.resetConfig() }, 'resetConfig').name('重置为默认配置');
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      // 更新相机位置
      this.updateCameraPosition();

      // 获取光照设置
      const interiorLights = this.sceneManager.getInteriorLights();
      const exteriorLights = this.sceneManager.getExteriorLights();

      if (this.sceneType === 'interior' && interiorLights) {
        if (interiorLights.ambient) {
          this.config.lighting.ambient.color = `#${interiorLights.ambient.color.getHexString()}`;
          this.config.lighting.ambient.intensity = interiorLights.ambient.intensity;
        }
        if (interiorLights.directional) {
          this.config.lighting.directional.color = `#${interiorLights.directional.color.getHexString()}`;
          this.config.lighting.directional.intensity = interiorLights.directional.intensity;
          this.config.lighting.directional.position = {
            x: interiorLights.directional.position.x,
            y: interiorLights.directional.position.y,
            z: interiorLights.directional.position.z,
          };
        }
      } else if (this.sceneType === 'exterior' && exteriorLights) {
        if (exteriorLights.ambient) {
          this.config.lighting.ambient.color = `#${exteriorLights.ambient.color.getHexString()}`;
          this.config.lighting.ambient.intensity = exteriorLights.ambient.intensity;
        }
        if (exteriorLights.directional) {
          this.config.lighting.directional.color = `#${exteriorLights.directional.color.getHexString()}`;
          this.config.lighting.directional.intensity = exteriorLights.directional.intensity;
          this.config.lighting.directional.position = {
            x: exteriorLights.directional.position.x,
            y: exteriorLights.directional.position.y,
            z: exteriorLights.directional.position.z,
          };
        }
      }

      // 获取天空设置
      const skyMaterial = this.skyManager?.getSkyMaterial();
      if (skyMaterial && skyMaterial.uniforms) {
        this.config.sky.topColor = `#${skyMaterial.uniforms.topColor.value.getHexString()}`;
        this.config.sky.bottomColor = `#${skyMaterial.uniforms.bottomColor.value.getHexString()}`;
        this.config.sky.exponent = skyMaterial.uniforms.exponent.value;
      }

      // 保存配置到本地存储
      const configKey = `debug_gui_config_${this.sceneType}`;
      localStorage.setItem(configKey, JSON.stringify(this.config));

      console.log(`[DebugGUIManager] 已保存${this.sceneType}场景配置`);

      // 显示保存成功提示
      alert(`已成功保存${this.sceneType === 'interior' ? '室内' : '室外'}场景配置`);
    } catch (error) {
      console.error('[DebugGUIManager] 保存配置失败:', error);
      alert('保存配置失败，请查看控制台获取详细信息');
    }
  }

  /**
   * 加载配置
   */
  private loadConfig(): void {
    try {
      // 从本地存储加载配置
      const configKey = `debug_gui_config_${this.sceneType}`;
      const savedConfig = localStorage.getItem(configKey);

      if (!savedConfig) {
        alert(`未找到${this.sceneType === 'interior' ? '室内' : '室外'}场景的保存配置`);
        return;
      }

      // 解析配置
      const loadedConfig = JSON.parse(savedConfig) as DebugConfig;

      // 更新配置
      this.config = loadedConfig;

      // 更新GUI控制器
      this.updateControllersFromConfig();

      // 应用配置到场景
      this.applyConfigToScene();

      console.log(`[DebugGUIManager] 已加载${this.sceneType}场景配置`);

      // 显示加载成功提示
      alert(`已成功加载${this.sceneType === 'interior' ? '室内' : '室外'}场景配置`);
    } catch (error) {
      console.error('[DebugGUIManager] 加载配置失败:', error);
      alert('加载配置失败，请查看控制台获取详细信息');
    }
  }

  /**
   * 重置配置
   */
  private resetConfig(): void {
    // 重置为默认配置
    this.config = this.getDefaultConfig();

    // 更新GUI控制器
    this.updateControllersFromConfig();

    // 应用配置到场景
    this.applyConfigToScene();

    console.log('[DebugGUIManager] 已重置为默认配置');

    // 显示重置成功提示
    alert('已重置为默认配置');
  }

  /**
   * 更新控制器显示的值
   */
  private updateControllersFromConfig(): void {
    try {
      // 更新控制器值
      Object.entries(this.controllers).forEach(([key, controller]) => {
        if (controller && controller.updateDisplay) {
          controller.updateDisplay();
        }
      });

      // 对于特定控制器，需要手动设置值
      if (this.controllers.ambientColor) {
        this.controllers.ambientColor.setValue(this.config.lighting.ambient.color);
      }

      if (this.controllers.ambientIntensity) {
        this.controllers.ambientIntensity.setValue(this.config.lighting.ambient.intensity);
      }

      if (this.controllers.directionalColor) {
        this.controllers.directionalColor.setValue(this.config.lighting.directional.color);
      }

      if (this.controllers.directionalIntensity) {
        this.controllers.directionalIntensity.setValue(this.config.lighting.directional.intensity);
      }

      if (this.controllers.directionalPosX) {
        this.controllers.directionalPosX.setValue(this.config.lighting.directional.position.x);
      }

      if (this.controllers.directionalPosY) {
        this.controllers.directionalPosY.setValue(this.config.lighting.directional.position.y);
      }

      if (this.controllers.directionalPosZ) {
        this.controllers.directionalPosZ.setValue(this.config.lighting.directional.position.z);
      }

      // 相机控制器
      if (this.controllers.fov) {
        this.controllers.fov.setValue(this.config.camera.fov);
      }

      if (this.controllers.near) {
        this.controllers.near.setValue(this.config.camera.near);
      }

      if (this.controllers.far) {
        this.controllers.far.setValue(this.config.camera.far);
      }

      // 渲染控制器
      if (this.controllers.shadows) {
        this.controllers.shadows.setValue(this.config.rendering.shadows);
      }

      if (this.controllers.shadowMapType) {
        this.controllers.shadowMapType.setValue(this.config.rendering.shadowMapType);
      }

      if (this.controllers.pixelRatio) {
        this.controllers.pixelRatio.setValue(this.config.rendering.pixelRatio);
      }

      if (this.controllers.toneMapping) {
        this.controllers.toneMapping.setValue(this.config.rendering.toneMapping);
      }

      if (this.controllers.toneMappingExposure) {
        this.controllers.toneMappingExposure.setValue(this.config.rendering.toneMappingExposure);
      }

      // 性能控制器
      if (this.controllers.frameRateLimit) {
        this.controllers.frameRateLimit.setValue(this.config.performance.frameRateLimit);
      }

      if (this.controllers.frustumCulling) {
        this.controllers.frustumCulling.setValue(this.config.performance.frustumCulling);
      }

      // 天空控制器
      if (this.controllers.skyVisible) {
        this.controllers.skyVisible.setValue(this.config.sky.visible);
      }

      if (this.controllers.skyTopColor) {
        this.controllers.skyTopColor.setValue(this.config.sky.topColor);
      }

      if (this.controllers.skyBottomColor) {
        this.controllers.skyBottomColor.setValue(this.config.sky.bottomColor);
      }

      if (this.controllers.skyExponent) {
        this.controllers.skyExponent.setValue(this.config.sky.exponent);
      }
    } catch (error) {
      console.error('[DebugGUIManager] 更新控制器显示值失败:', error);
    }
  }

  /**
   * 应用配置到场景
   */
  private applyConfigToScene(): void {
    try {
      // 优化渲染器配置应用
      const renderer = this.renderingPipeline.getRenderer();
      if (renderer) {
        // 直接应用渲染配置，不依赖控制器
        renderer.shadowMap.enabled = this.config.rendering.shadows;
        renderer.shadowMap.type = this.config.rendering.shadowMapType;
        renderer.setPixelRatio(this.config.rendering.pixelRatio);
        renderer.toneMapping = this.config.rendering.toneMapping;
        renderer.toneMappingExposure = this.config.rendering.toneMappingExposure;

        // 移除强制渲染调用，避免代理错误
        // renderer.render(this.sceneManager.scene, this.cameraController.camera);
      }

      // 应用光照设置
      if (this.sceneType === 'interior') {
        if (this.sceneManager.interiorLights) {
          // 环境光
          if (this.sceneManager.interiorLights.ambient) {
            this.sceneManager.interiorLights.ambient.color.set(this.config.lighting.ambient.color);
            this.sceneManager.interiorLights.ambient.intensity = this.config.lighting.ambient.intensity;
          }

          // 方向光
          if (this.sceneManager.interiorLights.directional) {
            this.sceneManager.interiorLights.directional.color.set(this.config.lighting.directional.color);
            this.sceneManager.interiorLights.directional.intensity = this.config.lighting.directional.intensity;
            this.sceneManager.interiorLights.directional.position.set(
              this.config.lighting.directional.position.x,
              this.config.lighting.directional.position.y,
              this.config.lighting.directional.position.z
            );
          }
        }
      } else {
        if (this.sceneManager.exteriorLights) {
          // 环境光
          if (this.sceneManager.exteriorLights.ambient) {
            this.sceneManager.exteriorLights.ambient.color.set(this.config.lighting.ambient.color);
            this.sceneManager.exteriorLights.ambient.intensity = this.config.lighting.ambient.intensity;
          }

          // 方向光
          if (this.sceneManager.exteriorLights.directional) {
            this.sceneManager.exteriorLights.directional.color.set(this.config.lighting.directional.color);
            this.sceneManager.exteriorLights.directional.intensity = this.config.lighting.directional.intensity;
            this.sceneManager.exteriorLights.directional.position.set(
              this.config.lighting.directional.position.x,
              this.config.lighting.directional.position.y,
              this.config.lighting.directional.position.z
            );
          }
        }
      }

      // 应用相机设置
      if (this.cameraController && this.cameraController.camera) {
        this.cameraController.camera.fov = this.config.camera.fov;
        this.cameraController.camera.near = this.config.camera.near;
        this.cameraController.camera.far = this.config.camera.far;
        this.cameraController.camera.updateProjectionMatrix();
      }

      // 应用性能设置
      this.renderingPipeline.setFrameRateLimit(this.config.performance.frameRateLimit);

      // 更新天空设置
      if (this.skyManager) {
        if (this.sceneType === 'interior') {
          // 内景模式：完全隐藏天空和云朵
          this.skyManager.hideSky();
          console.log('[DebugGUIManager] 内景模式：已隐藏天空元素');
        } else if (this.sceneType === 'exterior') {
          if (this.config.sky.visible) {
            this.skyManager.showSky();
            this.skyManager.updateSkyColors(
              new THREE.Color(this.config.sky.topColor),
              new THREE.Color(this.config.sky.bottomColor),
              this.config.sky.exponent
            );
            console.log('[DebugGUIManager] 外景模式：已显示天空元素');
          } else {
            this.skyManager.hideSky();
            console.log('[DebugGUIManager] 外景模式：已隐藏天空元素（用户设置）');
          }
        }
      }

      console.log(`[DebugGUIManager] 已成功应用${this.sceneType}场景配置`);

      // 添加一个延迟触发场景更新的机制
      setTimeout(() => {
        this.sceneManager.forceUpdate();
      }, 50);
    } catch (error) {
      console.error('[DebugGUIManager] 应用配置到场景时出错:', error);
    }
  }
}
