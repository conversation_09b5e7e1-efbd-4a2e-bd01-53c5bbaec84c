<template>
  <div class="h-full flex flex-col">
    <!-- 操作工具栏 -->
    <div class="flex justify-between items-center mb-[1vw]">
      <div class="flex items-center space-x-[0.8vw]">
        <button
          class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
          @click="showAddDialog = true"
        >
          <PlusOutlined class="mr-[0.2vw]" />
          新增入库
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
          @click="showBatchImportDialog = true"
        >
          <UploadOutlined class="mr-[0.2vw]" />
          批量导入
        </button>
        <button class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors" @click="exportData">
          <DownloadOutlined class="mr-[0.2vw]" />
          导出数据
        </button>
      </div>

      <div class="flex items-center space-x-[0.6vw]">
        <select v-model="statusFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部状态</option>
          <option value="pending">待验收</option>
          <option value="accepted">已验收</option>
          <option value="rejected">已拒收</option>
        </select>
        <input
          v-model="searchQuery"
          placeholder="搜索资产名称、型号、序列号..."
          class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none w-[15vw]"
        />
        <button class="px-[0.6vw] py-[0.3vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors" @click="searchAssets">
          <SearchOutlined />
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-black/20 rounded border border-white/10 overflow-hidden">
      <div class="overflow-auto h-full">
        <table class="w-full text-[0.6vw]">
          <thead class="bg-blue-500/20 sticky top-0">
            <tr>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">
                <input type="checkbox" @change="toggleSelectAll" class="mr-[0.4vw]" />
                资产编号
              </th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">资产名称</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">型号规格</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">序列号</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">供应商</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">入库时间</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">状态</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="asset in paginatedAssets" :key="asset.id" class="hover:bg-white/5 transition-colors">
              <td class="p-[0.6vw] text-white border-b border-white/5">
                <input type="checkbox" :checked="selectedAssets.includes(asset.id)" @change="toggleSelectAsset(asset.id)" class="mr-[0.4vw]" />
                {{ asset.assetCode }}
              </td>
              <td class="p-[0.6vw] text-white border-b border-white/5">{{ asset.name }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ asset.model }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ asset.serialNumber }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ asset.supplier }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ asset.inboundDate }}</td>
              <td class="p-[0.6vw] border-b border-white/5">
                <span
                  :class="[
                    'px-[0.4vw] py-[0.1vw] rounded text-[0.5vw]',
                    asset.status === 'pending'
                      ? 'bg-yellow-500/20 text-yellow-400'
                      : asset.status === 'accepted'
                        ? 'bg-green-500/20 text-green-400'
                        : 'bg-red-500/20 text-red-400',
                  ]"
                >
                  {{ getStatusText(asset.status) }}
                </span>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex space-x-[0.4vw]">
                  <button
                    v-if="asset.status === 'pending'"
                    class="text-green-400 hover:text-green-300 text-[0.5vw] bg-transparent"
                    @click="acceptAsset(asset)"
                  >
                    验收
                  </button>
                  <button class="text-blue-400 hover:text-blue-300 text-[0.5vw] bg-transparent" @click="viewAssetDetail(asset)"> 详情 </button>
                  <button class="text-orange-400 hover:text-orange-300 text-[0.5vw] bg-transparent" @click="editAsset(asset)"> 编辑 </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-between items-center mt-[0.8vw] text-[0.6vw] text-gray-400">
      <div>共 {{ filteredAssets.length }} 条记录</div>
      <div class="flex items-center space-x-[0.4vw]">
        <button
          :disabled="currentPage === 1"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="text-white">{{ currentPage }} / {{ totalPages }}</span>
        <button
          :disabled="currentPage === totalPages"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage++"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 新增入库弹窗 -->
    <ModalDialog
      v-model:visible="showAddDialog"
      title="新增入库资产"
      width="60vw"
      :show-footer="true"
      :showDefaultButtons="true"
      @confirm="confirmAdd"
      @cancel="showAddDialog = false"
    >
      <AssetInboundForm v-model:form-data="addForm" />
    </ModalDialog>

    <!-- 批量导入弹窗 -->
    <ModalDialog
      v-model:visible="showBatchImportDialog"
      title="批量导入资产"
      width="50vw"
      :show-footer="true"
      @confirm="confirmBatchImport"
      @cancel="showBatchImportDialog = false"
    >
      <BatchImportForm v-model:file="importFile" />
    </ModalDialog>

    <!-- 资产详情弹窗 -->
    <ModalDialog v-model:visible="showDetailDialog" title="资产详情" width="70vw" :show-footer="false" @cancel="showDetailDialog = false">
      <AssetDetailView :asset="selectedAsset" />
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { PlusOutlined, UploadOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import AssetInboundForm from './forms/AssetInboundForm.vue';
  import BatchImportForm from './forms/BatchImportForm.vue';
  import AssetDetailView from './forms/AssetDetailView.vue';

  // 响应式数据
  const assets = ref([]);
  const selectedAssets = ref([]);
  const selectedAsset = ref(null);
  const searchQuery = ref('');
  const statusFilter = ref('');
  const currentPage = ref(1);
  const pageSize = ref(20);
  const showAddDialog = ref(false);
  const showBatchImportDialog = ref(false);
  const showDetailDialog = ref(false);
  const addForm = ref({});
  const importFile = ref(null);

  // 计算属性
  const filteredAssets = computed(() => {
    let result = assets.value;

    if (statusFilter.value) {
      result = result.filter((asset) => asset.status === statusFilter.value);
    }

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(
        (asset) =>
          asset.name.toLowerCase().includes(query) ||
          asset.model.toLowerCase().includes(query) ||
          asset.serialNumber.toLowerCase().includes(query) ||
          asset.assetCode.toLowerCase().includes(query)
      );
    }

    return result;
  });

  const totalPages = computed(() => Math.ceil(filteredAssets.value.length / pageSize.value));

  const paginatedAssets = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return filteredAssets.value.slice(start, end);
  });

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      pending: '待验收',
      accepted: '已验收',
      rejected: '已拒收',
    };
    return statusMap[status] || status;
  };

  const toggleSelectAll = (event) => {
    if (event.target.checked) {
      selectedAssets.value = paginatedAssets.value.map((asset) => asset.id);
    } else {
      selectedAssets.value = [];
    }
  };

  const toggleSelectAsset = (assetId) => {
    const index = selectedAssets.value.indexOf(assetId);
    if (index > -1) {
      selectedAssets.value.splice(index, 1);
    } else {
      selectedAssets.value.push(assetId);
    }
  };

  const searchAssets = () => {
    currentPage.value = 1;
  };

  const acceptAsset = (asset) => {
    asset.status = 'accepted';
    asset.acceptDate = new Date().toISOString().split('T')[0];
  };

  const viewAssetDetail = (asset) => {
    selectedAsset.value = asset;
    showDetailDialog.value = true;
  };

  const editAsset = (asset) => {
    // 编辑资产
    console.log('编辑资产:', asset);
  };

  const confirmAdd = () => {
    // 表单验证
    if (!addForm.value.name || !addForm.value.type || !addForm.value.supplier || !addForm.value.inboundDate) {
      alert('请填写必填字段：资产名称、资产类型、供应商、入库日期');
      return;
    }

    // 生成新的资产记录
    const newAsset = {
      id: Date.now(),
      assetCode: `IT-${new Date().getFullYear()}-${String(assets.value.length + 1).padStart(3, '0')}`,
      name: addForm.value.name,
      model: addForm.value.model || '-',
      serialNumber: addForm.value.serialNumber || `SN${Date.now()}`,
      supplier: addForm.value.supplier,
      inboundDate: addForm.value.inboundDate,
      status: 'pending',
    };

    // 添加到资产列表
    assets.value.unshift(newAsset);

    // 重置表单
    addForm.value = {};
    showAddDialog.value = false;

    // 显示成功消息
    alert('资产入库成功！');
  };

  const confirmBatchImport = () => {
    if (!importFile.value) {
      alert('请选择要导入的文件');
      return;
    }

    // 模拟批量导入处理
    const importCount = Math.floor(Math.random() * 10) + 5; // 5-14条记录
    const newAssets = [];

    for (let i = 0; i < importCount; i++) {
      newAssets.push({
        id: Date.now() + i,
        assetCode: `IT-${new Date().getFullYear()}-${String(assets.value.length + i + 1).padStart(3, '0')}`,
        name: `批量导入设备-${i + 1}`,
        model: `Model-${Math.floor(Math.random() * 1000)}`,
        serialNumber: `SN${Date.now() + i}`,
        supplier: '批量导入供应商',
        inboundDate: new Date().toISOString().split('T')[0],
        status: 'pending',
      });
    }

    // 添加到资产列表
    assets.value.unshift(...newAssets);

    // 重置
    importFile.value = null;
    showBatchImportDialog.value = false;

    // 显示成功消息
    alert(`批量导入成功！共导入 ${importCount} 条资产记录`);
  };

  const exportData = () => {
    // 准备导出数据
    const exportData = filteredAssets.value.map((asset) => ({
      资产编号: asset.assetCode,
      资产名称: asset.name,
      型号规格: asset.model,
      序列号: asset.serialNumber,
      供应商: asset.supplier,
      入库时间: asset.inboundDate,
      状态: getStatusText(asset.status),
    }));

    // 转换为CSV格式
    const headers = Object.keys(exportData[0] || {});
    const csvContent = [headers.join(','), ...exportData.map((row) => headers.map((header) => `"${row[header] || ''}"`).join(','))].join('\n');

    // 创建下载链接
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `资产入库数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert('数据导出成功！');
  };

  // 初始化数据
  onMounted(() => {
    // 模拟数据
    assets.value = [
      {
        id: 1,
        assetCode: 'IT-2024-001',
        name: 'Dell PowerEdge R740服务器',
        model: 'PowerEdge R740',
        serialNumber: 'SN123456789',
        supplier: '戴尔科技',
        inboundDate: '2024-01-15',
        status: 'pending',
      },
      {
        id: 2,
        assetCode: 'IT-2024-002',
        name: 'HP ProLiant DL380服务器',
        model: 'ProLiant DL380 Gen10',
        serialNumber: 'SN987654321',
        supplier: '惠普企业',
        inboundDate: '2024-01-16',
        status: 'accepted',
      },
      // 更多模拟数据...
    ];
  });
</script>
