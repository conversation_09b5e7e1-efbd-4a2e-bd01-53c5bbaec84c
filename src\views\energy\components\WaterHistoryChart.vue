<template>
  <a-row>
    <a-col :span="24">
      <div class="relative w-full">
        <div class="flex justify-between items-center mb-4">
          <span class="text-base">历史用水量趋势</span>
          <a-button type="primary" size="small" @click="handleRefresh" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </div>
        <div ref="chartRef" style="width: 100%; height: 50vh"></div>
      </div>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { debounce } from 'lodash-es';
  import * as echarts from 'echarts';
  import { getWaterMeterHistory } from '/@/api/energy/water';
  import { ReloadOutlined } from '@ant-design/icons-vue';

  const waterHistoryRawData = ref<Record<string, any[]>>({});
  const availableMeters = ref<string[]>([]);
  const chartRef = ref<HTMLElement | null>(null);
  let chartInstance: echarts.ECharts | null = null;
  const loading = ref(false);

  // 渲染图表
  const renderChart = () => {
    if (!chartInstance || !chartRef.value) return;

    if (availableMeters.value.length === 0) {
      return;
    }

    // 获取所有日期
    const allDates = new Set<string>();
    Object.values(waterHistoryRawData.value).forEach((meterData) => {
      meterData.forEach((record) => {
        if (record.dataTime) {
          const date = record.dataTime.split(' ')[0];
          allDates.add(date);
        }
      });
    });

    // 按日期排序
    const sortedDates = Array.from(allDates).sort();

    // 构建系列数据
    const series: any[] = [];
    availableMeters.value.forEach((meterName) => {
      if (!waterHistoryRawData.value[meterName]) return;

      // 创建该水表的数据映射
      const meterDataMap = new Map<string, number>();
      waterHistoryRawData.value[meterName].forEach((record) => {
        const date = record.dataTime.split(' ')[0];
        const value = parseFloat(record.valueData) || 0;
        meterDataMap.set(date, value);
      });

      // 创建该水表的折线图数据
      const data = sortedDates.map((date) => (meterDataMap.has(date) ? meterDataMap.get(date) : 0));

      series.push({
        name: meterName,
        type: 'line',
        data: data,
        smooth: true,
        symbol: 'circle',
        symbolSize: 7, // 略微增大标记点
        sampling: 'average', // 或者 'lttb' 优化大量数据点
        lineStyle: {
          width: 2.5,
          shadowColor: 'rgba(0,0,0,0.3)',
          shadowBlur: 5,
          shadowOffsetY: 2,
        },
        areaStyle: {
          opacity: 0.3, // ECharts 会根据线条颜色自动创建渐变
        },
        emphasis: {
          focus: 'series',
          lineStyle: {
            width: 3.5,
          },
          itemStyle: {
            // 强调数据点
            borderWidth: 2,
            borderColor: '#fff',
            shadowBlur: 5,
            shadowColor: 'rgba(0,0,0,0.3)',
          },
        },
        label: {
          show: false,
        },
      });
    });

    // 设置图表选项并渲染
    chartInstance.setOption({
      title: {
        show: false,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: { backgroundColor: '#6a7985' },
          lineStyle: { color: '#555', width: 1, type: 'solid' },
          crossStyle: { color: '#555', width: 1, type: 'solid' },
        },
        backgroundColor: 'rgba(50,50,50,0.8)',
        borderColor: '#333',
        textStyle: { color: '#fff' },
        formatter: function (params: any) {
          let res = params[0].name + '<br/>';
          params.forEach(function (item: any) {
            res += item.marker + item.seriesName + ' : ' + item.value + ' 吨<br/>';
          });
          return res;
        },
      },
      legend: {
        type: 'scroll',
        data: availableMeters.value,
        bottom: '6%', // 为 dataZoom 留出空间
        textStyle: {
          color: '#333',
          fontSize: 12,
        },
        itemGap: 15,
        selected: availableMeters.value.reduce(
          (acc, meter) => {
            acc[meter] = true; // 默认全部显示
            return acc;
          },
          {} as Record<string, boolean>
        ),
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%', // 增加底部边距以容纳 legend 和 dataZoom
        containLabel: true,
        backgroundColor: '#f9f9f9',
        borderColor: '#eee',
        show: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: sortedDates,
        axisLabel: {
          interval: 'auto',
          rotate: 0,
          margin: 10,
          formatter: function (value: string) {
            return value;
          },
          hideOverlap: true,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        name: '用水量(吨)',
        nameTextStyle: {
          padding: [0, 30, 0, 0],
        },
        splitLine: {
          lineStyle: {
            color: '#e0e0e0',
            type: 'dashed',
          },
        },
      },
      series: series,
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'],
      dataZoom: [
        {
          type: 'slider',
          xAxisIndex: 0,
          filterMode: 'filter',
          start: 0,
          end: 100,
          bottom: '1%',
          height: 20,
          dataBackground: {
            lineStyle: { color: '#ddd' },
            areaStyle: { color: '#eee' },
          },
          selectedDataBackground: {
            lineStyle: { color: '#83bff6' },
            areaStyle: { color: '#83bff6', opacity: 0.4 },
          },
          fillerColor: 'rgba(131, 191, 246, 0.2)',
          borderColor: '#ddd',
          handleIcon:
            'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: { color: '#fff', shadowBlur: 3, shadowColor: 'rgba(0, 0, 0, 0.6)', shadowOffsetX: 2, shadowOffsetY: 2 },
          textStyle: { color: '#333' },
        },
        {
          type: 'inside',
          xAxisIndex: 0,
          filterMode: 'filter',
        },
      ],
    });
  };

  // 初始化图表实例
  const initChart = () => {
    if (chartRef.value) {
      chartInstance = echarts.init(chartRef.value);
      window.addEventListener('resize', handleResize);
    }
  };

  // 处理窗口大小变化
  const handleResize = debounce(() => {
    if (chartInstance) {
      chartInstance.resize();
    }
  }, 300);

  // 加载数据
  const loadData = debounce(async () => {
    try {
      loading.value = true;
      const result = await getWaterMeterHistory();
      if (result && typeof result === 'object') {
        waterHistoryRawData.value = result;
        availableMeters.value = Object.keys(result);
        renderChart();
      } else {
        throw new Error('数据格式错误');
      }
    } catch (error) {
      console.error('水表历史数据加载错误:', error);
      message.error('加载水表历史数据失败');
      waterHistoryRawData.value = {};
    } finally {
      loading.value = false;
    }
  }, 500);

  const handleRefresh = () => {
    if (!loading.value) {
      loadData();
    }
  };

  onMounted(() => {
    initChart();
    loadData();
  });

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
    window.removeEventListener('resize', handleResize);
  });

  defineExpose({
    loadData,
    refresh: loadData,
    loading,
  });
</script>
