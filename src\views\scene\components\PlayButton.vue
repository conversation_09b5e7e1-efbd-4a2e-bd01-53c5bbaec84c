<template>
  <div class="relative">
    <!-- 播放工具栏 -->
    <PlayToolbar ref="playToolbarRef" />

    <!-- 播放主按钮 -->
    <a-tooltip placement="right" :title="getTooltip">
      <div
        class="w-[2.2vw] h-[2.2vw] bg-[rgba(23,43,77,0.8)] border border-[rgba(36,108,249,0.3)] rounded-[0.3vw] flex flex-col items-center justify-center cursor-pointer text-white transition-all duration-300 relative overflow-hidden hover:(bg-[rgba(36,108,249,0.2)] border-[rgba(36,108,249,0.5)] scale-105) before:content-empty before:absolute before:left-[-100%] before:top-[-50%] before:w-[200%] before:h-[200%] before:bg-gradient-to-r before:from-transparent before:via-[rgba(36,108,249,0.3)] before:to-transparent before:rotate-45 hover:before:animate-scan"
        :class="{
          'active bg-[rgba(36,108,249,0.3)] border-[rgba(36,108,249,0.8)] shadow-[0_0_10px_rgba(36,108,249,0.3)]': isPlaying || isToolbarExpanded,
          'opacity-50 cursor-not-allowed': isProcessing || globalThreeStore.isFloorSwitching || globalThreeStore.isPatrolActive,
        }"
        @click="handlePlayButtonClick"
        data-view-control="play"
      >
        <div class="flex flex-col items-center justify-center h-full">
          <LoadingOutlined v-if="isProcessing" class="text-[0.8vw]" />
          <template v-else>
            <component :is="getPlayIcon" class="text-[0.8vw]" />
            <span class="text-[0.5vw] text-white/80 mt-[0.1vw]">{{ getPlayText }}</span>
          </template>
        </div>
      </div>
    </a-tooltip>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onBeforeUnmount, onMounted } from 'vue';
  import { PlayCircleOutlined, PauseCircleOutlined, StopOutlined, LoadingOutlined, CaretRightOutlined } from '@ant-design/icons-vue';
  import { PlayController, PlayState, PlayDevice } from '../lib/play/PlayController';
  import { useGlobalThreeStore } from '../store/globalThreeStore';
  import PlayToolbar from './PlayToolbar.vue';

  // 状态定义
  const isPlaying = ref(false);
  const isPaused = ref(false);
  const isProcessing = ref(false);
  const currentDevice = ref(0);
  const totalDevices = ref(0);
  const currentDeviceName = ref('');
  const isToolbarExpanded = ref(false); // 工具栏是否展开
  const playToolbarRef = ref<InstanceType<typeof PlayToolbar> | null>(null);
  const globalThreeStore = useGlobalThreeStore();

  // 计算属性
  const getPlayIcon = computed(() => {
    if (isPlaying.value) {
      return isPaused.value ? PlayCircleOutlined : PauseCircleOutlined;
    }
    return CaretRightOutlined;
  });

  const getPlayText = computed(() => {
    if (isPlaying.value) {
      return isPaused.value ? '继续' : '暂停';
    }
    return '播放';
  });

  const getTooltip = computed(() => {
    if (globalThreeStore.isFloorSwitching) {
      return '楼层切换中，暂时无法使用播放功能';
    }
    if (globalThreeStore.isPatrolActive) {
      return '巡检功能正在运行中，请先停止巡检再使用播放功能';
    }
    if (isProcessing.value) {
      return '正在处理播放请求，请稍候...';
    }
    if (isPlaying.value) {
      return isPaused.value ? '继续自动播放' : '暂停自动播放';
    }
    if (isToolbarExpanded.value) {
      return '关闭播放工具栏';
    }
    return '开始自动播放（聚焦设备）';
  });

  // 监听ESC键
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && isPlaying.value) {
      stopPlay();
    }
  };

  // 处理播放按钮点击
  const handlePlayButtonClick = async () => {
    // 检查是否可以执行操作
    if (isProcessing.value || globalThreeStore.isFloorSwitching || globalThreeStore.isPatrolActive) {
      if (globalThreeStore.isFloorSwitching) {
        showMessage('楼层切换中，请等待切换完成后再使用播放功能', 'warning');
        return;
      }
      if (globalThreeStore.isPatrolActive) {
        showMessage('巡检功能正在运行中，请先停止巡检再使用播放功能', 'warning');
        return;
      }
      return;
    }

    if (!globalThreeStore.canUserInteract) {
      showMessage('系统正在加载中，请稍候再试...', 'warning');
      return;
    }

    // 如果正在播放，则根据当前状态执行相应操作
    if (isPlaying.value) {
      if (isPaused.value) {
        resumePlay();
      } else {
        pausePlay();
      }
    } else {
      // 如果没有播放，则切换工具栏显示状态
      toggleToolbar();
    }
  };

  // 切换工具栏显示状态
  const toggleToolbar = () => {
    if (playToolbarRef.value) {
      if (isToolbarExpanded.value) {
        playToolbarRef.value.hideToolbar();
        isToolbarExpanded.value = false;
      } else {
        playToolbarRef.value.showToolbar();
        isToolbarExpanded.value = true;
      }
    }
  };

  // 开始播放
  const startPlay = async () => {
    try {
      isProcessing.value = true;

      // 确保工具栏已显示
      if (!isToolbarExpanded.value && playToolbarRef.value) {
        playToolbarRef.value.showToolbar();
        isToolbarExpanded.value = true;
      }

      // 获取播放控制器
      const playController = PlayController.getInstance();

      // 查找设备
      const devices = playController.findDevices();

      if (devices.length === 0) {
        showMessage('未找到可播放的设备，无法开始播放', 'warning');
        isProcessing.value = false;
        return;
      }

      // 更新状态
      totalDevices.value = devices.length;
      currentDevice.value = 0;

      // 启用透视功能
      await enableTransparency();

      // 设置事件回调
      const events = {
        onStart: () => {
          isPlaying.value = true;
          isPaused.value = false;
          isProcessing.value = false;

          // 更新工具栏状态
          if (playToolbarRef.value) {
            playToolbarRef.value.updatePlayState(true, false);
            playToolbarRef.value.updateDeviceInfo(currentDevice.value, totalDevices.value);
          }

          showMessage('自动播放已开始', 'success');
        },
        onPause: () => {
          isPaused.value = true;

          // 更新工具栏状态
          if (playToolbarRef.value) {
            playToolbarRef.value.updatePlayState(true, true);
          }

          showMessage('自动播放已暂停', 'info');
        },
        onResume: () => {
          isPaused.value = false;

          // 更新工具栏状态
          if (playToolbarRef.value) {
            playToolbarRef.value.updatePlayState(true, false);
          }

          showMessage('自动播放已继续', 'info');
        },
        onStop: () => {
          isPlaying.value = false;
          isPaused.value = false;

          // 更新工具栏状态
          if (playToolbarRef.value) {
            playToolbarRef.value.updatePlayState(false, false);
          }

          showMessage('自动播放已停止', 'info');
        },
        onDeviceReached: (device: PlayDevice, index: number) => {
          currentDevice.value = index;
          currentDeviceName.value = device.name || '';

          // 更新工具栏设备信息
          if (playToolbarRef.value) {
            playToolbarRef.value.updateDeviceInfo(index, totalDevices.value);
          }

          // 显示当前播放设备信息
          if (device.name) {
            showMessage(`正在播放: ${device.name}`, 'info');
          }
        },
        onPlayComplete: () => {
          // 在循环模式下，播放完成事件不会被触发
          // 只有在非循环模式下才会触发
          stopPlay();
          showMessage('播放已完成', 'success');
        },
      };

      // 开始播放
      playController.start(events);

      // 添加键盘事件监听
      window.addEventListener('keydown', handleKeyDown);
    } catch (error) {
      console.error('开始播放失败:', error);
      showMessage('开始播放失败', 'error');
      isProcessing.value = false;
    }
  };

  // 暂停播放
  const pausePlay = () => {
    const playController = PlayController.getInstance();
    playController.pause();
  };

  // 继续播放
  const resumePlay = () => {
    const playController = PlayController.getInstance();
    playController.resume();
  };

  // 停止播放
  const stopPlay = async () => {
    try {
      isProcessing.value = true;

      // 停止播放
      const playController = PlayController.getInstance();
      playController.stop();

      // 移除键盘事件监听
      window.removeEventListener('keydown', handleKeyDown);

      // 恢复透明状态
      await restoreTransparency();

      // 更新状态
      isPlaying.value = false;
      isPaused.value = false;
      isProcessing.value = false;

      // 更新工具栏状态
      if (playToolbarRef.value) {
        playToolbarRef.value.updatePlayState(false, false);
      }
    } catch (error) {
      console.error('停止播放失败:', error);
      showMessage('停止播放失败', 'error');
      isProcessing.value = false;
    }
  };

  // 启用透视功能
  const enableTransparency = async (): Promise<void> => {
    return new Promise((resolve) => {
      // 获取透视按钮
      const viewControl = document.querySelector('[data-view-control="transparency"]');
      const isCurrentlyTransparent = viewControl?.classList.contains('active') || false;

      // 如果已经是透明状态，直接返回
      if (isCurrentlyTransparent) {
        resolve();
        return;
      }

      // 创建点击事件
      const event = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
      });

      // 监听透明状态变化
      const checkTransparency = () => {
        const isNowTransparent = viewControl?.classList.contains('active') || false;
        if (isNowTransparent) {
          // 显示提示
          showMessage('已自动启用透视功能，以便更清晰地查看设备', 'info');
          resolve();
        } else {
          // 如果还没变化，继续等待
          setTimeout(checkTransparency, 100);
        }
      };

      // 触发透视功能
      viewControl?.dispatchEvent(event);

      // 开始检查
      checkTransparency();
    });
  };

  // 恢复透明状态
  const restoreTransparency = async (): Promise<void> => {
    return new Promise((resolve) => {
      // 获取透视按钮
      const viewControl = document.querySelector('[data-view-control="transparency"]');
      const isCurrentlyTransparent = viewControl?.classList.contains('active') || false;

      // 如果当前是透明状态，关闭透明
      if (isCurrentlyTransparent) {
        // 创建点击事件
        const event = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window,
        });

        // 监听透明状态变化
        const checkTransparency = () => {
          const isNowTransparent = viewControl?.classList.contains('active') || false;
          if (!isNowTransparent) {
            resolve();
          } else {
            // 如果还没变化，继续等待
            setTimeout(checkTransparency, 100);
          }
        };

        // 触发透视功能关闭
        viewControl?.dispatchEvent(event);

        // 开始检查
        checkTransparency();
      } else {
        // 如果已经是不透明状态，直接返回
        resolve();
      }
    });
  };

  // 显示消息的安全方法
  const showMessage = (text: string, type: string = 'info', options = {}) => {
    try {
      if (window.$message) {
        window.$message[type]?.(text);
      }
    } catch (error) {
      console.log('显示消息失败:', error);
    }
  };

  // 监听工具栏停止播放事件
  const handlePlayStopped = () => {
    if (isPlaying.value) {
      stopPlay();
    }
  };

  // 监听工具栏播放完成事件
  const handlePlayCompleted = () => {
    if (isPlaying.value) {
      stopPlay();
    }
  };

  // 处理巡检功能激活事件
  const handlePatrolActivated = () => {
    // 如果播放正在进行中，需要停止播放
    if (isPlaying.value) {
      stopPlay();
      showMessage('巡检功能已激活，播放功能已自动停止', 'info');
    }
  };

  // 处理循环模式变更
  const handleLoopModeChanged = (enabled: boolean) => {
    // 如果播放正在进行中，更新控制器的循环模式
    if (isPlaying.value) {
      const playController = PlayController.getInstance();
      playController.setLoopMode(enabled);
      showMessage(`${enabled ? '启用' : '禁用'}循环播放模式`, 'info');
    }
  };

  // 组件挂载时添加事件监听
  onMounted(() => {
    window.addEventListener('play-stopped', handlePlayStopped);
    window.addEventListener('play-completed', handlePlayCompleted);
    window.addEventListener('patrol-activated', handlePatrolActivated);
  });

  // 组件卸载时清理
  onBeforeUnmount(() => {
    // 确保停止播放
    const playController = PlayController.getInstance();
    if (playController.getState() !== PlayState.IDLE) {
      playController.stop();
    }

    // 移除事件监听
    window.removeEventListener('keydown', handleKeyDown);
    window.removeEventListener('play-stopped', handlePlayStopped);
    window.removeEventListener('play-completed', handlePlayCompleted);
    window.removeEventListener('patrol-activated', handlePatrolActivated);
  });
</script>
