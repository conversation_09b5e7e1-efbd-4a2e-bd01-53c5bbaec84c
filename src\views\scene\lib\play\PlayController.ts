import * as THREE from 'three';
import { CameraController } from '../CameraController';
import { ControlManager } from '../control/ControlManager';
import { SceneManager } from '../SceneManager';
import { ModelLoaderManager } from '../load/ModelLoaderManager';
import { useGlobalThreeStore } from '../../store/globalThreeStore';

// 播放设备接口
export interface PlayDevice {
  object: THREE.Object3D;
  name: string;
  position: THREE.Vector3;
  size: THREE.Vector3;
}

// 播放状态枚举
export enum PlayState {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
}

// 播放配置接口
export interface PlayConfig {
  stayTime: number; // 每个设备停留时间（毫秒）
  viewDistance: number; // 观察距离系数
  minViewDistance: number; // 最小观察距离
  cameraHeight: number; // 相机高度（米）
  transitionDuration: number; // 相机移动过渡时间（毫秒）
  loopMode: boolean; // 循环播放模式
  orbitMode: boolean; // 环绕观察模式
  orbitSpeed: number; // 环绕速度（弧度/秒）
  orbitAngle: number; // 环绕角度（弧度）
}

// 播放事件接口
export interface PlayEvents {
  onStart?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  onStop?: () => void;
  onDeviceReached?: (device: PlayDevice, index: number) => void;
  onPlayComplete?: () => void;
}

/**
 * 自动播放控制器
 * 实现自动聚焦到场景中的每个设备
 */
export class PlayController {
  private static instance: PlayController | null = null;

  // 基础组件
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private controlManager: ControlManager;
  private cameraController: CameraController;

  // 播放状态
  private state: PlayState = PlayState.IDLE;
  private devices: PlayDevice[] = [];
  private currentDeviceIndex: number = 0;
  private playCompleted: boolean = false;

  // 动画控制
  private deviceTimeout: number | null = null;
  private cameraMoving: boolean = false;
  private orbitAnimationId: number | null = null;

  // 环绕动画状态
  private orbitState: {
    device: PlayDevice;
    angle: number;
    radius: number;
    startTime: number;
    startAngle: number; // 起始角度
    endAngle: number; // 结束角度
  } | null = null;

  // 配置
  private config: PlayConfig = {
    stayTime: 8000, // 每个设备停留时间
    viewDistance: 2.5, // 观察距离系数
    minViewDistance: 2.0, // 最小观察距离
    cameraHeight: 1.7, // 相机高度（米）
    transitionDuration: 1200, // 相机移动过渡时间（毫秒）
    loopMode: true, // 默认启用循环播放模式
    orbitMode: true, // 默认启用环绕观察模式
    orbitSpeed: 0.2, // 环绕速度（弧度/秒）
    orbitAngle: (120 * Math.PI) / 180, // 环绕角度 - 120度（转换为弧度）
  };

  // 事件回调
  private events: PlayEvents = {};

  /**
   * 获取单例实例
   */
  public static getInstance(): PlayController {
    if (!PlayController.instance) {
      PlayController.instance = new PlayController();
    }
    return PlayController.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    // 获取必要组件
    this.scene = SceneManager.getInstance().scene;
    this.camera = CameraController.getInstance().camera as THREE.PerspectiveCamera;
    this.controlManager = ControlManager.getInstance();
    this.cameraController = CameraController.getInstance();
  }

  /**
   * 查找当前楼层的设备
   */
  public findDevices(): PlayDevice[] {
    const devices: PlayDevice[] = [];

    // 获取当前楼层ID
    const globalThreeStore = useGlobalThreeStore();
    const currentFloorId = globalThreeStore.currentFloorId;

    if (!currentFloorId) {
      console.warn('[PlayController] 无法查找设备：未找到当前楼层ID');
      return devices;
    }

    // 获取当前楼层的模型
    const modelLoader = ModelLoaderManager.getInstance();
    const models = modelLoader.getCurrentModels();
    const processedDevices = new Map<string, boolean>();

    // 提取楼层号
    const currentFloorMatch = currentFloorId.match(/floor-1-(\d+)/);
    const currentFloorNumber = currentFloorMatch ? currentFloorMatch[1] : null;

    if (!currentFloorNumber) {
      console.warn('[PlayController] 无法解析楼层号');
      return devices;
    }

    // 遍历所有模型，查找符合条件的设备
    models.forEach((model) => {
      model.traverse((object) => {
        // 跳过已处理的对象
        if (processedDevices.has(object.uuid)) return;
        processedDevices.set(object.uuid, true);

        // 检查是否是楼层设备
        if (object.name && this._isFloorDevice(object.name)) {
          // 提取楼层号
          const floorMatch = object.name.match(/(\d+)F/);
          const deviceFloorNumber = floorMatch ? floorMatch[1] : null;

          if (deviceFloorNumber && deviceFloorNumber === currentFloorNumber) {
            // 只添加当前楼层的设备
            const position = new THREE.Vector3();
            object.getWorldPosition(position);

            // 计算设备的包围盒
            const boundingBox = new THREE.Box3().setFromObject(object);
            const size = new THREE.Vector3();
            boundingBox.getSize(size);

            // 添加设备
            devices.push({
              object: object,
              name: object.name,
              position: position,
              size: size,
            });
          }
        }
      });
    });

    console.log(`[PlayController] 找到 ${devices.length} 个设备`);
    return devices;
  }

  /**
   * 判断对象名称是否为楼层设备
   * @param name 对象名称
   * @returns 是否为楼层设备
   */
  private _isFloorDevice(name: string): boolean {
    // 使用统一的设备识别逻辑
    return /^[1-4]F/.test(name);
  }

  /**
   * 移动到指定设备
   * @param device 设备
   */
  private _moveToDevice(device: PlayDevice): void {
    this.cameraMoving = true;

    // 计算观察位置
    const viewDistance = Math.max(Math.max(device.size.x, device.size.z) * this.config.viewDistance, this.config.minViewDistance);

    // 如果启用了环绕模式，使用环绕观察
    if (this.config.orbitMode) {
      // 随机选择设备的一个面作为初始观察点
      // 生成一个随机角度（0-360度）
      const randomAngle = Math.random() * Math.PI * 2;

      // 计算初始角度 - 随机选择一个面作为起点
      // 将随机角度转换为 -180 到 +180 度范围内，便于后续计算
      const initialAngle = randomAngle - Math.PI;

      // 计算环绕的起始角度和结束角度
      // 起始角度为随机选择的角度，结束角度为起始角度 + 120度
      const startAngle = initialAngle;
      const endAngle = initialAngle + this.config.orbitAngle; // 俯视角度参数 - 调整为45度左右的俯视角
      const heightOffset = device.size.y + viewDistance * 0.4; // 降低高度偏移，调整为更合适的俯视角度

      // 计算初始位置（45度左右的俯视角）
      const cameraPosition = new THREE.Vector3(
        device.position.x + Math.sin(startAngle) * viewDistance,
        device.position.y + heightOffset, // 降低相机高度，实现约45度俯视
        device.position.z + Math.cos(startAngle) * viewDistance
      );

      // 计算目标点 - 稍微偏移以创建俯视效果
      const targetPosition = new THREE.Vector3(
        device.position.x,
        device.position.y + device.size.y * 0.3, // 目标点稍微高于设备底部
        device.position.z
      );

      // 移动相机到初始位置
      this.cameraController.moveToPosition(
        {
          x: cameraPosition.x,
          y: cameraPosition.y,
          z: cameraPosition.z,
        },
        {
          x: targetPosition.x,
          y: targetPosition.y,
          z: targetPosition.z,
        },
        this.config.transitionDuration,
        () => {
          // 相机到达初始位置后，开始环绕动画
          this.cameraMoving = false;
          this._startOrbitAnimation(device, viewDistance, startAngle, endAngle);
        }
      );
    } else {
      // 使用原来的静态观察方式
      // 确定设备的前方向
      let frontDirection = new THREE.Vector3(0, 0, 1); // 默认Z轴正方向为前方

      // 如果设备有旋转，考虑其旋转
      if (device.object.rotation) {
        frontDirection.applyEuler(device.object.rotation);
      }

      // 创建一个向左旋转90度的方向向量（将前方向向量旋转90度）
      const leftDirection = new THREE.Vector3();
      // 叉乘前方向与上方向(0,1,0)得到右方向，再取反得到左方向
      leftDirection
        .crossVectors(frontDirection, new THREE.Vector3(0, 1, 0))
        .negate()
        .normalize();

      // 计算相机位置
      const cameraPosition = device.position.clone();
      cameraPosition.y = Math.max(device.position.y + device.size.y * 0.5, this.config.cameraHeight);

      // 在设备左侧放置相机（原来是前方）
      cameraPosition.addScaledVector(leftDirection, viewDistance);

      // 移动相机
      this.cameraController.moveToPosition(
        {
          x: cameraPosition.x,
          y: cameraPosition.y,
          z: cameraPosition.z,
        },
        {
          x: device.position.x,
          y: device.position.y,
          z: device.position.z,
        },
        this.config.transitionDuration,
        () => {
          this.cameraMoving = false;
          this._deviceReached();
        }
      );
    }
  }

  /**
   * 开始环绕动画
   * @param device 设备
   * @param radius 环绕半径
   * @param startAngle 起始角度（弧度）
   * @param endAngle 结束角度（弧度）
   */
  private _startOrbitAnimation(device: PlayDevice, radius: number, startAngle?: number, endAngle?: number): void {
    // 停止任何正在进行的环绕动画
    this._stopOrbitAnimation();

    // 初始化环绕状态
    this.orbitState = {
      device: device,
      angle: startAngle !== undefined ? startAngle : (-120 * Math.PI) / 180, // 使用提供的起始角度或默认值
      radius: radius,
      startTime: performance.now(),
      startAngle: startAngle !== undefined ? startAngle : (-120 * Math.PI) / 180, // 记录起始角度
      endAngle: endAngle !== undefined ? endAngle : (120 * Math.PI) / 180, // 记录结束角度
    };

    // 触发设备到达事件
    if (this.events.onDeviceReached) {
      this.events.onDeviceReached(device, this.currentDeviceIndex);
    }

    // 开始环绕动画
    this._updateOrbitAnimation();
  }

  /**
   * 更新环绕动画
   */
  private _updateOrbitAnimation(): void {
    // 如果播放已暂停或停止，不更新动画
    if (this.state !== PlayState.RUNNING || !this.orbitState) {
      return;
    }

    const now = performance.now();
    const elapsed = (now - this.orbitState.startTime) / 1000; // 转换为秒

    // 计算旋转指定角度所需的时间（秒）
    // 计算从起始角度到结束角度的角度差
    const angleDifference = this.orbitState.endAngle - this.orbitState.startAngle;
    const rotationTime = Math.abs(angleDifference) / this.config.orbitSpeed;

    // 计算当前角度 - 从起始角度开始，最多旋转到结束角度
    const progress = Math.min((elapsed * this.config.orbitSpeed) / Math.abs(angleDifference), 1);
    this.orbitState.angle = this.orbitState.startAngle + progress * angleDifference;

    // 计算相机位置
    const device = this.orbitState.device;
    const radius = this.orbitState.radius; // 俯视角度参数 - 调整为45度左右的俯视角
    const heightOffset = device.size.y + radius * 0.4; // 降低高度偏移，调整为更合适的俯视角度

    // 计算环绕位置（水平面上的位置）
    const horizontalX = Math.sin(this.orbitState.angle) * radius;
    const horizontalZ = Math.cos(this.orbitState.angle) * radius;

    // 应用俯视角度
    const cameraPosition = new THREE.Vector3(
      device.position.x + horizontalX,
      device.position.y + heightOffset, // 位于设备上方
      device.position.z + horizontalZ
    );

    // 计算目标点 - 稍微偏移以创建俯视效果
    const targetPosition = new THREE.Vector3(
      device.position.x,
      device.position.y + device.size.y * 0.3, // 目标点稍微高于设备底部
      device.position.z
    );

    // 更新相机位置
    this.camera.position.copy(cameraPosition);

    // 使相机朝向目标点
    this.camera.lookAt(targetPosition);

    // 请求渲染
    SceneManager.getInstance().needsRender = true;

    // 检查是否完成指定角度的环绕
    if (elapsed >= rotationTime) {
      // 已经完成指定角度的环绕

      // 如果已经停留足够长的时间，移动到下一个设备
      if (elapsed >= this.config.stayTime / 1000) {
        // 环绕完成，移动到下一个设备
        this._stopOrbitAnimation();
        this._moveToNextDevice();
      } else {
        // 已完成环绕但还未达到停留时间，保持在最终位置
        // 不再继续旋转，但保持动画帧以便检查时间
        this.orbitAnimationId = requestAnimationFrame(() => this._updateOrbitAnimation());
      }
    } else {
      // 尚未完成指定角度的环绕，继续环绕
      this.orbitAnimationId = requestAnimationFrame(() => this._updateOrbitAnimation());
    }
  }

  /**
   * 停止环绕动画
   */
  private _stopOrbitAnimation(): void {
    if (this.orbitAnimationId !== null) {
      cancelAnimationFrame(this.orbitAnimationId);
      this.orbitAnimationId = null;
    }
    this.orbitState = null;
  }

  /**
   * 移动到下一个设备
   */
  private _moveToNextDevice(): void {
    // 移动到下一个设备
    this.currentDeviceIndex++;

    // 如果所有设备都已访问
    if (this.currentDeviceIndex >= this.devices.length) {
      // 如果启用了循环模式，则重新从第一个设备开始
      if (this.config.loopMode) {
        console.log('[PlayController] 循环播放模式：重新从第一个设备开始');
        this.currentDeviceIndex = 0;
        this._moveToDevice(this.devices[this.currentDeviceIndex]);
      } else {
        // 如果没有启用循环模式，则完成播放
        this._playCompleted();
        return;
      }
    } else {
      // 移动到下一个设备
      this._moveToDevice(this.devices[this.currentDeviceIndex]);
    }
  }

  /**
   * 当到达设备时调用（仅用于非环绕模式）
   */
  private _deviceReached(): void {
    const currentDevice = this.devices[this.currentDeviceIndex];

    // 触发设备到达事件
    if (this.events.onDeviceReached) {
      this.events.onDeviceReached(currentDevice, this.currentDeviceIndex);
    }

    // 在设备停留指定时间
    this.deviceTimeout = window.setTimeout(() => {
      this._moveToNextDevice();
    }, this.config.stayTime);
  }

  /**
   * 当完成所有设备播放时调用
   */
  private _playCompleted(): void {
    this.playCompleted = true;
    this.state = PlayState.IDLE;

    // 触发播放完成事件
    if (this.events.onPlayComplete) {
      this.events.onPlayComplete();
    }
  }

  /**
   * 开始播放
   * @param events 事件回调
   */
  public start(events?: PlayEvents): void {
    // 检查巡检功能是否激活
    const globalThreeStore = useGlobalThreeStore();
    if (globalThreeStore.isPatrolActive) {
      console.warn('[PlayController] 无法开始播放：巡检功能正在运行中');
      if (window.$message) {
        window.$message.warning('巡检功能正在运行中，请先停止巡检再使用播放功能');
      }
      return;
    }

    // 如果已经在运行，先停止
    if (this.state === PlayState.RUNNING) {
      this.stop();
    }

    // 设置事件回调
    if (events) {
      this.events = events;
    }

    // 查找设备
    this.devices = this.findDevices();

    if (this.devices.length === 0) {
      console.warn('[PlayController] 无法开始播放：没有找到设备');
      return;
    }

    // 初始化状态
    this.currentDeviceIndex = 0;
    this.playCompleted = false;

    // 更新状态
    this.state = PlayState.RUNNING;

    // 更新全局状态
    globalThreeStore.setPlayActive(true);

    // 触发开始事件
    if (this.events.onStart) {
      this.events.onStart();
    }

    // 禁用控制器
    this.controlManager.lockControl();

    // 移动到第一个设备
    this._moveToDevice(this.devices[0]);

    console.log('[PlayController] 开始播放，设备数量:', this.devices.length);
  }

  /**
   * 暂停播放
   */
  public pause(): void {
    if (this.state !== PlayState.RUNNING) return;

    this.state = PlayState.PAUSED;

    // 清除设备超时
    if (this.deviceTimeout !== null) {
      window.clearTimeout(this.deviceTimeout);
      this.deviceTimeout = null;
    }

    // 停止环绕动画（暂停时）
    if (this.orbitAnimationId !== null) {
      cancelAnimationFrame(this.orbitAnimationId);
      this.orbitAnimationId = null;
    }

    // 触发暂停事件
    if (this.events.onPause) {
      this.events.onPause();
    }

    console.log('[PlayController] 暂停播放');
  }

  /**
   * 恢复播放
   */
  public resume(): void {
    if (this.state !== PlayState.PAUSED) return;

    this.state = PlayState.RUNNING;

    // 如果相机不在移动中，重新开始设备停留计时或环绕动画
    if (!this.cameraMoving) {
      if (this.config.orbitMode && this.orbitState) {
        // 如果是环绕模式，重新开始环绕动画
        // 更新开始时间，保持当前角度位置
        this.orbitState.startTime = performance.now() - ((this.orbitState.angle - this.orbitState.startAngle) * 1000) / this.config.orbitSpeed;
        this._updateOrbitAnimation();
      } else {
        // 如果是静态观察模式，重新开始设备停留计时
        this._deviceReached();
      }
    }

    // 触发恢复事件
    if (this.events.onResume) {
      this.events.onResume();
    }

    console.log('[PlayController] 恢复播放');
  }

  /**
   * 停止播放
   */
  public stop(): void {
    // 即使当前状态是IDLE，也执行清理操作，确保完全停止

    // 清除设备超时
    if (this.deviceTimeout !== null) {
      window.clearTimeout(this.deviceTimeout);
      this.deviceTimeout = null;
    }

    // 停止环绕动画
    this._stopOrbitAnimation();

    // 更新状态
    const wasPlaying = this.state !== PlayState.IDLE;
    this.state = PlayState.IDLE;
    this.playCompleted = true;
    this.cameraMoving = false;

    // 触发停止事件（只有在之前是播放状态时才触发）
    if (wasPlaying && this.events.onStop) {
      try {
        this.events.onStop();
      } catch (error) {
        console.error('[PlayController] 触发停止事件失败:', error);
      }
    }

    // 确保控制器被解锁
    try {
      this.controlManager.unlockControl();
    } catch (error) {
      console.error('[PlayController] 解锁控制器失败:', error);
    }

    // 更新全局状态
    const globalThreeStore = useGlobalThreeStore();
    globalThreeStore.setPlayActive(false);

    console.log('[PlayController] 停止播放');
  }

  /**
   * 获取当前状态
   */
  public getState(): PlayState {
    return this.state;
  }

  /**
   * 获取当前设备索引
   */
  public getCurrentDeviceIndex(): number {
    return this.currentDeviceIndex;
  }

  /**
   * 获取设备总数
   */
  public getDeviceCount(): number {
    return this.devices.length;
  }

  /**
   * 获取当前设备
   */
  public getCurrentDevice(): PlayDevice | null {
    return this.devices[this.currentDeviceIndex] || null;
  }

  /**
   * 设置配置
   * @param config 配置对象
   */
  public setConfig(config: Partial<PlayConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取配置
   */
  public getConfig(): PlayConfig {
    return { ...this.config };
  }

  /**
   * 设置循环播放模式
   * @param enabled 是否启用循环播放
   */
  public setLoopMode(enabled: boolean): void {
    this.config.loopMode = enabled;
    console.log(`[PlayController] ${enabled ? '启用' : '禁用'}循环播放模式`);
  }

  /**
   * 获取循环播放模式状态
   * @returns 是否启用循环播放
   */
  public isLoopModeEnabled(): boolean {
    return this.config.loopMode;
  }

  /**
   * 切换循环播放模式
   * @returns 切换后的循环播放模式状态
   */
  public toggleLoopMode(): boolean {
    this.config.loopMode = !this.config.loopMode;
    console.log(`[PlayController] ${this.config.loopMode ? '启用' : '禁用'}循环播放模式`);
    return this.config.loopMode;
  }

  /**
   * 设置环绕观察模式
   * @param enabled 是否启用环绕观察模式
   */
  public setOrbitMode(enabled: boolean): void {
    this.config.orbitMode = enabled;
    console.log(`[PlayController] ${enabled ? '启用' : '禁用'}环绕观察模式`);
  }

  /**
   * 获取环绕观察模式状态
   * @returns 是否启用环绕观察模式
   */
  public isOrbitModeEnabled(): boolean {
    return this.config.orbitMode;
  }

  /**
   * 切换环绕观察模式
   * @returns 切换后的环绕观察模式状态
   */
  public toggleOrbitMode(): boolean {
    this.config.orbitMode = !this.config.orbitMode;
    console.log(`[PlayController] ${this.config.orbitMode ? '启用' : '禁用'}环绕观察模式`);
    return this.config.orbitMode;
  }

  /**
   * 切换到下一个设备
   * @returns 是否成功切换
   */
  public goToNextDevice(): boolean {
    // 如果没有在播放中，或者没有设备，则返回false
    if (this.state === PlayState.IDLE || this.devices.length === 0) {
      return false;
    }

    // 计算下一个索引
    const nextIndex = this.currentDeviceIndex + 1;

    // 检查是否超出范围
    if (nextIndex >= this.devices.length) {
      // 如果循环模式开启，回到第一个设备
      if (this.config.loopMode) {
        this.currentDeviceIndex = 0;
      } else {
        return false;
      }
    } else {
      this.currentDeviceIndex = nextIndex;
    }

    // 停止当前的环绕动画（如果有）
    this._stopOrbitAnimation();

    // 移动到新的设备位置
    this._moveToDevice(this.devices[this.currentDeviceIndex]);
    return true;
  }

  /**
   * 切换到上一个设备
   * @returns 是否成功切换
   */
  public goToPreviousDevice(): boolean {
    // 如果没有在播放中，或者没有设备，则返回false
    if (this.state === PlayState.IDLE || this.devices.length === 0) {
      return false;
    }

    // 计算上一个索引
    const previousIndex = this.currentDeviceIndex - 1;

    // 检查是否超出范围
    if (previousIndex < 0) {
      // 如果循环模式开启，跳转到最后一个设备
      if (this.config.loopMode) {
        this.currentDeviceIndex = this.devices.length - 1;
      } else {
        return false;
      }
    } else {
      this.currentDeviceIndex = previousIndex;
    }

    // 停止当前的环绕动画（如果有）
    this._stopOrbitAnimation();

    // 移动到新的设备位置
    this._moveToDevice(this.devices[this.currentDeviceIndex]);
    return true;
  }
}
