import * as THREE from 'three';
import { SceneManager } from '../SceneManager';
import { ControlManager } from '../control/ControlManager';
import { RenderingPipeline } from '../RenderingPipeline';
import { CameraController } from '../CameraController';

/**
 * 漫游配置接口
 */
export interface RoamingConfig {
  moveSpeed: number; // 移动速度（米/秒）
  runSpeedMultiplier: number; // 奔跑速度倍数
  mouseSensitivity: number; // 鼠标灵敏度
  cameraHeight: number; // 相机高度（米）
  fov: number; // 超广角视场角（度），固定为110度，提供更宽广的视野
}

/**
 * 漫游状态枚举
 */
export enum RoamingState {
  IDLE = 'idle', // 空闲状态
  ACTIVE = 'active', // 漫游中
}

/**
 * 移动输入接口
 */
export interface Movement {
  forward: boolean;
  backward: boolean;
  left: boolean;
  right: boolean;
  up: boolean;
  accelerate: boolean;
}

/**
 * 漫游控制器
 * 实现第一人称漫游功能，包括碰撞检测
 * 使用超广角视角(FOV: 110度)提供更宽广的视野，进入漫游模式时自动应用，退出时恢复原始FOV
 */
export class RoamingController {
  private static instance: RoamingController | null = null;

  // 基础组件
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private controlManager: ControlManager;
  private raycaster: THREE.Raycaster;

  // 漫游状态
  private state: RoamingState = RoamingState.IDLE;
  private startPosition: THREE.Vector3 = new THREE.Vector3();
  private originalCameraPosition: THREE.Vector3 = new THREE.Vector3();
  private originalCameraRotation: THREE.Euler = new THREE.Euler();
  private originalCameraFov: number = 75;
  private originalControlsTarget: THREE.Vector3 = new THREE.Vector3();
  private position: THREE.Vector3 = new THREE.Vector3();
  private velocity: THREE.Vector3 = new THREE.Vector3();
  private targetRotation: { x: number; y: number } = { x: 0, y: 0 }; // 目标旋转值
  private movement: Movement = {
    forward: false,
    backward: false,
    left: false,
    right: false,
    up: false,
    accelerate: false,
  };

  // 场景对象
  private wallObjects: THREE.Object3D[] = [];
  private floorObjects: THREE.Object3D[] = [];

  // 配置
  private config: RoamingConfig = {
    moveSpeed: 5,
    runSpeedMultiplier: 2.0,
    mouseSensitivity: 0.002, // 调整为更适合第一人称视角的灵敏度值
    cameraHeight: 1.7,
    fov: 110, // 超广角视角效果 (110度)
  };

  // 事件处理函数
  private keyDownHandler: (e: KeyboardEvent) => void;
  private keyUpHandler: (e: KeyboardEvent) => void;
  private mouseDownHandler: (e: MouseEvent) => void;
  private mouseUpHandler: (e: MouseEvent) => void;
  private mouseMoveHandler: ((e: MouseEvent) => void) & { cancel?: () => void };
  private pointerLockChangeHandler: () => void;
  private pointerLockErrorHandler: () => void;
  private updateCallback: ((deltaTime: number) => void) | null = null;

  // 移动相关
  private currentVelocity: THREE.Vector3 = new THREE.Vector3();
  private targetVelocity: THREE.Vector3 = new THREE.Vector3();
  private animationFrameId: number | null = null;

  // 鼠标移动处理函数

  /**
   * 获取单例实例
   */
  public static getInstance(): RoamingController {
    if (!RoamingController.instance) {
      RoamingController.instance = new RoamingController();
    }
    return RoamingController.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    try {
      const sceneManager = SceneManager.getInstance();
      this.scene = sceneManager.scene;

      // 从CameraController获取相机，确保相机已初始化
      const cameraController = CameraController.getInstance();
      this.camera = cameraController.camera;

      // 从RenderingPipeline获取渲染器，确保渲染器已初始化
      const renderingPipeline = RenderingPipeline.getInstance();
      this.renderer = renderingPipeline.getRenderer();

      this.controlManager = ControlManager.getInstance();
      this.raycaster = new THREE.Raycaster();

      // 设置射线检测器
      this.raycaster.firstHitOnly = true;
      this.raycaster.near = 0;
      this.raycaster.far = 10;

      // 初始化事件处理函数 - 修改：先定义函数再绑定，避免undefined错误
      this.keyDownHandler = this._handleKeyDown.bind(this);
      this.keyUpHandler = this._handleKeyUp.bind(this);
      this.mouseDownHandler = this._handleMouseDown.bind(this);
      this.mouseUpHandler = this._handleMouseUp.bind(this);
      // 直接绑定鼠标移动处理函数，不使用节流
      this.mouseMoveHandler = this._handleMouseMove.bind(this);
      this.pointerLockChangeHandler = this._handlePointerLockChange.bind(this);
      this.pointerLockErrorHandler = this._handlePointerLockError.bind(this);

      console.log('[RoamingController] 初始化完成');
    } catch (error) {
      console.error('[RoamingController] 初始化失败:', error);
    }
  }

  /**
   * 设置起始位置
   * @param position 起始位置 (可选，如果不提供则自动查找home_position或设备位置)
   */
  public setStartPosition(position?: THREE.Vector3): void {
    try {
      if (position) {
        // 如果提供了位置，直接使用
        this.startPosition.copy(position);
        console.log(`[RoamingController] 使用提供的位置作为起始点: (${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)})`);
      } else {
        // 查找home_position或任何设备作为起始位置
        const foundPosition = this._findDevicePosition();
        if (foundPosition) {
          this.startPosition.copy(foundPosition);
          // 日志已在_findDevicePosition方法中输出
        } else {
          // 如果找不到home_position或设备，使用当前相机位置
          console.warn('[RoamingController] 未找到home_position或设备，使用当前相机位置作为起始点');

          // 确保相机位置是有效的
          if (this.camera && !isNaN(this.camera.position.x) && !isNaN(this.camera.position.y) && !isNaN(this.camera.position.z)) {
            this.startPosition.copy(this.camera.position);
            console.log(
              `[RoamingController] 使用相机位置作为起始点: (${this.camera.position.x.toFixed(2)}, ${this.camera.position.y.toFixed(2)}, ${this.camera.position.z.toFixed(2)})`
            );
          } else {
            // 如果相机位置无效，使用默认位置
            console.warn('[RoamingController] 相机位置无效，使用默认位置');
            this.startPosition.set(0, this.config.cameraHeight, 0);
          }
        }
      }

      // 确保位置有效
      if (
        isNaN(this.startPosition.x) ||
        isNaN(this.startPosition.y) ||
        isNaN(this.startPosition.z) ||
        !isFinite(this.startPosition.x) ||
        !isFinite(this.startPosition.y) ||
        !isFinite(this.startPosition.z)
      ) {
        console.warn('[RoamingController] 起始位置包含无效值，重置为默认位置');
        this.startPosition.set(0, this.config.cameraHeight, 0);
      }

      // 确保场景和地板对象已收集
      if (this.scene && this.floorObjects.length === 0) {
        this._collectSceneObjects();
      }

      // 查找地板位置并确保角色站在地板上
      this._ensurePositionAboveFloor(this.startPosition);

      console.log(
        `[RoamingController] 最终设置的起始位置: (${this.startPosition.x.toFixed(2)}, ${this.startPosition.y.toFixed(2)}, ${this.startPosition.z.toFixed(2)})`
      );
    } catch (error) {
      console.error('[RoamingController] 设置起始位置时发生错误:', error);
      // 出错时使用安全的默认位置
      this.startPosition.set(0, this.config.cameraHeight, 0);
    }
  }

  /**
   * 确保位置在地板上方，防止玩家掉到地板下面
   * @param position 需要检查的位置
   * @param minHeightAboveFloor 与地板的最小高度差，默认为1.6（大致等于人眼高度）
   * @returns 调整后的位置
   */
  private _ensurePositionAboveFloor(position: THREE.Vector3, minHeightAboveFloor: number = 1.6): THREE.Vector3 {
    // 如果地板对象列表为空，先收集场景对象
    if (this.floorObjects.length === 0) {
      this._collectSceneObjects();
    }

    // 如果仍然没有地板对象，无法检测地板
    if (this.floorObjects.length === 0) {
      console.warn('[RoamingController] 未找到地板对象，无法调整高度');
      return position;
    }

    // 创建向下的射线检测地板
    const raycaster = new THREE.Raycaster();
    const direction = new THREE.Vector3(0, -1, 0);

    // 将射线原点设置稍微高一点，确保能检测到地板
    const rayOrigin = position.clone();
    rayOrigin.y += 10;

    raycaster.set(rayOrigin, direction);

    // 只检测地板对象，而不是整个场景
    const intersects = raycaster.intersectObjects(this.floorObjects, true);

    // 如果有交点，找出最近的地板
    if (intersects.length > 0) {
      // 找到地板，确保位置在地板上方
      const floorY = intersects[0].point.y;

      // 如果当前位置低于地板+最小高度，则调整高度
      if (position.y < floorY + minHeightAboveFloor) {
        position.y = floorY + minHeightAboveFloor;
      }

      return position;
    }

    // 如果没有找到地板，返回原始位置
    console.warn('[RoamingController] 未检测到地板，使用原始位置');
    return position;
  }

  // 移除已废弃的平滑移动相关方法

  /**
   * 开始漫游
   */
  public start(): void {
    if (this.state === RoamingState.ACTIVE) {
      console.warn('[RoamingController] 漫游已经在进行中');
      return;
    }

    try {
      // 保存当前相机位置和朝向
      this.originalCameraPosition.copy(this.camera.position);
      this.originalCameraRotation.copy(this.camera.rotation);
      this.originalCameraFov = this.camera.fov;

      // 保存OrbitControls的target
      const controlManager = ControlManager.getInstance();
      if (controlManager) {
        const controls = controlManager.getOrbitControls();
        if (controls && controls.target) {
          this.originalControlsTarget.copy(controls.target);
          console.log(
            `[RoamingController] 已保存原始控制器目标点: (${controls.target.x.toFixed(2)}, ${controls.target.y.toFixed(2)}, ${controls.target.z.toFixed(2)})`
          );
        }
      }

      // 设置漫游起始位置
      if (this.startPosition.lengthSq() === 0) {
        this.setStartPosition();
      }

      // 将相机移动到起始位置
      this.camera.position.copy(this.startPosition);

      // 初始化相机旋转和视角设置
      this._initializeCameraRotation();

      // 启用漫游控制
      this._enableRoamingControls();

      // 更新状态
      this.state = RoamingState.ACTIVE;

      console.log('[RoamingController] 漫游已启动');
    } catch (error) {
      console.error('[RoamingController] 启动漫游失败:', error);
      this._restoreCamera();
    }
  }

  /**
   * 初始化相机旋转和视角设置
   * 应用超广角视角效果，提供更宽广的视野
   * @private
   */
  private _initializeCameraRotation(): void {
    try {
      // 确保相机旋转顺序正确
      this.camera.rotation.order = 'YXZ';

      // 重置旋转角度，确保初始视角正确
      this.camera.rotation.set(0, 0, 0);

      // 初始化目标旋转值
      this.targetRotation.x = 0;
      this.targetRotation.y = 0;

      // 设置超广角FOV（视场角）- 固定为110度，提供更宽广的视野
      this.camera.fov = this.config.fov; // 使用配置中的超广角FOV值
      this.camera.updateProjectionMatrix();

      console.log(`[RoamingController] 已初始化相机旋转和视角设置，应用超广角视角(FOV: ${this.config.fov}度)`);
    } catch (error) {
      console.error('[RoamingController] 初始化相机旋转失败:', error);
    }
  }

  /**
   * 停止漫游
   */
  public stop(): void {
    if (this.state === RoamingState.IDLE) {
      console.warn('[RoamingController] 漫游未在进行中');
      return;
    }

    try {
      // 禁用漫游控制
      this._disableRoamingControls();

      // 恢复相机位置和朝向
      this._restoreCamera();

      // 更新状态
      this.state = RoamingState.IDLE;

      console.log('[RoamingController] 漫游已停止');
    } catch (error) {
      console.error('[RoamingController] 停止漫游失败:', error);
    }
  }

  /**
   * 查找漫游起始位置
   * 优先查找名为"home_position"的对象，其次查找设备
   * @param deviceName 可选的设备名称，如果不提供则查找任何设备
   * @returns 找到的位置，如果找不到则返回默认位置
   */
  private _findDevicePosition(deviceName?: string): THREE.Vector3 | null {
    try {
      // 确保场景已初始化
      if (!this.scene) {
        console.error('[RoamingController] 场景未初始化，无法查找设备');
        return null;
      }

      // 首先尝试查找home_position
      let homePositionObject: THREE.Object3D | null = null;
      this.scene.traverse((object) => {
        if (!homePositionObject && object.name.toLowerCase().includes('home_position')) {
          homePositionObject = object;
        }
      });

      // 如果找到了home_position
      if (homePositionObject) {
        const position = new THREE.Vector3();
        this._safeGetWorldPosition(homePositionObject, position);
        position.y += this.config.cameraHeight;

        console.log(`[RoamingController] 找到home_position位置: (${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)})`);
        return position;
      }

      // 设备关键词列表，用于识别设备
      const deviceKeywords = ['device', '设备', 'equipment', 'machine', 'server'];
      let deviceObject: THREE.Object3D | null = null;

      // 如果提供了特定设备名称，先尝试查找该设备
      if (deviceName) {
        // 在场景中查找指定名称的设备
        this.scene.traverse((object) => {
          if (!deviceObject && (object.name === deviceName || object.name.includes(deviceName))) {
            deviceObject = object;
          }
        });

        if (deviceObject) {
          const position = new THREE.Vector3();
          this._safeGetWorldPosition(deviceObject, position);
          position.y += this.config.cameraHeight;
          position.z -= 2; // 向后移动2米，便于观察设备

          console.log(
            `[RoamingController] 找到指定设备 "${deviceName}" 位置: (${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)})`
          );
          return position;
        }
      }

      // 如果没有找到指定设备或没有提供设备名称，查找任何设备
      // 首先查找包含设备关键词的对象
      this.scene.traverse((object) => {
        if (!deviceObject) {
          const name = object.name.toLowerCase();
          for (const keyword of deviceKeywords) {
            if (name.includes(keyword)) {
              deviceObject = object;
              break;
            }
          }
        }
      });

      // 如果找到了设备
      if (deviceObject) {
        const position = new THREE.Vector3();
        this._safeGetWorldPosition(deviceObject, position);
        position.y += this.config.cameraHeight;

        console.log(`[RoamingController] 找到设备位置: (${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)})`);
        return position;
      }

      console.warn('[RoamingController] 未找到任何设备，尝试查找任何可见的网格对象');

      // 如果找不到任何设备，尝试使用任何可见的网格对象
      this.scene.traverse((object) => {
        if (!deviceObject && object.visible && object instanceof THREE.Mesh) {
          deviceObject = object;
        }
      });

      if (deviceObject) {
        const position = new THREE.Vector3();
        this._safeGetWorldPosition(deviceObject, position);
        position.y += this.config.cameraHeight;

        console.log(`[RoamingController] 使用网格对象作为起始点: (${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)})`);
        return position;
      }
    } catch (error) {
      console.error('[RoamingController] 查找设备位置失败:', error);
    }

    // 即使出错，也返回一个默认位置，避免漫游失败
    return new THREE.Vector3(0, this.config.cameraHeight, 0);
  }

  /**
   * 安全地获取对象的世界位置
   * @param object 要获取位置的对象
   * @param target 目标向量，用于存储结果
   * @private
   */
  private _safeGetWorldPosition(object: THREE.Object3D, target: THREE.Vector3): void {
    try {
      // 尝试使用getWorldPosition方法
      object.getWorldPosition(target);
    } catch (error) {
      console.warn('[RoamingController] 使用getWorldPosition失败，尝试替代方法:', error);

      try {
        // 如果getWorldPosition失败，尝试使用position属性
        if (object.position) {
          target.copy(object.position);

          // 如果对象有父对象，需要考虑父对象的变换
          if (object.parent) {
            const worldMatrix = new THREE.Matrix4();
            object.parent.updateMatrixWorld(true);
            worldMatrix.copy(object.parent.matrixWorld);
            target.applyMatrix4(worldMatrix);
          }
        } else {
          // 如果都失败，使用默认位置
          target.set(0, this.config.cameraHeight, 0);
        }
      } catch (fallbackError) {
        console.error('[RoamingController] 获取对象位置完全失败，使用默认位置:', fallbackError);
        target.set(0, this.config.cameraHeight, 0);
      }
    }
  }

  /**
   * 收集场景中的墙体和地板对象
   * 优化版本：只在对象列表为空时才执行完整遍历
   */
  private _collectSceneObjects(): void {
    try {
      // 如果已经有墙体和地板对象，不需要重新收集
      if (this.wallObjects.length > 0 && this.floorObjects.length > 0) {
        return;
      }

      // 清空之前的对象
      this.wallObjects = [];
      this.floorObjects = [];

      // 遍历场景中的所有对象
      if (!this.scene) {
        console.warn('[RoamingController] 场景未初始化，无法收集对象');
        return;
      }

      console.log('[RoamingController] 开始收集场景对象...');

      // 使用Set来避免重复处理同一个对象
      const processedObjects = new Set<string>();

      // 墙体和地板的关键词
      const wallKeywords = ['wall', '墙', 'partition', 'barrier'];
      const floorKeywords = ['floor', '地板', '地面', 'ground', 'platform'];

      this.scene.traverse((object) => {
        // 跳过已处理的对象
        if (processedObjects.has(object.uuid)) return;
        processedObjects.add(object.uuid);

        // 只处理可见的网格对象
        if (!object.visible || !(object instanceof THREE.Mesh)) return;

        const name = object.name.toLowerCase();

        // 检测墙体对象
        let isWall = false;
        for (const keyword of wallKeywords) {
          if (name.includes(keyword)) {
            isWall = true;
            break;
          }
        }

        if (isWall) {
          this.wallObjects.push(object);
          return; // 如果是墙体，不需要检查是否是地板
        }

        // 检测地板对象
        for (const keyword of floorKeywords) {
          if (name.includes(keyword)) {
            this.floorObjects.push(object);
            break;
          }
        }
      });

      console.log(`[RoamingController] 收集到 ${this.wallObjects.length} 个墙体对象和 ${this.floorObjects.length} 个地板对象`);

      // 如果没有找到地板对象，尝试使用更宽松的条件
      if (this.floorObjects.length === 0) {
        console.warn('[RoamingController] 未找到地板对象，创建默认地板');
        this._createDefaultFloor();
      }
    } catch (error) {
      console.error('[RoamingController] 收集场景对象失败:', error);

      // 确保至少有一个地板对象
      if (this.floorObjects.length === 0) {
        this._createDefaultFloor();
      }
    }
  }

  /**
   * 创建默认地板
   */
  private _createDefaultFloor(): void {
    try {
      // 创建一个简单的平面作为默认地板
      const geometry = new THREE.PlaneGeometry(1000, 1000);
      const material = new THREE.MeshBasicMaterial({
        visible: false, // 不可见
        side: THREE.DoubleSide,
      });

      const floor = new THREE.Mesh(geometry, material);
      floor.rotation.x = -Math.PI / 2; // 水平放置
      floor.position.y = 0; // 在y=0位置
      floor.name = 'default_floor';

      // 添加到场景
      if (this.scene) {
        this.scene.add(floor);
      }

      // 添加到地板对象列表
      this.floorObjects.push(floor);

      console.log('[RoamingController] 已创建默认地板');
    } catch (error) {
      console.error('[RoamingController] 创建默认地板失败:', error);
    }
  }

  /**
   * 启用漫游控制
   * @private
   */
  private _enableRoamingControls(): void {
    if (!this.renderer || !this.renderer.domElement) {
      console.error('[RoamingController] 渲染器未初始化');
      return;
    }

    // 禁用其他控制器
    const controlManager = ControlManager.getInstance();
    if (controlManager) {
      controlManager.setControlsEnabled(false);
    }

    // 绑定事件处理函数
    window.addEventListener('keydown', this.keyDownHandler);
    window.addEventListener('keyup', this.keyUpHandler);
    document.addEventListener('pointerlockchange', this.pointerLockChangeHandler);
    document.addEventListener('pointerlockerror', this.pointerLockErrorHandler);
    this.renderer.domElement.addEventListener('mousedown', this.mouseDownHandler);
    this.renderer.domElement.addEventListener('mouseup', this.mouseUpHandler);
    this.renderer.domElement.addEventListener('mousemove', this.mouseMoveHandler);

    // 启用指针锁定
    try {
      this.renderer.domElement.requestPointerLock();
    } catch (error) {
      console.error('[RoamingController] 请求指针锁定失败:', error);
    }

    // 设置漫游状态
    this.state = RoamingState.ACTIVE;
    console.log('[RoamingController] 启用漫游控制');

    // 更新动画
    this._startUpdateLoop();
  }

  /**
   * 禁用漫游控制
   * @private
   */
  private _disableRoamingControls(): void {
    // 取消事件绑定
    window.removeEventListener('keydown', this.keyDownHandler);
    window.removeEventListener('keyup', this.keyUpHandler);
    document.removeEventListener('pointerlockchange', this.pointerLockChangeHandler);
    document.removeEventListener('pointerlockerror', this.pointerLockErrorHandler);

    if (this.renderer?.domElement) {
      this.renderer.domElement.removeEventListener('mousedown', this.mouseDownHandler);
      this.renderer.domElement.removeEventListener('mouseup', this.mouseUpHandler);
      this.renderer.domElement.removeEventListener('mousemove', this.mouseMoveHandler);
    }

    // 退出指针锁定状态
    if (document.pointerLockElement === this.renderer?.domElement) {
      document.exitPointerLock();
    }

    // 恢复其他控制器
    const controlManager = ControlManager.getInstance();
    if (controlManager) {
      controlManager.setControlsEnabled(true);
    }

    // 重置状态
    this.state = RoamingState.IDLE;
    this.movement = {
      forward: false,
      backward: false,
      left: false,
      right: false,
      up: false,
      accelerate: false,
    };

    // 停止更新循环
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    console.log('[RoamingController] 禁用漫游控制');
  }

  /**
   * 启动更新循环
   * @private
   */
  private _startUpdateLoop(): void {
    let lastTime = performance.now();

    const update = () => {
      if (this.state !== RoamingState.ACTIVE) return;

      const currentTime = performance.now();
      const deltaTime = (currentTime - lastTime) / 1000;
      lastTime = currentTime;

      // 更新移动和旋转
      this._updateMovement(deltaTime);

      // 继续循环
      this.animationFrameId = requestAnimationFrame(update);
    };

    // 开始更新循环
    this.animationFrameId = requestAnimationFrame(update);
  }

  /**
   * 更新移动
   * @private
   */
  private _updateMovement(deltaTime: number): void {
    if (!this.camera) return;

    // 计算移动速度
    const moveSpeed = this.movement.accelerate ? this.config.moveSpeed * this.config.runSpeedMultiplier : this.config.moveSpeed;

    // 根据相机朝向计算移动方向
    const forward = new THREE.Vector3(0, 0, -1).applyEuler(this.camera.rotation);
    const right = new THREE.Vector3(1, 0, 0).applyEuler(this.camera.rotation);

    // 计算目标速度
    this.targetVelocity.set(0, 0, 0);

    if (this.movement.forward) this.targetVelocity.add(forward);
    if (this.movement.backward) this.targetVelocity.sub(forward);
    if (this.movement.right) this.targetVelocity.add(right);
    if (this.movement.left) this.targetVelocity.sub(right);

    // 归一化速度向量（如果不为零）
    if (this.targetVelocity.lengthSq() > 0) {
      this.targetVelocity.normalize().multiplyScalar(moveSpeed);
    }

    // 直接使用非平滑移动模式
    this.currentVelocity.copy(this.targetVelocity);

    // 如果有移动意图，检测碰撞
    if (this.currentVelocity.lengthSq() > 0) {
      // 计算下一帧的位置
      const nextPosition = this.camera.position.clone().addScaledVector(this.currentVelocity, deltaTime);

      // 检测碰撞
      if (this._checkCollision(this.camera.position, nextPosition)) {
        // 如果检测到碰撞，停止移动
        this.currentVelocity.set(0, 0, 0);
      }
    }

    // 应用移动
    this.camera.position.addScaledVector(this.currentVelocity, deltaTime);

    // 确保相机不会低于地面
    this._ensurePositionAboveFloor(this.camera.position);
  }

  /**
   * 处理键盘按下事件
   * @private
   */
  private _handleKeyDown(event: KeyboardEvent): void {
    if (this.state !== RoamingState.ACTIVE) return;

    switch (event.code) {
      case 'KeyW':
      case 'ArrowUp':
        this.movement.forward = true;
        break;
      case 'KeyS':
      case 'ArrowDown':
        this.movement.backward = true;
        break;
      case 'KeyA':
      case 'ArrowLeft':
        this.movement.left = true;
        break;
      case 'KeyD':
      case 'ArrowRight':
        this.movement.right = true;
        break;
      case 'Space':
        this.movement.up = true;
        break;
      case 'ShiftLeft':
        this.movement.accelerate = true;
        break;
    }
  }

  /**
   * 处理键盘抬起事件
   * @private
   */
  private _handleKeyUp(event: KeyboardEvent): void {
    if (this.state !== RoamingState.ACTIVE) return;

    switch (event.code) {
      case 'KeyW':
      case 'ArrowUp':
        this.movement.forward = false;
        break;
      case 'KeyS':
      case 'ArrowDown':
        this.movement.backward = false;
        break;
      case 'KeyA':
      case 'ArrowLeft':
        this.movement.left = false;
        break;
      case 'KeyD':
      case 'ArrowRight':
        this.movement.right = false;
        break;
      case 'Space':
        this.movement.up = false;
        break;
      case 'ShiftLeft':
        this.movement.accelerate = false;
        break;
    }
  }

  /**
   * 处理鼠标移动事件
   * @private
   */
  private _handleMouseMove(event: MouseEvent): void {
    if (this.state !== RoamingState.ACTIVE) return;
    if (!document.pointerLockElement) return;

    const movementX = event.movementX || 0;
    const movementY = event.movementY || 0;

    // 修正：调整旋转方向，使其符合第一人称视角的直觉
    // 水平旋转：向右移动鼠标，视角向右旋转（负值）
    this.targetRotation.y -= movementX * this.config.mouseSensitivity;
    // 垂直旋转：向上移动鼠标，视角向上旋转（负值）
    this.targetRotation.x -= movementY * this.config.mouseSensitivity;

    // 限制垂直旋转角度，防止过度旋转和翻转
    this.targetRotation.x = Math.max(-Math.PI / 2 + 0.1, Math.min(Math.PI / 2 - 0.1, this.targetRotation.x));

    // 确保相机旋转顺序正确（YXZ顺序对第一人称视角很重要）
    this.camera.rotation.order = 'YXZ';

    // 应用旋转
    this.camera.rotation.x = this.targetRotation.x;
    this.camera.rotation.y = this.targetRotation.y;
    this.camera.rotation.z = 0; // 确保z轴旋转为0，防止视角倾斜
  }

  /**
   * 处理鼠标按下事件
   * @private
   */
  private _handleMouseDown(): void {
    if (this.state !== RoamingState.ACTIVE) return;
    if (this.renderer) {
      this.renderer.domElement.requestPointerLock();
    }
  }

  /**
   * 处理鼠标抬起事件
   * @private
   */
  private _handleMouseUp(): void {
    // 处理鼠标抬起事件
  }

  /**
   * 处理指针锁定变化事件
   * @private
   */
  private _handlePointerLockChange(): void {
    if (this.renderer && document.pointerLockElement === this.renderer.domElement) {
      console.log('[RoamingController] 进入指针锁定状态');
    } else {
      console.log('[RoamingController] 退出指针锁定状态');
    }
  }

  /**
   * 处理指针锁定错误事件
   * @private
   */
  private _handlePointerLockError(): void {
    console.error('[RoamingController] 指针锁定失败');
  }

  /**
   * 恢复相机位置和状态
   * 包括恢复原始FOV值，从超广角视角恢复到正常视角
   * @private
   */
  private _restoreCamera(): void {
    // 恢复相机位置和旋转
    this.camera.position.copy(this.originalCameraPosition);
    this.camera.rotation.copy(this.originalCameraRotation);

    // 恢复原始FOV值（从超广角视角恢复到正常视角）
    this.camera.fov = this.originalCameraFov;
    this.camera.updateProjectionMatrix();

    console.log(`[RoamingController] 已恢复相机原始FOV: ${this.originalCameraFov}度`);
    console.log(
      `[RoamingController] 已恢复相机原始位置: (${this.originalCameraPosition.x.toFixed(2)}, ${this.originalCameraPosition.y.toFixed(2)}, ${this.originalCameraPosition.z.toFixed(2)})`
    );

    // 重置所有移动和旋转状态
    this.position.copy(this.originalCameraPosition);
    this.targetRotation.x = 0;
    this.targetRotation.y = 0;
    this.velocity.set(0, 0, 0);
    this.currentVelocity.set(0, 0, 0);
    this.targetVelocity.set(0, 0, 0);

    // 更新控制器
    const controlManager = ControlManager.getInstance();
    if (controlManager) {
      const controls = controlManager.getOrbitControls();
      if (controls) {
        // 恢复原始控制器目标点
        controls.target.copy(this.originalControlsTarget);
        console.log(
          `[RoamingController] 已恢复控制器原始目标点: (${this.originalControlsTarget.x.toFixed(2)}, ${this.originalControlsTarget.y.toFixed(2)}, ${this.originalControlsTarget.z.toFixed(2)})`
        );

        // 更新控制器
        controls.update();
      }
    }
  }

  /**
   * 检测从当前位置到目标位置的移动是否会发生碰撞
   * @param currentPosition 当前位置
   * @param targetPosition 目标位置
   * @returns 如果检测到碰撞返回true，否则返回false
   */
  private _checkCollision(currentPosition: THREE.Vector3, targetPosition: THREE.Vector3): boolean {
    // 如果墙体对象列表为空，先收集场景对象
    if (this.wallObjects.length === 0) {
      this._collectSceneObjects();
    }

    // 如果仍然没有墙体对象，无法检测碰撞
    if (this.wallObjects.length === 0) {
      return false;
    }

    // 计算移动方向和距离
    const direction = new THREE.Vector3().subVectors(targetPosition, currentPosition).normalize();
    const distance = currentPosition.distanceTo(targetPosition);

    // 设置射线检测
    this.raycaster.set(currentPosition, direction);
    this.raycaster.far = distance + 0.5; // 增加一点余量

    // 只检测墙体对象
    const intersects = this.raycaster.intersectObjects(this.wallObjects, true);

    // 如果有交点，表示会发生碰撞
    return intersects.length > 0;
  }
}
