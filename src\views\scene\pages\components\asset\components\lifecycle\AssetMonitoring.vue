<template>
  <div class="h-full flex flex-col">
    <!-- 监控概览卡片 -->
    <div class="grid grid-cols-5 gap-[0.8vw] mb-[1vw]">
      <div v-for="stat in monitoringStats" :key="stat.label" class="bg-black/20 rounded border border-white/10 p-[0.6vw]">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-[0.5vw] text-gray-400">{{ stat.label }}</div>
            <div :class="['text-[0.8vw] font-semibold', stat.valueClass]">{{ stat.value }}</div>
          </div>
          <div :class="['text-[1vw]', stat.iconClass]">{{ stat.icon }}</div>
        </div>
      </div>
    </div>

    <!-- 操作工具栏 -->
    <div class="flex justify-between items-center mb-[1vw]">
      <div class="flex items-center space-x-[0.8vw]">
        <button
          class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
          @click="refreshMonitoring"
        >
          <ReloadOutlined class="mr-[0.2vw]" />
          刷新监控
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
          @click="showAlarmRules"
        >
          <BellOutlined class="mr-[0.2vw]" />
          告警规则
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors"
          @click="exportMonitoringData"
        >
          <DownloadOutlined class="mr-[0.2vw]" />
          导出数据
        </button>
      </div>

      <div class="flex items-center space-x-[0.6vw]">
        <select v-model="statusFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部状态</option>
          <option value="online">在线</option>
          <option value="offline">离线</option>
          <option value="warning">告警</option>
          <option value="error">故障</option>
        </select>
        <select v-model="typeFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部类型</option>
          <option value="server">服务器</option>
          <option value="network">网络设备</option>
          <option value="storage">存储设备</option>
          <option value="power">电力设备</option>
        </select>
        <input
          v-model="searchQuery"
          placeholder="搜索资产名称、IP地址..."
          class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none w-[15vw]"
        />
        <button class="px-[0.6vw] py-[0.3vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors" @click="searchAssets">
          <SearchOutlined />
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-black/20 rounded border border-white/10 overflow-hidden">
      <div class="overflow-auto h-full">
        <table class="w-full text-[0.6vw]">
          <thead class="bg-blue-500/20 sticky top-0">
            <tr>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">资产名称</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">IP地址</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">类型</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">CPU使用率</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">内存使用率</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">磁盘使用率</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">网络流量</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">状态</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="asset in paginatedAssets" :key="asset.id" class="hover:bg-white/5 transition-colors">
              <td class="p-[0.6vw] text-white border-b border-white/5">{{ asset.name }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ asset.ipAddress }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ getTypeText(asset.type) }}</td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex items-center">
                  <div class="w-[3vw] bg-gray-600 rounded-full h-[0.3vw] mr-[0.4vw]">
                    <div
                      :class="['h-full rounded-full', asset.cpuUsage > 80 ? 'bg-red-500' : asset.cpuUsage > 60 ? 'bg-yellow-500' : 'bg-green-500']"
                      :style="{ width: asset.cpuUsage + '%' }"
                    ></div>
                  </div>
                  <span :class="['text-[0.5vw]', asset.cpuUsage > 80 ? 'text-red-400' : asset.cpuUsage > 60 ? 'text-yellow-400' : 'text-green-400']"
                    >{{ asset.cpuUsage }}%</span
                  >
                </div>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex items-center">
                  <div class="w-[3vw] bg-gray-600 rounded-full h-[0.3vw] mr-[0.4vw]">
                    <div
                      :class="[
                        'h-full rounded-full',
                        asset.memoryUsage > 80 ? 'bg-red-500' : asset.memoryUsage > 60 ? 'bg-yellow-500' : 'bg-green-500',
                      ]"
                      :style="{ width: asset.memoryUsage + '%' }"
                    ></div>
                  </div>
                  <span
                    :class="['text-[0.5vw]', asset.memoryUsage > 80 ? 'text-red-400' : asset.memoryUsage > 60 ? 'text-yellow-400' : 'text-green-400']"
                    >{{ asset.memoryUsage }}%</span
                  >
                </div>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex items-center">
                  <div class="w-[3vw] bg-gray-600 rounded-full h-[0.3vw] mr-[0.4vw]">
                    <div
                      :class="['h-full rounded-full', asset.diskUsage > 80 ? 'bg-red-500' : asset.diskUsage > 60 ? 'bg-yellow-500' : 'bg-green-500']"
                      :style="{ width: asset.diskUsage + '%' }"
                    ></div>
                  </div>
                  <span :class="['text-[0.5vw]', asset.diskUsage > 80 ? 'text-red-400' : asset.diskUsage > 60 ? 'text-yellow-400' : 'text-green-400']"
                    >{{ asset.diskUsage }}%</span
                  >
                </div>
              </td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ asset.networkTraffic }}</td>
              <td class="p-[0.6vw] border-b border-white/5">
                <span
                  :class="[
                    'px-[0.4vw] py-[0.1vw] rounded text-[0.5vw]',
                    asset.status === 'online'
                      ? 'bg-green-500/20 text-green-400'
                      : asset.status === 'offline'
                        ? 'bg-gray-500/20 text-gray-400'
                        : asset.status === 'warning'
                          ? 'bg-yellow-500/20 text-yellow-400'
                          : 'bg-red-500/20 text-red-400',
                  ]"
                >
                  {{ getStatusText(asset.status) }}
                </span>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex space-x-[0.4vw]">
                  <button class="bg-transparent text-blue-400 hover:text-blue-300 text-[0.5vw]" @click="viewMetrics(asset)"> 监控 </button>
                  <button class="bg-transparent text-orange-400 hover:text-orange-300 text-[0.5vw]" @click="viewAlarms(asset)"> 告警 </button>
                  <button class="bg-transparent text-green-400 hover:text-green-300 text-[0.5vw]" @click="viewLogs(asset)"> 日志 </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-between items-center mt-[0.8vw] text-[0.6vw] text-gray-400">
      <div>共 {{ filteredAssets.length }} 条记录</div>
      <div class="flex items-center space-x-[0.4vw]">
        <button
          :disabled="currentPage === 1"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="text-white">{{ currentPage }} / {{ totalPages }}</span>
        <button
          :disabled="currentPage === totalPages"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage++"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 监控详情弹窗 -->
    <ModalDialog
      v-model:visible="showMetricsDialog"
      title="资产监控详情"
      width="80vw"
      height="70vh"
      :show-footer="false"
      @cancel="showMetricsDialog = false"
    >
      <AssetMetricsView :asset="selectedAsset" />
    </ModalDialog>

    <!-- 告警规则弹窗 -->
    <ModalDialog
      v-model:visible="showAlarmRulesDialog"
      title="告警规则管理"
      width="70vw"
      :show-footer="true"
      @confirm="saveAlarmRules"
      @cancel="showAlarmRulesDialog = false"
    >
      <AlarmRulesManager v-model:rules="alarmRules" />
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { ReloadOutlined, BellOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import AssetMetricsView from './forms/AssetMetricsView.vue';
  import AlarmRulesManager from './forms/AlarmRulesManager.vue';

  // 响应式数据
  const assets = ref([]);
  const selectedAsset = ref(null);
  const searchQuery = ref('');
  const statusFilter = ref('');
  const typeFilter = ref('');
  const currentPage = ref(1);
  const pageSize = ref(20);
  const showMetricsDialog = ref(false);
  const showAlarmRulesDialog = ref(false);
  const alarmRules = ref([]);

  // 监控统计
  const monitoringStats = ref([
    { label: '在线设备', value: '142', valueClass: 'text-green-400', icon: '🟢', iconClass: 'text-green-400' },
    { label: '离线设备', value: '8', valueClass: 'text-gray-400', icon: '⚫', iconClass: 'text-gray-400' },
    { label: '告警设备', value: '12', valueClass: 'text-yellow-400', icon: '🟡', iconClass: 'text-yellow-400' },
    { label: '故障设备', value: '3', valueClass: 'text-red-400', icon: '🔴', iconClass: 'text-red-400' },
    { label: '平均负载', value: '65%', valueClass: 'text-blue-400', icon: '📊', iconClass: 'text-blue-400' },
  ]);

  // 计算属性
  const filteredAssets = computed(() => {
    let result = assets.value;

    if (statusFilter.value) {
      result = result.filter((asset) => asset.status === statusFilter.value);
    }

    if (typeFilter.value) {
      result = result.filter((asset) => asset.type === typeFilter.value);
    }

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter((asset) => asset.name.toLowerCase().includes(query) || asset.ipAddress.toLowerCase().includes(query));
    }

    return result;
  });

  const totalPages = computed(() => Math.ceil(filteredAssets.value.length / pageSize.value));

  const paginatedAssets = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return filteredAssets.value.slice(start, end);
  });

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      online: '在线',
      offline: '离线',
      warning: '告警',
      error: '故障',
    };
    return statusMap[status] || status;
  };

  const getTypeText = (type) => {
    const typeMap = {
      server: '服务器',
      network: '网络设备',
      storage: '存储设备',
      power: '电力设备',
    };
    return typeMap[type] || type;
  };

  const searchAssets = () => {
    currentPage.value = 1;
  };

  const refreshMonitoring = () => {
    // 模拟刷新数据
    assets.value.forEach((asset) => {
      asset.cpuUsage = Math.floor(Math.random() * 100);
      asset.memoryUsage = Math.floor(Math.random() * 100);
      asset.diskUsage = Math.floor(Math.random() * 100);
      asset.networkTraffic = `${Math.floor(Math.random() * 500) + 50}MB/s`;

      // 根据使用率更新状态
      const maxUsage = Math.max(asset.cpuUsage, asset.memoryUsage, asset.diskUsage);
      if (maxUsage > 90) {
        asset.status = 'error';
      } else if (maxUsage > 75) {
        asset.status = 'warning';
      } else {
        asset.status = 'online';
      }
    });

    // 更新统计数据
    const onlineCount = assets.value.filter((a) => a.status === 'online').length;
    const warningCount = assets.value.filter((a) => a.status === 'warning').length;
    const errorCount = assets.value.filter((a) => a.status === 'error').length;
    const offlineCount = assets.value.filter((a) => a.status === 'offline').length;
    const avgLoad = Math.round(assets.value.reduce((sum, a) => sum + Math.max(a.cpuUsage, a.memoryUsage, a.diskUsage), 0) / assets.value.length);

    monitoringStats.value = [
      { label: '在线设备', value: onlineCount.toString(), valueClass: 'text-green-400', icon: '🟢', iconClass: 'text-green-400' },
      { label: '离线设备', value: offlineCount.toString(), valueClass: 'text-gray-400', icon: '⚫', iconClass: 'text-gray-400' },
      { label: '告警设备', value: warningCount.toString(), valueClass: 'text-yellow-400', icon: '🟡', iconClass: 'text-yellow-400' },
      { label: '故障设备', value: errorCount.toString(), valueClass: 'text-red-400', icon: '🔴', iconClass: 'text-red-400' },
      { label: '平均负载', value: `${avgLoad}%`, valueClass: 'text-blue-400', icon: '📊', iconClass: 'text-blue-400' },
    ];

    alert('监控数据已刷新！');
  };

  const showAlarmRules = () => {
    showAlarmRulesDialog.value = true;
  };

  const exportMonitoringData = () => {
    const exportData = filteredAssets.value.map((asset) => ({
      资产名称: asset.name,
      IP地址: asset.ipAddress,
      类型: getTypeText(asset.type),
      CPU使用率: `${asset.cpuUsage}%`,
      内存使用率: `${asset.memoryUsage}%`,
      磁盘使用率: `${asset.diskUsage}%`,
      网络流量: asset.networkTraffic,
      状态: getStatusText(asset.status),
    }));

    const headers = Object.keys(exportData[0] || {});
    const csvContent = [headers.join(','), ...exportData.map((row) => headers.map((header) => `"${row[header] || ''}"`).join(','))].join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `监控数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert('监控数据导出成功！');
  };

  const viewMetrics = (asset) => {
    selectedAsset.value = asset;
    showMetricsDialog.value = true;
  };

  const viewAlarms = (asset) => {
    const alarmCount = Math.floor(Math.random() * 5);
    if (alarmCount === 0) {
      alert(`${asset.name} 当前无告警信息`);
    } else {
      alert(`${asset.name} 当前有 ${alarmCount} 条告警信息：\n- CPU使用率过高\n- 内存使用率告警\n- 磁盘空间不足`);
    }
  };

  const viewLogs = (asset) => {
    alert(
      `正在查看 ${asset.name} 的系统日志...\n\n最近日志：\n[2024-01-20 10:30:15] 系统启动\n[2024-01-20 10:31:02] 服务正常运行\n[2024-01-20 11:15:33] CPU使用率达到85%\n[2024-01-20 11:20:45] 内存清理完成`
    );
  };

  const saveAlarmRules = () => {
    alert('告警规则保存成功！');
    showAlarmRulesDialog.value = false;
  };

  // 初始化数据
  onMounted(() => {
    assets.value = [
      {
        id: 1,
        name: 'Web服务器-01',
        ipAddress: '*************',
        type: 'server',
        cpuUsage: 45,
        memoryUsage: 67,
        diskUsage: 32,
        networkTraffic: '125MB/s',
        status: 'online',
      },
      {
        id: 2,
        name: '核心交换机-01',
        ipAddress: '***********',
        type: 'network',
        cpuUsage: 23,
        memoryUsage: 34,
        diskUsage: 15,
        networkTraffic: '2.3GB/s',
        status: 'warning',
      },
    ];
  });
</script>
