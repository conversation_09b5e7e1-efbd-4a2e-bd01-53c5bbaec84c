<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 趋势分析概览 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-chart-line mr-[0.4vw] text-blue-400"></i>
        能耗趋势分析概览
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in trendStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.trend" class="text-[0.5vw] mt-[0.2vw]" :class="stat.trendClass">
            {{ stat.trend }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：趋势图表和分析 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-chart-area mr-[0.4vw] text-blue-400"></i>
            能耗趋势图表
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="period in timePeriods"
              :key="period.key"
              @click="activePeriod = period.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activePeriod === period.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30',
              ]"
            >
              {{ period.label }}
            </button>
          </div>
        </div>

        <!-- 模拟图表区域 -->
        <div class="flex-1 bg-[#15274D]/30 rounded p-[0.6vw] mb-[0.6vw]">
          <div class="h-full flex items-center justify-center">
            <div class="text-center">
              <i class="fas fa-chart-line text-[3vw] text-blue-400/50 mb-[0.5vw]"></i>
              <div class="text-[0.7vw] text-gray-400">{{ currentPeriodData.title }}</div>
              <div class="text-[0.6vw] text-gray-500 mt-[0.2vw]">{{ currentPeriodData.description }}</div>
            </div>
          </div>
        </div>

        <!-- 趋势分析结论 -->
        <div class="bg-[#15274D]/30 rounded p-[0.6vw]">
          <div class="text-[0.6vw] text-white mb-[0.4vw] font-medium">趋势分析结论</div>
          <div class="space-y-[0.3vw]">
            <div v-for="conclusion in trendConclusions" :key="conclusion.id" class="flex items-start">
              <div class="w-[0.4vw] h-[0.4vw] rounded-full bg-blue-400 mt-[0.4vw] mr-[0.4vw] flex-shrink-0"></div>
              <span class="text-[0.6vw] text-gray-300">{{ conclusion.text }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：详细分析和预测 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 分类能耗分析 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-pie-chart mr-[0.4vw] text-blue-400"></i>
            分类能耗分析
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="category in energyCategories" :key="category.name" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ category.name }}</span>
                <span class="text-[0.6vw] text-blue-400">{{ category.consumption }}</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400 mb-[0.2vw]">
                <span>占比：{{ category.percentage }}%</span>
                <span>{{ category.trend }}</span>
              </div>
              <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                <div class="bg-blue-400 h-[0.3vw] rounded-full transition-all" :style="{ width: category.percentage + '%' }"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 峰谷分析 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-mountain mr-[0.4vw] text-blue-400"></i>
            峰谷用电分析
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="period in peakValleyAnalysis" :key="period.name" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ period.name }}</span>
                <span class="text-[0.6vw]" :class="getPeriodClass(period.type)">{{ period.consumption }}</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>时段：{{ period.timeRange }}</span>
                <span>电价：{{ period.price }}</span>
              </div>
            </div>
          </div>

          <div class="mt-[0.6vw] pt-[0.6vw] border-t border-gray-600">
            <div class="flex justify-between text-[0.6vw]">
              <span class="text-gray-400">峰谷差率</span>
              <span class="text-green-400">{{ peakValleyRatio }}</span>
            </div>
          </div>
        </div>

        <!-- 能耗预测 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-crystal-ball mr-[0.4vw] text-blue-400"></i>
            能耗预测
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="forecast in energyForecasts" :key="forecast.period" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ forecast.period }}</span>
                <span class="text-[0.6vw] text-blue-400">{{ forecast.predicted }}</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>置信度：{{ forecast.confidence }}</span>
                <span>变化：{{ forecast.change }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 异常检测 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-search mr-[0.4vw] text-blue-400"></i>
            异常检测结果
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="detection in anomalyDetections" :key="detection.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex items-center justify-between mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ detection.time }}</span>
                <span class="text-[0.5vw]" :class="getAnomalyClass(detection.level)">{{ detection.level }}</span>
              </div>
              <div class="text-[0.5vw] text-gray-400">{{ detection.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 趋势分析概览数据
  const trendStats = ref([
    {
      label: '月度能耗',
      value: '12,456 kWh',
      valueClass: 'text-blue-400',
      trend: '↓ 8.5%',
      trendClass: 'text-green-400',
    },
    {
      label: '日均能耗',
      value: '415 kWh',
      valueClass: 'text-green-400',
      trend: '↓ 5.2%',
      trendClass: 'text-green-400',
    },
    {
      label: '峰值负荷',
      value: '85.6 kW',
      valueClass: 'text-yellow-400',
      trend: '↓ 3.1%',
      trendClass: 'text-green-400',
    },
    {
      label: '负荷率',
      value: '78.5%',
      valueClass: 'text-blue-400',
      trend: '↑ 2.3%',
      trendClass: 'text-green-400',
    },
    {
      label: '预测准确率',
      value: '94.2%',
      valueClass: 'text-green-400',
      trend: '↑ 1.8%',
      trendClass: 'text-green-400',
    },
  ]);

  // 时间周期
  const timePeriods = ref([
    { key: 'day', label: '日' },
    { key: 'week', label: '周' },
    { key: 'month', label: '月' },
    { key: 'year', label: '年' },
  ]);

  const activePeriod = ref('month');

  // 当前周期数据
  const periodData = ref({
    day: {
      title: '日能耗趋势图',
      description: '显示24小时内的能耗变化曲线，峰值出现在14:00-16:00',
    },
    week: {
      title: '周能耗趋势图',
      description: '显示7天内的能耗变化，工作日能耗明显高于周末',
    },
    month: {
      title: '月能耗趋势图',
      description: '显示30天内的能耗变化，整体呈下降趋势，节能效果显著',
    },
    year: {
      title: '年能耗趋势图',
      description: '显示12个月的能耗变化，夏季制冷期能耗较高',
    },
  });

  const currentPeriodData = computed(() => periodData.value[activePeriod.value]);

  // 趋势分析结论
  const trendConclusions = ref([
    { id: 1, text: '整体能耗呈稳定下降趋势，节能措施效果显著' },
    { id: 2, text: '峰谷差率保持在合理范围内，负荷分布均衡' },
    { id: 3, text: '设备运行效率持续提升，维护策略有效' },
    { id: 4, text: '能耗预测模型准确率达到94.2%，可靠性高' },
  ]);

  // 分类能耗分析
  const energyCategories = ref([
    { name: '制冷系统', consumption: '4,856 kWh', percentage: 39, trend: '↓ 12%' },
    { name: '照明系统', consumption: '2,734 kWh', percentage: 22, trend: '↓ 18%' },
    { name: 'IT设备', consumption: '2,245 kWh', percentage: 18, trend: '↑ 3%' },
    { name: 'UPS系统', consumption: '1,621 kWh', percentage: 13, trend: '↓ 5%' },
    { name: '其他设备', consumption: '1,000 kWh', percentage: 8, trend: '↓ 8%' },
  ]);

  // 峰谷分析
  const peakValleyAnalysis = ref([
    { name: '峰时段', type: 'peak', consumption: '2,856 kWh', timeRange: '8:00-11:00, 18:00-23:00', price: '1.2元/kWh' },
    { name: '平时段', type: 'normal', consumption: '6,234 kWh', timeRange: '6:00-8:00, 11:00-18:00', price: '0.8元/kWh' },
    { name: '谷时段', type: 'valley', consumption: '3,366 kWh', timeRange: '23:00-6:00', price: '0.4元/kWh' },
  ]);

  const peakValleyRatio = ref('2.8:1');

  // 能耗预测
  const energyForecasts = ref([
    { period: '下周预测', predicted: '2,890 kWh', confidence: '95%', change: '↓ 3.2%' },
    { period: '下月预测', predicted: '11,850 kWh', confidence: '92%', change: '↓ 4.8%' },
    { period: '下季度预测', predicted: '35,200 kWh', confidence: '88%', change: '↓ 6.1%' },
  ]);

  // 异常检测
  const anomalyDetections = ref([
    { id: 1, time: '2024-02-28 14:30', level: '正常', description: '所有设备运行正常，无异常能耗' },
    { id: 2, time: '2024-02-28 10:15', level: '正常', description: '负荷切换正常，能耗在预期范围内' },
    { id: 3, time: '2024-02-28 08:45', level: '正常', description: '设备启动正常，能耗曲线符合预期' },
  ]);

  // 获取时段样式
  const getPeriodClass = (type) => {
    switch (type) {
      case 'peak':
        return 'text-red-400';
      case 'normal':
        return 'text-yellow-400';
      case 'valley':
        return 'text-green-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取异常等级样式
  const getAnomalyClass = (level) => {
    switch (level) {
      case '正常':
        return 'text-green-400';
      case '注意':
        return 'text-yellow-400';
      case '异常':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
