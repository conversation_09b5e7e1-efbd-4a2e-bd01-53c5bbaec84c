<template>
  <a-modal
    v-model:visible="visible"
    title="防控措施设置"
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formData" layout="vertical">
      <a-form-item label="体温阈值">
        <a-input-number
          v-model:value="formData.temperatureThreshold"
          :min="35"
          :max="42"
          :step="0.1"
          :precision="1"
          style="width: 200px"
        >
          <template #addonAfter>°C</template>
        </a-input-number>
      </a-form-item>

      <a-form-item label="防控要求">
        <a-checkbox-group v-model:value="formData.requirements">
          <a-checkbox value="healthCode">健康码检查</a-checkbox>
          <a-checkbox value="vaccine">疫苗接种要求</a-checkbox>
          <a-checkbox value="nucleicTest">核酸检测要求</a-checkbox>
        </a-checkbox-group>
      </a-form-item>

      <a-form-item label="核酸检测有效期">
        <a-input-number
          v-model:value="formData.nucleicTestValidDays"
          :min="1"
          :max="30"
          style="width: 200px"
        >
          <template #addonAfter>天</template>
        </a-input-number>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';

  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{ 'update:visible': [value: boolean]; success: [] }>();

  const formRef = ref();
  const formData = reactive({
    temperatureThreshold: 37.3,
    requirements: ['healthCode'],
    nucleicTestValidDays: 7,
  });

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleSubmit = async () => {
    try {
      message.success('防控措施设置成功');
      emit('success');
    } catch (error) {
      message.error('设置失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };
</script>
