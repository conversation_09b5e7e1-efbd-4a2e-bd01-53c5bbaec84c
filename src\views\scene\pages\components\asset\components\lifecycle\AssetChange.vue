<template>
  <div class="h-full flex flex-col">
    <!-- 操作工具栏 -->
    <div class="flex justify-between items-center mb-[1vw]">
      <div class="flex items-center space-x-[0.8vw]">
        <button
          class="px-[0.8vw] py-[0.4vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors bg-transparent"
          @click="showChangeDialog = true"
        >
          <EditOutlined class="mr-[0.2vw]" />
          新建变更
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
          @click="batchApprove"
          :disabled="selectedChanges.length === 0"
        >
          <CheckOutlined class="mr-[0.2vw]" />
          批量审批
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors"
          @click="showChangeCalendar"
        >
          <CalendarOutlined class="mr-[0.2vw]" />
          变更日历
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] bg-purple-500 text-white text-[0.6vw] rounded hover:bg-purple-600 transition-colors"
          @click="exportChanges"
        >
          <DownloadOutlined class="mr-[0.2vw]" />
          导出记录
        </button>
      </div>

      <div class="flex items-center space-x-[0.6vw]">
        <select v-model="statusFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部状态</option>
          <option value="draft">草稿</option>
          <option value="pending">待审批</option>
          <option value="approved">已批准</option>
          <option value="implementing">实施中</option>
          <option value="completed">已完成</option>
          <option value="failed">实施失败</option>
          <option value="rollback">已回滚</option>
        </select>
        <select v-model="typeFilter" class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none">
          <option value="">全部类型</option>
          <option value="config">配置变更</option>
          <option value="upgrade">升级变更</option>
          <option value="patch">补丁变更</option>
          <option value="emergency">紧急变更</option>
        </select>
        <input
          v-model="searchQuery"
          placeholder="搜索变更单号、资产名称..."
          class="bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.3vw] text-white text-[0.6vw] outline-none w-[15vw]"
        />
        <button class="px-[0.6vw] py-[0.3vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors" @click="searchChanges">
          <SearchOutlined />
        </button>
      </div>
    </div>

    <!-- 变更统计卡片 -->
    <div class="grid grid-cols-4 gap-[0.8vw] mb-[1vw]">
      <div v-for="stat in changeStats" :key="stat.label" class="bg-black/20 rounded border border-white/10 p-[0.6vw]">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-[0.5vw] text-gray-400">{{ stat.label }}</div>
            <div :class="['text-[0.8vw] font-semibold', stat.valueClass]">{{ stat.value }}</div>
          </div>
          <div :class="['text-[1vw]', stat.iconClass]">{{ stat.icon }}</div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-black/20 rounded border border-white/10 overflow-hidden">
      <div class="overflow-auto h-full">
        <table class="w-full text-[0.6vw]">
          <thead class="bg-blue-500/20 sticky top-0">
            <tr>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">
                <input type="checkbox" @change="toggleSelectAll" class="mr-[0.4vw]" />
                变更单号
              </th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">变更标题</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">变更类型</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">影响资产</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">申请人</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">计划时间</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">风险等级</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">状态</th>
              <th class="text-left p-[0.6vw] text-gray-300 border-b border-white/10">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="change in paginatedChanges" :key="change.id" class="hover:bg-white/5 transition-colors">
              <td class="p-[0.6vw] text-white border-b border-white/5">
                <input type="checkbox" :checked="selectedChanges.includes(change.id)" @change="toggleSelectChange(change.id)" class="mr-[0.4vw]" />
                {{ change.changeCode }}
              </td>
              <td class="p-[0.6vw] text-white border-b border-white/5">{{ change.title }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ getTypeText(change.type) }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ change.affectedAssets }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ change.applicant }}</td>
              <td class="p-[0.6vw] text-gray-300 border-b border-white/5">{{ change.plannedDate }}</td>
              <td class="p-[0.6vw] border-b border-white/5">
                <span
                  :class="[
                    'px-[0.4vw] py-[0.1vw] rounded text-[0.5vw]',
                    change.riskLevel === 'low'
                      ? 'bg-green-500/20 text-green-400'
                      : change.riskLevel === 'medium'
                        ? 'bg-yellow-500/20 text-yellow-400'
                        : 'bg-red-500/20 text-red-400',
                  ]"
                >
                  {{ getRiskText(change.riskLevel) }}
                </span>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <span
                  :class="[
                    'px-[0.4vw] py-[0.1vw] rounded text-[0.5vw]',
                    change.status === 'draft'
                      ? 'bg-gray-500/20 text-gray-400'
                      : change.status === 'pending'
                        ? 'bg-yellow-500/20 text-yellow-400'
                        : change.status === 'approved'
                          ? 'bg-blue-500/20 text-blue-400'
                          : change.status === 'implementing'
                            ? 'bg-purple-500/20 text-purple-400'
                            : change.status === 'completed'
                              ? 'bg-green-500/20 text-green-400'
                              : 'bg-red-500/20 text-red-400',
                  ]"
                >
                  {{ getStatusText(change.status) }}
                </span>
              </td>
              <td class="p-[0.6vw] border-b border-white/5">
                <div class="flex space-x-[0.4vw]">
                  <button
                    v-if="change.status === 'pending'"
                    class="text-green-400 hover:text-green-300 text-[0.5vw] bg-transparent"
                    @click="approveChange(change)"
                  >
                    审批
                  </button>
                  <button
                    v-if="change.status === 'approved'"
                    class="text-blue-400 hover:text-blue-300 text-[0.5vw] bg-transparent"
                    @click="implementChange(change)"
                  >
                    实施
                  </button>
                  <button class="text-orange-400 hover:text-orange-300 text-[0.5vw] bg-transparent" @click="viewChangeDetail(change)"> 详情 </button>
                  <button
                    v-if="change.status === 'implementing'"
                    class="text-red-400 hover:text-red-300 text-[0.5vw] bg-transparent"
                    @click="rollbackChange(change)"
                  >
                    回滚
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-between items-center mt-[0.8vw] text-[0.6vw] text-gray-400">
      <div>共 {{ filteredChanges.length }} 条记录</div>
      <div class="flex items-center space-x-[0.4vw]">
        <button
          :disabled="currentPage === 1"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="text-white">{{ currentPage }} / {{ totalPages }}</span>
        <button
          :disabled="currentPage === totalPages"
          class="px-[0.6vw] py-[0.2vw] bg-blue-500 text-white rounded disabled:bg-gray-500 disabled:cursor-not-allowed"
          @click="currentPage++"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 新建变更弹窗 -->
    <ModalDialog
      v-model:visible="showChangeDialog"
      title="新建变更申请"
      width="70vw"
      :show-footer="true"
      @confirm="confirmChange"
      @cancel="showChangeDialog = false"
    >
      <AssetChangeForm v-model:form-data="changeForm" />
    </ModalDialog>

    <!-- 变更日历弹窗 -->
    <ModalDialog
      v-model:visible="showCalendarDialog"
      title="变更日历"
      width="80vw"
      height="70vh"
      :show-footer="false"
      @cancel="showCalendarDialog = false"
    >
      <ChangeCalendarView :changes="changes" />
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { EditOutlined, CheckOutlined, CalendarOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import AssetChangeForm from './forms/AssetChangeForm.vue';
  import ChangeCalendarView from './forms/ChangeCalendarView.vue';

  // 响应式数据
  const changes = ref([]);
  const selectedChanges = ref([]);
  const searchQuery = ref('');
  const statusFilter = ref('');
  const typeFilter = ref('');
  const currentPage = ref(1);
  const pageSize = ref(20);
  const showChangeDialog = ref(false);
  const showCalendarDialog = ref(false);
  const changeForm = ref({});

  // 变更统计
  const changeStats = ref([
    { label: '本月变更', value: '24', valueClass: 'text-white', icon: '📝', iconClass: 'text-blue-400' },
    { label: '待审批', value: '8', valueClass: 'text-yellow-400', icon: '⏳', iconClass: 'text-yellow-400' },
    { label: '实施中', value: '3', valueClass: 'text-purple-400', icon: '🔄', iconClass: 'text-purple-400' },
    { label: '成功率', value: '95%', valueClass: 'text-green-400', icon: '✅', iconClass: 'text-green-400' },
  ]);

  // 计算属性
  const filteredChanges = computed(() => {
    let result = changes.value;

    if (statusFilter.value) {
      result = result.filter((change) => change.status === statusFilter.value);
    }

    if (typeFilter.value) {
      result = result.filter((change) => change.type === typeFilter.value);
    }

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(
        (change) =>
          change.changeCode.toLowerCase().includes(query) ||
          change.title.toLowerCase().includes(query) ||
          change.affectedAssets.toLowerCase().includes(query)
      );
    }

    return result;
  });

  const totalPages = computed(() => Math.ceil(filteredChanges.value.length / pageSize.value));

  const paginatedChanges = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return filteredChanges.value.slice(start, end);
  });

  // 方法
  const getStatusText = (status) => {
    const statusMap = {
      draft: '草稿',
      pending: '待审批',
      approved: '已批准',
      implementing: '实施中',
      completed: '已完成',
      failed: '实施失败',
      rollback: '已回滚',
    };
    return statusMap[status] || status;
  };

  const getTypeText = (type) => {
    const typeMap = {
      config: '配置变更',
      upgrade: '升级变更',
      patch: '补丁变更',
      emergency: '紧急变更',
    };
    return typeMap[type] || type;
  };

  const getRiskText = (risk) => {
    const riskMap = {
      low: '低风险',
      medium: '中风险',
      high: '高风险',
    };
    return riskMap[risk] || risk;
  };

  const toggleSelectAll = (event) => {
    if (event.target.checked) {
      selectedChanges.value = paginatedChanges.value.map((change) => change.id);
    } else {
      selectedChanges.value = [];
    }
  };

  const toggleSelectChange = (changeId) => {
    const index = selectedChanges.value.indexOf(changeId);
    if (index > -1) {
      selectedChanges.value.splice(index, 1);
    } else {
      selectedChanges.value.push(changeId);
    }
  };

  const searchChanges = () => {
    currentPage.value = 1;
  };

  const approveChange = (change) => {
    change.status = 'approved';
    change.approveDate = new Date().toISOString().split('T')[0];
  };

  const implementChange = (change) => {
    change.status = 'implementing';
    change.implementDate = new Date().toISOString().split('T')[0];
  };

  const rollbackChange = (change) => {
    change.status = 'rollback';
    change.rollbackDate = new Date().toISOString().split('T')[0];
  };

  const viewChangeDetail = (change) => {
    alert(
      `变更详情：\n\n变更单号：${change.changeCode}\n变更标题：${change.title}\n变更类型：${getTypeText(change.type)}\n影响资产：${change.affectedAssets}\n申请人：${change.applicant}\n计划时间：${change.plannedDate}\n风险等级：${getRiskText(change.riskLevel)}\n状态：${getStatusText(change.status)}`
    );
  };

  const batchApprove = () => {
    if (selectedChanges.value.length === 0) {
      alert('请选择要批量审批的变更');
      return;
    }

    const pendingChanges = changes.value.filter((change) => selectedChanges.value.includes(change.id) && change.status === 'pending');

    if (pendingChanges.length === 0) {
      alert('所选记录中没有待审批的变更');
      return;
    }

    pendingChanges.forEach((change) => {
      change.status = 'approved';
      change.approveDate = new Date().toISOString().split('T')[0];
    });

    selectedChanges.value = [];
    alert(`批量审批成功！共审批 ${pendingChanges.length} 条变更申请`);
  };

  const showChangeCalendar = () => {
    showCalendarDialog.value = true;
  };

  const exportChanges = () => {
    const exportData = filteredChanges.value.map((change) => ({
      变更单号: change.changeCode,
      变更标题: change.title,
      变更类型: getTypeText(change.type),
      影响资产: change.affectedAssets,
      申请人: change.applicant,
      计划时间: change.plannedDate,
      风险等级: getRiskText(change.riskLevel),
      状态: getStatusText(change.status),
    }));

    const headers = Object.keys(exportData[0] || {});
    const csvContent = [headers.join(','), ...exportData.map((row) => headers.map((header) => `"${row[header] || ''}"`).join(','))].join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `变更数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert('变更数据导出成功！');
  };

  const confirmChange = () => {
    if (!changeForm.value.title || !changeForm.value.type || !changeForm.value.affectedAssets) {
      alert('请填写必填字段：变更标题、变更类型、影响资产');
      return;
    }

    const newChange = {
      id: Date.now(),
      changeCode: `CHG-${new Date().getFullYear()}-${String(changes.value.length + 1).padStart(3, '0')}`,
      title: changeForm.value.title,
      type: changeForm.value.type,
      affectedAssets: changeForm.value.affectedAssets,
      applicant: changeForm.value.applicant || '当前用户',
      plannedDate: changeForm.value.plannedDate,
      riskLevel: changeForm.value.riskLevel || 'medium',
      status: 'draft',
    };

    changes.value.unshift(newChange);
    changeForm.value = {};
    showChangeDialog.value = false;

    alert('变更申请创建成功！');
  };

  // 初始化数据
  onMounted(() => {
    changes.value = [
      {
        id: 1,
        changeCode: 'CHG-2024-001',
        title: '服务器系统升级',
        type: 'upgrade',
        affectedAssets: 'Web服务器-01, 数据库服务器-01',
        applicant: '张三',
        plannedDate: '2024-01-20',
        riskLevel: 'medium',
        status: 'pending',
      },
      {
        id: 2,
        changeCode: 'CHG-2024-002',
        title: '网络配置优化',
        type: 'config',
        affectedAssets: '核心交换机-01',
        applicant: '李四',
        plannedDate: '2024-01-22',
        riskLevel: 'low',
        status: 'approved',
      },
    ];
  });
</script>
