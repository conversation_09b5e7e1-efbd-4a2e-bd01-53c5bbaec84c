import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getDeviceDetailByCode, type DeviceDetailData } from '@/api/scene';
import { defHttp } from '/@/utils/http/axios';

// 定义设备数据接口，匹配API返回的结构
interface DeviceDataItem {
  id: number;
  name: string;
  code: string;
  type: number; // 1-模拟量，2-设定值
  remark: string;
  dataTime: string;
  valueData: string;
  addr: number;
}

// 定义设备详情状态管理
export const useDeviceDetailStore = defineStore('deviceDetail', () => {
  // 状态
  const deviceData = ref<DeviceDataItem[] | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 加载设备数据
  const loadDeviceData = async (code: string): Promise<boolean> => {
    // 确保有效的设备代码
    if (!code) {
      console.error('[DeviceDetailStore] 无效的设备编码');
      error.value = '无效的设备编码';
      return false;
    }

    // 避免重复加载
    if (deviceData.value && deviceData.value.length > 0 && deviceData.value[0].code === code) {
      console.log(`[DeviceDetailStore] 设备数据 ${code} 已加载，跳过请求`);
      return true;
    }

    loading.value = true;
    error.value = null;

    try {
      console.log(`[DeviceDetailStore] 开始加载设备数据: ${code}`);
      // 调用API获取设备详细参数
      const response = await defHttp.get({
        url: '/baDeviceData/code',
        params: { code },
      });

      console.log('[DeviceDetailStore] 接收到API响应:', response);

      if (response && Array.isArray(response) && response.length > 0) {
        console.log('[DeviceDetailStore] 设置数据到store...');
        deviceData.value = response;
        console.log(`[DeviceDetailStore] 成功加载设备数据，共 ${response.length} 条记录，deviceData.value:`, deviceData.value);
        return true;
      } else {
        console.warn('[DeviceDetailStore] API返回的数据为空或格式异常:', response);
        error.value = '获取设备数据失败: 返回数据为空或格式异常';
        deviceData.value = null; // 确保在空数据情况下重置数据
        return false;
      }
    } catch (err) {
      console.error('[DeviceDetailStore] 获取设备数据出错:', err);
      error.value = '获取设备数据出错';
      deviceData.value = null; // 确保在错误情况下重置数据
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 清除设备数据
  const clearDeviceData = () => {
    deviceData.value = null;
    error.value = null;
    loading.value = false;
  };

  return {
    deviceData,
    loading,
    error,
    loadDeviceData,
    clearDeviceData,
  };
});
