<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 车辆出入统计 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-car mr-[0.4vw] text-blue-400"></i>
        车辆出入统计
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in vehicleStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.trend" class="text-[0.5vw] mt-[0.2vw]" :class="stat.trendClass">
            {{ stat.trend }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：车辆出入记录 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-list mr-[0.4vw] text-blue-400"></i>
            车辆出入记录
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="filter in recordFilters"
              :key="filter.key"
              @click="activeRecordFilter = filter.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activeRecordFilter === filter.key
                  ? 'bg-[#3B8EE6] text-white'
                  : 'bg-black/20 text-gray-300 hover:bg-black/30'
              ]"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>
        
        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div
              v-for="record in filteredRecords"
              :key="record.id"
              class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all"
            >
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <div class="w-[0.6vw] h-[0.6vw] rounded-full mr-[0.6vw]" 
                       :class="record.type === 'in' ? 'bg-green-400' : 'bg-orange-400'"></div>
                  <span class="text-[0.65vw] text-white font-medium">{{ record.plateNumber }}</span>
                </div>
                <span class="text-[0.6vw]" :class="getRecordTypeClass(record.type)">
                  {{ record.type === 'in' ? '进入' : '离开' }}
                </span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400 mb-[0.3vw]">
                <span>车主：{{ record.owner }}</span>
                <span>时间：{{ record.time }}</span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400">
                <span>入口：{{ record.entrance }}</span>
                <span v-if="record.duration">停车时长：{{ record.duration }}</span>
                <span v-if="record.parkingSpace">停车位：{{ record.parkingSpace }}</span>
              </div>
              <div v-if="record.image" class="mt-[0.3vw]">
                <div class="text-[0.5vw] text-gray-400 mb-[0.2vw]">车辆照片</div>
                <div class="w-[4vw] h-[2.5vw] bg-gray-600 rounded flex items-center justify-center">
                  <i class="fas fa-image text-gray-400"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：车辆信息和统计 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 车辆类型统计 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-chart-pie mr-[0.4vw] text-blue-400"></i>
            车辆类型统计
          </div>
          
          <div class="space-y-[0.4vw]">
            <div v-for="type in vehicleTypes" :key="type.name" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ type.name }}</span>
                <span class="text-[0.6vw] text-blue-400">{{ type.count }}辆</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400 mb-[0.2vw]">
                <span>占比：{{ type.percentage }}%</span>
                <span>今日：{{ type.todayCount }}辆</span>
              </div>
              <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                <div 
                  class="bg-blue-400 h-[0.3vw] rounded-full transition-all"
                  :style="{ width: type.percentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 停车时长分析 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-clock mr-[0.4vw] text-blue-400"></i>
            停车时长分析
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="duration in parkingDurations" :key="duration.range" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ duration.range }}</span>
              <div class="flex items-center gap-[0.4vw]">
                <span class="text-[0.6vw] text-white">{{ duration.count }}辆</span>
                <div class="w-[3vw] bg-black/30 rounded-full h-[0.3vw]">
                  <div 
                    class="bg-green-400 h-[0.3vw] rounded-full"
                    :style="{ width: duration.percentage + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="mt-[0.6vw] pt-[0.6vw] border-t border-gray-600">
            <div class="flex justify-between text-[0.6vw]">
              <span class="text-gray-400">平均停车时长</span>
              <span class="text-green-400">{{ averageParkingTime }}</span>
            </div>
          </div>
        </div>

        <!-- 高峰时段分析 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-chart-line mr-[0.4vw] text-blue-400"></i>
            高峰时段分析
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="peak in peakHours" :key="peak.time" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ peak.time }}</span>
                <span class="text-[0.6vw]" :class="getPeakLevelClass(peak.level)">{{ peak.level }}</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>进入：{{ peak.inCount }}辆</span>
                <span>离开：{{ peak.outCount }}辆</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 违规记录 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-exclamation-triangle mr-[0.4vw] text-blue-400"></i>
            违规记录
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="violation in violations" :key="violation.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ violation.time }}</span>
                <span class="text-[0.5vw]" :class="getViolationLevelClass(violation.level)">{{ violation.level }}</span>
              </div>
              <div class="text-[0.5vw] text-gray-400">{{ violation.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 车辆出入统计数据
  const vehicleStats = ref([
    { 
      label: '今日进入', 
      value: '156', 
      valueClass: 'text-green-400',
      trend: '↑ 12',
      trendClass: 'text-green-400'
    },
    { 
      label: '今日离开', 
      value: '142', 
      valueClass: 'text-orange-400',
      trend: '↑ 8',
      trendClass: 'text-green-400'
    },
    { 
      label: '当前在场', 
      value: '89', 
      valueClass: 'text-blue-400',
      trend: '↑ 4',
      trendClass: 'text-green-400'
    },
    { 
      label: '平均停留', 
      value: '4.5h', 
      valueClass: 'text-yellow-400',
      trend: '↓ 0.3h',
      trendClass: 'text-green-400'
    },
    { 
      label: '违规次数', 
      value: '0', 
      valueClass: 'text-green-400',
      trend: '→ 0',
      trendClass: 'text-green-400'
    },
  ]);

  // 记录筛选器
  const recordFilters = ref([
    { key: 'all', label: '全部' },
    { key: 'in', label: '进入' },
    { key: 'out', label: '离开' },
    { key: 'today', label: '今日' },
  ]);

  const activeRecordFilter = ref('all');

  // 车辆出入记录
  const vehicleRecords = ref([
    {
      id: 1,
      plateNumber: '京A12345',
      owner: '张工程师',
      type: 'in',
      time: '2024-02-28 08:25',
      entrance: '主入口',
      parkingSpace: 'A-01',
      image: true,
    },
    {
      id: 2,
      plateNumber: '京B67890',
      owner: '李经理',
      type: 'out',
      time: '2024-02-28 17:30',
      entrance: '主入口',
      duration: '8小时30分钟',
      image: true,
    },
    {
      id: 3,
      plateNumber: '京C11111',
      owner: '王技师',
      type: 'in',
      time: '2024-02-28 09:15',
      entrance: '侧门',
      parkingSpace: 'B-05',
      image: true,
    },
    {
      id: 4,
      plateNumber: '京D22222',
      owner: '访客',
      type: 'out',
      time: '2024-02-28 16:45',
      entrance: '访客通道',
      duration: '2小时15分钟',
      image: true,
    },
    {
      id: 5,
      plateNumber: '京E33333',
      owner: '赵主管',
      type: 'in',
      time: '2024-02-28 10:30',
      entrance: '主入口',
      parkingSpace: 'A-08',
      image: true,
    },
  ]);

  // 筛选后的记录
  const filteredRecords = computed(() => {
    if (activeRecordFilter.value === 'all') {
      return vehicleRecords.value;
    }
    if (activeRecordFilter.value === 'today') {
      return vehicleRecords.value.filter(record => record.time.includes('2024-02-28'));
    }
    return vehicleRecords.value.filter(record => record.type === activeRecordFilter.value);
  });

  // 车辆类型统计
  const vehicleTypes = ref([
    { name: '员工车辆', count: 125, percentage: 65, todayCount: 98 },
    { name: '访客车辆', count: 45, percentage: 23, todayCount: 32 },
    { name: '公务车辆', count: 18, percentage: 9, todayCount: 15 },
    { name: '其他车辆', count: 6, percentage: 3, todayCount: 3 },
  ]);

  // 停车时长分析
  const parkingDurations = ref([
    { range: '< 1小时', count: 15, percentage: 20 },
    { range: '1-4小时', count: 28, percentage: 35 },
    { range: '4-8小时', count: 32, percentage: 40 },
    { range: '> 8小时', count: 14, percentage: 18 },
  ]);

  const averageParkingTime = ref('4.5小时');

  // 高峰时段分析
  const peakHours = ref([
    { time: '08:00-09:00', level: '高峰', inCount: 45, outCount: 8 },
    { time: '12:00-13:00', level: '正常', inCount: 12, outCount: 18 },
    { time: '17:00-18:00', level: '高峰', inCount: 6, outCount: 52 },
    { time: '其他时段', level: '正常', inCount: 93, outCount: 64 },
  ]);

  // 违规记录
  const violations = ref([
    { id: 1, time: '2024-02-28 14:30', level: '正常', description: '所有车辆进出正常，无违规行为' },
    { id: 2, time: '2024-02-28 10:15', level: '正常', description: '车辆识别系统运行正常' },
    { id: 3, time: '2024-02-28 08:45', level: '正常', description: '停车秩序良好' },
  ]);

  // 获取记录类型样式
  const getRecordTypeClass = (type) => {
    return type === 'in' ? 'text-green-400' : 'text-orange-400';
  };

  // 获取高峰等级样式
  const getPeakLevelClass = (level) => {
    switch (level) {
      case '高峰':
        return 'text-red-400';
      case '正常':
        return 'text-green-400';
      case '低峰':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取违规等级样式
  const getViolationLevelClass = (level) => {
    switch (level) {
      case '正常':
        return 'text-green-400';
      case '轻微':
        return 'text-yellow-400';
      case '严重':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
