<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 人员出入统计概览 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-chart-bar mr-[0.4vw] text-blue-400"></i>
        人员出入统计概览
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in statsOverview" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.trend" class="text-[0.5vw] mt-[0.2vw]" :class="stat.trendClass">
            {{ stat.trend }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：时间段统计 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-clock mr-[0.4vw] text-blue-400"></i>
            时间段统计分析
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="period in timePeriods"
              :key="period.key"
              @click="activePeriod = period.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activePeriod === period.key
                  ? 'bg-[#3B8EE6] text-white'
                  : 'bg-black/20 text-gray-300 hover:bg-black/30'
              ]"
            >
              {{ period.label }}
            </button>
          </div>
        </div>
        
        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div
              v-for="timeSlot in currentTimeStats"
              :key="timeSlot.time"
              class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all"
            >
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <i :class="timeSlot.icon" class="mr-[0.6vw] text-blue-400"></i>
                  <span class="text-[0.65vw] text-white font-medium">{{ timeSlot.time }}</span>
                </div>
                <span class="text-[0.6vw]" :class="getPeakLevelClass(timeSlot.level)">
                  {{ timeSlot.level }}
                </span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400 mb-[0.3vw]">
                <span>进入：{{ timeSlot.inCount }}人</span>
                <span>离开：{{ timeSlot.outCount }}人</span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400">
                <span>净流入：{{ timeSlot.netFlow }}人</span>
                <span>累计在场：{{ timeSlot.totalPresent }}人</span>
              </div>
              <div class="mt-[0.3vw]">
                <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                  <div 
                    :class="getPeakProgressClass(timeSlot.level)"
                    class="h-[0.3vw] rounded-full transition-all"
                    :style="{ width: timeSlot.intensity + '%' }"
                  ></div>
                </div>
                <div class="text-[0.5vw] text-gray-400 mt-[0.2vw]">流量强度：{{ timeSlot.intensity }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：统计分析和趋势 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 部门流量统计 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-building mr-[0.4vw] text-blue-400"></i>
            部门流量统计
          </div>
          
          <div class="space-y-[0.4vw]">
            <div v-for="dept in departmentFlow" :key="dept.name" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ dept.name }}</span>
                <span class="text-[0.6vw] text-blue-400">{{ dept.totalFlow }}人次</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400 mb-[0.2vw]">
                <span>进入：{{ dept.inFlow }}人次</span>
                <span>离开：{{ dept.outFlow }}人次</span>
              </div>
              <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                <div 
                  class="bg-blue-400 h-[0.3vw] rounded-full transition-all"
                  :style="{ width: dept.percentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 访客统计 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-user-friends mr-[0.4vw] text-blue-400"></i>
            访客统计
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="visitor in visitorStats" :key="visitor.label" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ visitor.label }}</span>
              <span class="text-[0.6vw] text-white font-medium">{{ visitor.value }}</span>
            </div>
          </div>
          
          <div class="mt-[0.6vw] pt-[0.6vw] border-t border-gray-600">
            <div class="text-[0.6vw] text-white mb-[0.3vw]">访客类型分布</div>
            <div class="space-y-[0.2vw]">
              <div v-for="type in visitorTypes" :key="type.name" class="flex justify-between items-center">
                <span class="text-[0.5vw] text-gray-400">{{ type.name }}</span>
                <span class="text-[0.5vw] text-white">{{ type.count }}人</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 异常统计 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-shield-alt mr-[0.4vw] text-blue-400"></i>
            安全统计
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="security in securityStats" :key="security.label" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ security.label }}</span>
                <span class="text-[0.6vw]" :class="getSecurityStatusClass(security.status)">{{ security.value }}</span>
              </div>
              <div class="text-[0.5vw] text-gray-400">{{ security.description }}</div>
            </div>
          </div>
        </div>

        <!-- 趋势分析 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-chart-line mr-[0.4vw] text-blue-400"></i>
            趋势分析
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="trend in trendAnalysis" :key="trend.period" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ trend.period }}</span>
                <span class="text-[0.6vw]" :class="getTrendClass(trend.change)">{{ trend.change }}</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>平均流量：{{ trend.avgFlow }}人次/天</span>
                <span>峰值：{{ trend.peakFlow }}人次</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 预测分析 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-crystal-ball mr-[0.4vw] text-blue-400"></i>
            预测分析
          </div>
          
          <div class="space-y-[0.3vw]">
            <div v-for="forecast in forecastData" :key="forecast.period" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ forecast.period }}</span>
              <div class="text-right">
                <div class="text-[0.6vw] text-white">{{ forecast.predicted }}人次</div>
                <div class="text-[0.5vw] text-gray-400">置信度：{{ forecast.confidence }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 人员出入统计概览数据
  const statsOverview = ref([
    { 
      label: '日均流量', 
      value: '432', 
      valueClass: 'text-blue-400',
      trend: '↑ 8.5%',
      trendClass: 'text-green-400'
    },
    { 
      label: '峰值流量', 
      value: '89', 
      valueClass: 'text-yellow-400',
      trend: '↑ 5.2%',
      trendClass: 'text-green-400'
    },
    { 
      label: '平均停留', 
      value: '7.5h', 
      valueClass: 'text-green-400',
      trend: '↑ 0.3h',
      trendClass: 'text-green-400'
    },
    { 
      label: '访客比例', 
      value: '12.5%', 
      valueClass: 'text-orange-400',
      trend: '↑ 2.1%',
      trendClass: 'text-green-400'
    },
    { 
      label: '安全指数', 
      value: '100%', 
      valueClass: 'text-green-400',
      trend: '→ 0%',
      trendClass: 'text-green-400'
    },
  ]);

  // 时间周期
  const timePeriods = ref([
    { key: 'hourly', label: '小时' },
    { key: 'daily', label: '日' },
    { key: 'weekly', label: '周' },
  ]);

  const activePeriod = ref('hourly');

  // 小时统计数据
  const hourlyStats = ref([
    { time: '08:00-09:00', level: '高峰', inCount: 45, outCount: 8, netFlow: 37, totalPresent: 45, intensity: 95, icon: 'fas fa-arrow-up' },
    { time: '09:00-10:00', level: '正常', inCount: 12, outCount: 5, netFlow: 7, totalPresent: 52, intensity: 25, icon: 'fas fa-arrow-right' },
    { time: '10:00-11:00', level: '正常', inCount: 8, outCount: 3, netFlow: 5, totalPresent: 57, intensity: 18, icon: 'fas fa-arrow-right' },
    { time: '11:00-12:00', level: '正常', inCount: 6, outCount: 12, netFlow: -6, totalPresent: 51, intensity: 32, icon: 'fas fa-arrow-down' },
    { time: '12:00-13:00', level: '正常', inCount: 15, outCount: 18, netFlow: -3, totalPresent: 48, intensity: 42, icon: 'fas fa-arrow-down' },
    { time: '13:00-14:00', level: '正常', inCount: 18, outCount: 8, netFlow: 10, totalPresent: 58, intensity: 38, icon: 'fas fa-arrow-up' },
    { time: '17:00-18:00', level: '高峰', inCount: 5, outCount: 52, netFlow: -47, totalPresent: 11, intensity: 88, icon: 'fas fa-arrow-down' },
  ]);

  // 日统计数据
  const dailyStats = ref([
    { time: '周一', level: '高峰', inCount: 234, outCount: 198, netFlow: 36, totalPresent: 156, intensity: 85, icon: 'fas fa-calendar-day' },
    { time: '周二', level: '正常', inCount: 218, outCount: 205, netFlow: 13, totalPresent: 142, intensity: 72, icon: 'fas fa-calendar-day' },
    { time: '周三', level: '正常', inCount: 225, outCount: 212, netFlow: 13, totalPresent: 148, intensity: 76, icon: 'fas fa-calendar-day' },
    { time: '周四', level: '正常', inCount: 231, outCount: 219, netFlow: 12, totalPresent: 152, intensity: 78, icon: 'fas fa-calendar-day' },
    { time: '周五', level: '高峰', inCount: 245, outCount: 238, netFlow: 7, totalPresent: 165, intensity: 92, icon: 'fas fa-calendar-day' },
  ]);

  // 周统计数据
  const weeklyStats = ref([
    { time: '第1周', level: '正常', inCount: 1153, outCount: 1072, netFlow: 81, totalPresent: 156, intensity: 78, icon: 'fas fa-calendar-week' },
    { time: '第2周', level: '正常', inCount: 1198, outCount: 1145, netFlow: 53, totalPresent: 162, intensity: 82, icon: 'fas fa-calendar-week' },
    { time: '第3周', level: '高峰', inCount: 1267, outCount: 1189, netFlow: 78, totalPresent: 178, intensity: 95, icon: 'fas fa-calendar-week' },
    { time: '第4周', level: '正常', inCount: 1134, outCount: 1098, netFlow: 36, totalPresent: 145, intensity: 75, icon: 'fas fa-calendar-week' },
  ]);

  // 当前时间统计
  const currentTimeStats = computed(() => {
    switch (activePeriod.value) {
      case 'hourly':
        return hourlyStats.value;
      case 'daily':
        return dailyStats.value;
      case 'weekly':
        return weeklyStats.value;
      default:
        return hourlyStats.value;
    }
  });

  // 部门流量统计
  const departmentFlow = ref([
    { name: '技术部', totalFlow: 178, inFlow: 89, outFlow: 89, percentage: 35 },
    { name: '管理部', totalFlow: 128, inFlow: 64, outFlow: 64, percentage: 25 },
    { name: '运维部', totalFlow: 112, inFlow: 56, outFlow: 56, percentage: 22 },
    { name: '安全部', totalFlow: 72, inFlow: 36, outFlow: 36, percentage: 14 },
    { name: '其他部门', totalFlow: 42, inFlow: 21, outFlow: 21, percentage: 8 },
  ]);

  // 访客统计
  const visitorStats = ref([
    { label: '今日访客', value: '28人' },
    { label: '本周访客', value: '156人' },
    { label: '本月访客', value: '678人' },
    { label: '平均停留', value: '2.5小时' },
  ]);

  // 访客类型
  const visitorTypes = ref([
    { name: '商务访客', count: 18 },
    { name: '技术交流', count: 8 },
    { name: '政府检查', count: 2 },
  ]);

  // 安全统计
  const securityStats = ref([
    { 
      label: '身份验证', 
      value: '100%', 
      status: 'normal',
      description: '所有人员身份验证通过'
    },
    { 
      label: '健康检测', 
      value: '正常', 
      status: 'normal',
      description: '体温检测和健康码验证正常'
    },
    { 
      label: '安全事件', 
      value: '0起', 
      status: 'normal',
      description: '无安全事件发生'
    },
  ]);

  // 趋势分析
  const trendAnalysis = ref([
    { period: '本周', change: '↑ 8.5%', avgFlow: 432, peakFlow: 89 },
    { period: '本月', change: '↑ 12.3%', avgFlow: 398, peakFlow: 95 },
    { period: '本季度', change: '↑ 15.7%', avgFlow: 365, peakFlow: 102 },
  ]);

  // 预测数据
  const forecastData = ref([
    { period: '明日预测', predicted: 445, confidence: '95%' },
    { period: '下周预测', predicted: 2890, confidence: '92%' },
    { period: '下月预测', predicted: 12560, confidence: '88%' },
  ]);

  // 获取峰值等级样式
  const getPeakLevelClass = (level) => {
    switch (level) {
      case '高峰':
        return 'text-red-400';
      case '正常':
        return 'text-green-400';
      case '低峰':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取峰值进度条样式
  const getPeakProgressClass = (level) => {
    switch (level) {
      case '高峰':
        return 'bg-red-400';
      case '正常':
        return 'bg-green-400';
      case '低峰':
        return 'bg-blue-400';
      default:
        return 'bg-gray-400';
    }
  };

  // 获取安全状态样式
  const getSecurityStatusClass = (status) => {
    switch (status) {
      case 'normal':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'danger':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取趋势样式
  const getTrendClass = (trend) => {
    if (trend.includes('↑')) return 'text-green-400';
    if (trend.includes('↓')) return 'text-red-400';
    return 'text-gray-400';
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
