import * as THREE from 'three';
import { PhysicsManager, PhysicsObjectType, PhysicsShapeType } from './PhysicsManager';
import { PlayerPhysicsController, PlayerPhysicsConfig } from './PlayerPhysicsController';
import { SceneManager } from '../SceneManager';

/**
 * 物理工厂类
 * 负责创建和管理物理对象
 */
export class PhysicsFactory {
  private static instance: PhysicsFactory;
  private physicsManager: PhysicsManager;
  private playerController: PlayerPhysicsController;
  private scene: THREE.Scene;
  private wallObjects: THREE.Object3D[] = [];
  private floorObjects: THREE.Object3D[] = [];
  private wallColliders: string[] = [];
  private floorColliders: string[] = [];
  private isInitialized: boolean = false;

  /**
   * 获取单例实例
   */
  public static getInstance(): PhysicsFactory {
    if (!PhysicsFactory.instance) {
      PhysicsFactory.instance = new PhysicsFactory();
    }
    return PhysicsFactory.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    this.physicsManager = PhysicsManager.getInstance();
    this.playerController = PlayerPhysicsController.getInstance();
    this.scene = SceneManager.getInstance().scene;
  }

  /**
   * 初始化物理工厂
   * @param playerConfig 玩家物理配置
   */
  public initialize(playerConfig?: Partial<PlayerPhysicsConfig>): void {
    if (this.isInitialized) return;

    // 启动物理引擎
    this.physicsManager.start();

    // 初始化玩家物理控制器
    this.playerController.initialize(playerConfig);

    this.isInitialized = true;
    console.log('[PhysicsFactory] 物理工厂初始化完成');
  }

  /**
   * 收集场景中的墙体和地板对象
   * 性能优化：使用缓存减少重复计算
   */
  public collectSceneObjects(): void {
    this.wallObjects = [];
    this.floorObjects = [];

    // 使用Set来避免重复处理同一个对象
    const processedObjects = new Set<string>();

    // 优先处理场景中的直接子对象，通常这些是主要结构
    for (const object of this.scene.children) {
      this._processObjectForCollision(object, processedObjects);
    }

    console.log(`[PhysicsFactory] 收集到 ${this.wallObjects.length} 个墙体对象和 ${this.floorObjects.length} 个地板对象`);

    // 如果没有找到地板对象，尝试使用更宽松的检测条件
    if (this.floorObjects.length === 0) {
      console.warn('[PhysicsFactory] 未找到明确的地板对象，尝试使用更宽松的检测条件');

      // 重置已处理对象集合
      processedObjects.clear();

      // 使用更宽松的条件再次遍历场景
      this.scene.traverse((object) => {
        // 跳过已处理的对象
        if (!object.visible || !(object instanceof THREE.Mesh) || processedObjects.has(object.uuid)) return;

        processedObjects.add(object.uuid);

        // 检查对象的位置和法线方向，如果是水平的且在场景底部，可能是地板
        const position = object.position;
        const geometry = object.geometry;

        // 如果对象在场景底部且是水平的，可能是地板
        if (geometry && position.y < 0.5) {
          // 检查是否有法线向上的面，使用更高效的检测方法
          let hasUpFacingNormal = this._hasUpFacingNormal(geometry);

          if (hasUpFacingNormal) {
            this.floorObjects.push(object);
          }
        }
      });

      console.log(`[PhysicsFactory] 使用宽松条件后收集到 ${this.floorObjects.length} 个地板对象`);
    }

    // 如果仍然没有找到地板对象，创建一个默认地板
    if (this.floorObjects.length === 0) {
      console.warn('[PhysicsFactory] 无法找到任何地板对象，创建默认地板');
      this._createDefaultFloor();
    }
  }

  /**
   * 处理单个对象的碰撞检测
   * @param object 要处理的对象
   * @param processedObjects 已处理对象的集合
   */
  private _processObjectForCollision(object: THREE.Object3D, processedObjects: Set<string>): void {
    // 跳过已处理的对象
    if (processedObjects.has(object.uuid)) return;
    processedObjects.add(object.uuid);

    // 只处理可见的网格对象
    if (object.visible && object instanceof THREE.Mesh) {
      const name = object.name.toLowerCase();

      // 检测墙体对象
      if (name.includes('wall') || name.includes('墙')) {
        this.wallObjects.push(object);
      }
      // 检测地板对象
      else if (name.includes('floor') || name.includes('地板') || name.includes('地面') || name.includes('ground')) {
        this.floorObjects.push(object);
      }
    }

    // 递归处理子对象，但限制深度以提高性能
    if (object.children.length > 0 && object.children.length < 100) {
      // 避免处理过多子对象
      for (const child of object.children) {
        this._processObjectForCollision(child, processedObjects);
      }
    }
  }

  /**
   * 检查几何体是否有向上的法线
   * 性能优化：使用采样而不是检查所有法线
   * @param geometry 几何体
   * @returns 是否有向上的法线
   */
  private _hasUpFacingNormal(geometry: THREE.BufferGeometry): boolean {
    if (!geometry.attributes || !geometry.attributes.normal) return false;

    const normals = geometry.attributes.normal.array;
    const count = normals.length / 3;

    // 对于大型几何体，使用采样而不是检查所有法线
    const sampleSize = Math.min(count, 100); // 最多检查100个法线
    const step = Math.max(1, Math.floor(count / sampleSize));

    for (let i = 1; i < normals.length; i += 3 * step) {
      // 检查是否有向上的法线 (y > 0.7)，稍微放宽条件
      if (normals[i] > 0.7) {
        return true;
      }
    }

    return false;
  }

  /**
   * 创建默认地板
   * 当无法找到地板对象时，创建一个不可见的物理地板
   */
  private _createDefaultFloor(): void {
    // 创建一个平面几何体作为默认地板
    const geometry = new THREE.PlaneGeometry(1000, 1000);
    const material = new THREE.MeshBasicMaterial({
      visible: false, // 不可见
    });
    const floor = new THREE.Mesh(geometry, material);

    // 设置为水平放置
    floor.rotation.x = -Math.PI / 2;
    floor.position.y = 0;
    floor.name = 'default_floor';

    // 添加到场景
    this.scene.add(floor);

    // 添加到地板对象列表
    this.floorObjects.push(floor);

    console.log('[PhysicsFactory] 已创建默认地板');
  }

  /**
   * 创建墙体碰撞器
   * 极度性能优化：使用超简化的碰撞形状，强制批量处理
   */
  public createWallColliders(): void {
    // 清除之前的墙体碰撞器
    this.clearWallColliders();

    // 极度性能优化：无论墙体数量多少，都使用优化策略
    // 直接创建极简化的墙体碰撞器
    this._createSuperOptimizedWallColliders();

    console.log(`[PhysicsFactory] 创建了 ${this.wallColliders.length} 个极度优化的墙体碰撞器`);
  }

  /**
   * 创建超级优化的墙体碰撞器
   * 极度性能优化：将所有墙体合并为少数几个简单碰撞器
   */
  private _createSuperOptimizedWallColliders(): void {
    // 如果没有墙体对象，直接返回
    if (this.wallObjects.length === 0) return;

    // 将墙体分为最多4个区域，每个区域创建一个碰撞器
    const maxGroups = Math.min(4, Math.ceil(this.wallObjects.length / 20));

    // 计算所有墙体的包围盒
    const bbox = new THREE.Box3();
    for (const wall of this.wallObjects) {
      bbox.expandByObject(wall);
    }

    // 创建分区
    const center = new THREE.Vector3();
    bbox.getCenter(center);

    // 创建分区的墙体组
    const groups: THREE.Object3D[][] = Array(maxGroups)
      .fill(null)
      .map(() => []);

    // 将墙体分配到最近的分区
    for (const wall of this.wallObjects) {
      const position = new THREE.Vector3();
      if (wall instanceof THREE.Mesh && wall.geometry) {
        position.copy(wall.position);
      } else {
        // 获取对象的世界位置
        wall.getWorldPosition(position);
      }

      // 确定分区索引 (简化为基于x和z坐标的象限)
      const groupIndex = (position.x > center.x ? 1 : 0) + (position.z > center.z ? 2 : 0);
      groups[groupIndex % maxGroups].push(wall);
    }

    // 为每个分区创建一个碰撞器
    for (let i = 0; i < maxGroups; i++) {
      const group = groups[i];
      if (group.length === 0) continue;

      // 计算分区包围盒
      const groupBbox = new THREE.Box3();
      for (const wall of group) {
        groupBbox.expandByObject(wall);
      }

      // 获取包围盒尺寸和中心
      const size = new THREE.Vector3();
      groupBbox.getSize(size);
      const groupCenter = new THREE.Vector3();
      groupBbox.getCenter(groupCenter);

      // 创建简化的盒体碰撞器
      const geometry = new THREE.BoxGeometry(size.x * 0.95, size.y, size.z * 0.95);
      const material = new THREE.MeshBasicMaterial({ visible: false });
      const boxMesh = new THREE.Mesh(geometry, material);
      boxMesh.position.copy(groupCenter);
      boxMesh.name = `wall_group_collider_${i}`;

      // 创建物理对象
      const physicsObject = this.physicsManager.addPhysicsObject(boxMesh, PhysicsObjectType.STATIC, PhysicsShapeType.BOX, { simplify: true });

      if (physicsObject) {
        this.wallColliders.push(physicsObject.uuid);
      }
    }
  }

  // 移除未使用的优化墙体碰撞器方法，使用超级优化版本替代

  /**
   * 创建地板碰撞器
   * 极度性能优化：使用单一平面碰撞器代替所有地板
   */
  public createFloorColliders(): void {
    // 清除之前的地板碰撞器
    this.clearFloorColliders();

    // 极度性能优化：无论地板数量多少，都使用单一平面碰撞器
    this._createSuperOptimizedFloorCollider();

    console.log(`[PhysicsFactory] 创建了 ${this.floorColliders.length} 个极度优化的地板碰撞器`);
  }

  /**
   * 创建超级优化的地板碰撞器
   * 极度性能优化：创建一个简单的平面作为所有地板的碰撞器
   */
  private _createSuperOptimizedFloorCollider(): void {
    // 如果没有地板对象，创建一个默认地板
    if (this.floorObjects.length === 0) {
      this._createDefaultFloor();
    }

    // 计算所有地板的包围盒
    const bbox = new THREE.Box3();

    for (const floor of this.floorObjects) {
      bbox.expandByObject(floor);
    }

    // 获取包围盒的尺寸和中心
    const size = new THREE.Vector3();
    bbox.getSize(size);
    const center = new THREE.Vector3();
    bbox.getCenter(center);

    // 创建一个超大的平面作为地板碰撞器，确保覆盖整个场景
    // 使用比实际尺寸大1.5倍的平面，确保玩家不会掉出边界
    const planeSize = Math.max(size.x, size.z) * 1.5;
    const geometry = new THREE.PlaneGeometry(planeSize, planeSize);
    const material = new THREE.MeshBasicMaterial({ visible: false });
    const planeMesh = new THREE.Mesh(geometry, material);

    // 设置平面位置和旋转
    planeMesh.position.set(center.x, bbox.min.y, center.z);
    planeMesh.rotation.x = -Math.PI / 2;
    planeMesh.name = 'super_optimized_floor_collider';

    // 创建物理对象 - 使用平面形状，计算量最小
    const physicsObject = this.physicsManager.addPhysicsObject(planeMesh, PhysicsObjectType.STATIC, PhysicsShapeType.PLANE);

    if (physicsObject) {
      this.floorColliders.push(physicsObject.uuid);
    }

    console.log(`[PhysicsFactory] 创建了超级优化地板碰撞器（原始地板: ${this.floorObjects.length}）`);
  }

  /**
   * 清除墙体碰撞器
   */
  public clearWallColliders(): void {
    // 移除所有墙体碰撞器
    for (const uuid of this.wallColliders) {
      this.physicsManager.removePhysicsObject(uuid);
    }

    // 清空墙体碰撞器列表
    this.wallColliders = [];
  }

  /**
   * 清除地板碰撞器
   */
  public clearFloorColliders(): void {
    // 移除所有地板碰撞器
    for (const uuid of this.floorColliders) {
      this.physicsManager.removePhysicsObject(uuid);
    }

    // 清空地板碰撞器列表
    this.floorColliders = [];
  }

  /**
   * 清除所有碰撞器
   */
  public clearAllColliders(): void {
    this.clearWallColliders();
    this.clearFloorColliders();
  }

  /**
   * 设置玩家位置
   * @param position 位置
   */
  public setPlayerPosition(position: THREE.Vector3): void {
    this.playerController.setPosition(position);
  }

  /**
   * 获取玩家位置
   */
  public getPlayerPosition(): THREE.Vector3 {
    return this.playerController.getPosition();
  }

  /**
   * 设置玩家速度
   * @param velocity 速度
   */
  public setPlayerVelocity(velocity: THREE.Vector3): void {
    this.playerController.setVelocity(velocity);
  }

  /**
   * 获取玩家速度
   */
  public getPlayerVelocity(): THREE.Vector3 {
    return this.playerController.getVelocity();
  }

  /**
   * 设置相机方向
   * @param direction 方向向量
   * @param quaternion 四元数
   */
  public setCameraDirection(direction: THREE.Vector3, quaternion: THREE.Quaternion): void {
    this.playerController.setCameraDirection(direction, quaternion);
  }

  /**
   * 设置移动输入
   * @param movement 移动输入
   */
  public setMovement(movement: { forward: boolean; backward: boolean; left: boolean; right: boolean; jump: boolean }): void {
    this.playerController.setMovement(movement);
  }

  /**
   * 更新玩家物理
   * @param deltaTime 时间增量（秒）
   */
  public updatePlayer(deltaTime: number): void {
    this.playerController.update(deltaTime);
  }

  /**
   * 检查玩家是否在地面上
   */
  public isPlayerOnGround(): boolean {
    return this.playerController.isOnGround();
  }

  /**
   * 设置调试模式
   * @param enabled 是否启用
   */
  public setDebugMode(enabled: boolean): void {
    this.physicsManager.setDebugMode(enabled);
  }

  /**
   * 检查物理工厂是否已初始化
   * @returns 是否已初始化
   */
  public getInitializationState(): boolean {
    return this.isInitialized;
  }

  /**
   * 销毁物理工厂
   */
  public dispose(): void {
    // 清除所有碰撞器
    this.clearAllColliders();

    // 销毁玩家物理控制器
    this.playerController.dispose();

    // 停止物理引擎
    this.physicsManager.stop();

    this.isInitialized = false;
  }
}
