import * as THREE from 'three';
import { CameraController } from '../CameraController';
import { ControlManager } from '../control/ControlManager';
import { SceneManager } from '../SceneManager';
import { PathDrawingTool, PathPoint } from './PathDrawingTool';
import { gsap } from 'gsap';
import { useGlobalThreeStore } from '../../store/globalThreeStore';
import { HighPerformanceTransparencyManager } from '../HighPerformanceTransparencyManager';

/**
 * 巡检状态枚举
 */
export enum PatrolState {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
}

/**
 * 巡检方向枚举
 */
export enum PatrolDirection {
  FORWARD = 'forward',
  BACKWARD = 'backward',
}

/**
 * 巡检终点处理模式枚举
 */
export enum PatrolEndpointMode {
  REVERSE = 'reverse', // 到达终点后反向返回（原有的180度旋转模式）
  LOOP = 'loop', // 到达终点后循环回起点继续巡检
}

/**
 * 巡检配置接口
 */
export interface PatrolConfig {
  speed: number; // 移动速度（米/秒）
  turnSpeed: number; // 转向速度
  cameraHeight: number; // 相机高度（米）
  lookAheadDistance: number; // 前视距离（米）
  pathResolution: number; // 路径分辨率（点之间的最小距离，米）
  showPathLine: boolean; // 是否显示路径线
  pathLineColor: number; // 路径线颜色
  pathLineWidth: number; // 路径线宽度
  endpointMode: PatrolEndpointMode; // 终点处理模式
  loopTransitionDuration: number; // 循环模式下从终点回到起点的过渡动画时间（秒）
  dynamicLookAhead: boolean; // 是否启用动态前视距离
  showPathMarkers: boolean; // 是否显示路径标记
  adaptiveCameraHeight: boolean; // 是否启用自适应相机高度
  smoothTurning: boolean; // 是否启用平滑转弯
  cameraFov: number; // 相机视场角(FOV)，值越大视角越广
  useUltraWideFov: boolean; // 是否启用超广角效果
}

/**
 * 巡检事件接口
 */
export interface PatrolEvents {
  onStart?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  onStop?: () => void;
  onDirectionChange?: (direction: PatrolDirection) => void;
  onPathComplete?: () => void;
  onPositionUpdate?: (position: THREE.Vector3, progress: number) => void;
}

/**
 * 自定义巡检控制器
 * 实现沿用户绘制路径的自动巡检，支持往返循环
 */
export class CustomPatrolController {
  private static instance: CustomPatrolController | null = null;

  // 基础组件
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private controlManager: ControlManager;

  // 巡检状态
  private state: PatrolState = PatrolState.IDLE;
  private direction: PatrolDirection = PatrolDirection.FORWARD;
  private pathPoints: THREE.Vector3[] = [];
  private interpolatedPath: THREE.Vector3[] = [];
  private currentIndex: number = 0;
  private currentPosition: THREE.Vector3 = new THREE.Vector3();
  private currentLookAt: THREE.Vector3 = new THREE.Vector3();
  private targetLookAt: THREE.Vector3 = new THREE.Vector3();
  private progress: number = 0;

  // 可视化对象
  private pathLine: THREE.Line | null = null;
  private pathMarkers: THREE.Mesh[] = [];

  // 路径可视化透明度设置
  private readonly normalOpacity: number = 1.0; // 正常状态下的不透明度
  private readonly patrolOpacity: number = 0.4; // 巡检状态下的不透明度
  private readonly hiddenOpacity: number = 0.0; // 完全隐藏状态下的不透明度
  private isPathTransparent: boolean = false; // 路径是否处于半透明状态
  private isPathHidden: boolean = false; // 路径是否处于完全隐藏状态

  // 动画控制
  private animationId: number | null = null;

  // 事件回调
  private events: PatrolEvents = {};

  // 相机FOV相关
  private originalFov: number = 75; // 保存原始FOV值

  // 配置
  private config: PatrolConfig = {
    speed: 3.0, // 移动速度（米/秒）- 降低速度使移动更平滑
    turnSpeed: 1.5, // 转向速度 - 降低转向速度使视角变化更平滑
    cameraHeight: 1.7, // 相机高度（米）
    lookAheadDistance: 5.0, // 前视距离（米）- 增加前视距离使转弯更平滑
    pathResolution: 0.2, // 路径分辨率（米）- 增加路径点密度
    showPathLine: true, // 显示路径线
    pathLineColor: 0x3b82f6, // 路径线颜色（蓝色）
    pathLineWidth: 2, // 路径线宽度
    endpointMode: PatrolEndpointMode.LOOP, // 默认使用循环模式
    loopTransitionDuration: 1.5, // 循环过渡动画时间（秒）
    dynamicLookAhead: true, // 启用动态前视距离
    showPathMarkers: true, // 显示路径标记
    adaptiveCameraHeight: false, // 默认不启用自适应相机高度
    smoothTurning: true, // 启用平滑转弯
    cameraFov: 110, // 超广角FOV值（默认值为110度，标准FOV通常为60-75度）
    useUltraWideFov: true, // 默认启用超广角效果
  };

  /**
   * 获取单例实例
   */
  public static getInstance(): CustomPatrolController {
    if (!CustomPatrolController.instance) {
      CustomPatrolController.instance = new CustomPatrolController();
    }
    return CustomPatrolController.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    // 获取必要组件
    this.scene = SceneManager.getInstance().scene;
    this.camera = CameraController.getInstance().camera as THREE.PerspectiveCamera;
    this.controlManager = ControlManager.getInstance();
  }

  /**
   * 设置路径点
   * @param points 路径点数组
   */
  public setPathPoints(points: PathPoint[]): void {
    // 提取位置数组
    this.pathPoints = points.map((point) => point.position.clone());

    // 生成插值路径
    this._generateInterpolatedPath();

    // 创建路径可视化
    this._createPathVisualization();

    // Console logs removed for clean interface during patrol
  }

  /**
   * 生成插值路径
   * 将原始路径点之间插入更多点，使用样条曲线使路径更平滑
   */
  private _generateInterpolatedPath(): void {
    if (this.pathPoints.length < 2) {
      this.interpolatedPath = [...this.pathPoints];
      return;
    }

    const interpolatedPath: THREE.Vector3[] = [];

    // 如果只有两个点，使用简单的线性插值
    if (this.pathPoints.length === 2) {
      // 添加第一个点
      interpolatedPath.push(this.pathPoints[0].clone());

      const startPoint = this.pathPoints[0];
      const endPoint = this.pathPoints[1];
      const distance = startPoint.distanceTo(endPoint);

      // 计算需要插入的点数
      const segmentCount = Math.max(1, Math.floor(distance / this.config.pathResolution));

      // 插入点
      for (let j = 1; j <= segmentCount; j++) {
        const t = j / segmentCount;
        const interpolatedPoint = new THREE.Vector3().lerpVectors(startPoint, endPoint, t);
        interpolatedPath.push(interpolatedPoint);
      }
    } else {
      // 使用样条曲线插值
      // 创建曲线
      const curve = new THREE.CatmullRomCurve3(this.pathPoints);

      // 计算曲线总长度（近似）
      let totalLength = 0;
      for (let i = 0; i < this.pathPoints.length - 1; i++) {
        totalLength += this.pathPoints[i].distanceTo(this.pathPoints[i + 1]);
      }

      // 计算需要的点数
      const pointCount = Math.max(50, Math.ceil(totalLength / this.config.pathResolution));

      // 生成均匀分布的点
      for (let i = 0; i <= pointCount; i++) {
        const t = i / pointCount;
        const point = curve.getPoint(t);
        interpolatedPath.push(point);
      }
    }

    this.interpolatedPath = interpolatedPath;

    // 确保路径点之间的距离不会太小，这可能导致抖动
    this._optimizePathPoints();
  }

  /**
   * 优化路径点，移除过于接近的点
   */
  private _optimizePathPoints(): void {
    if (this.interpolatedPath.length <= 2) return;

    const optimizedPath: THREE.Vector3[] = [];
    optimizedPath.push(this.interpolatedPath[0]);

    let lastAddedPoint = this.interpolatedPath[0];
    const minDistance = this.config.pathResolution * 0.5; // 最小距离阈值

    for (let i = 1; i < this.interpolatedPath.length; i++) {
      const currentPoint = this.interpolatedPath[i];
      const distance = lastAddedPoint.distanceTo(currentPoint);

      if (distance >= minDistance) {
        optimizedPath.push(currentPoint);
        lastAddedPoint = currentPoint;
      }
    }

    // 确保添加最后一个点
    const lastPoint = this.interpolatedPath[this.interpolatedPath.length - 1];
    if (lastAddedPoint !== lastPoint) {
      optimizedPath.push(lastPoint);
    }

    this.interpolatedPath = optimizedPath;
  }

  /**
   * 创建路径可视化
   */
  private _createPathVisualization(): void {
    // 移除现有可视化
    this._removePathVisualization();

    if (!this.config.showPathLine || this.interpolatedPath.length < 2) return;

    // 创建路径线 - 使用渐变色显示方向
    const points = this.interpolatedPath;
    const geometry = new THREE.BufferGeometry().setFromPoints(points);

    // 创建颜色数组，实现渐变效果
    const colors = [];
    const color1 = new THREE.Color(this.config.pathLineColor);
    const color2 = new THREE.Color(0x4ade80); // 终点颜色（绿色）

    for (let i = 0; i < points.length; i++) {
      const ratio = i / (points.length - 1);
      const color = new THREE.Color().lerpColors(color1, color2, ratio);
      colors.push(color.r, color.g, color.b);
    }

    // 设置颜色属性
    geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));

    // 使用顶点颜色材质，支持透明度
    const material = new THREE.LineBasicMaterial({
      vertexColors: true,
      linewidth: this.config.pathLineWidth,
      transparent: true,
      opacity: this.isPathTransparent ? this.patrolOpacity : this.normalOpacity,
    });

    this.pathLine = new THREE.Line(geometry, material);
    this.scene.add(this.pathLine);

    // 如果启用了路径标记，添加起点和终点标记
    if (this.config.showPathMarkers) {
      this._createPathMarkers();
    }

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 创建路径标记（起点和终点）
   */
  private _createPathMarkers(): void {
    if (this.interpolatedPath.length < 2) return;

    // 创建起点标记（蓝色球体）
    const startPoint = this.interpolatedPath[0].clone();
    startPoint.y += 0.1; // 稍微抬高一点，使其更明显，但高度减小

    const startMarkerGeometry = new THREE.SphereGeometry(0.12, 16, 16); // 减小到原来的40%
    const startMarkerMaterial = new THREE.MeshBasicMaterial({
      color: this.config.pathLineColor,
      transparent: true,
      opacity: this.isPathTransparent ? this.patrolOpacity : this.normalOpacity,
    });
    const startMarker = new THREE.Mesh(startMarkerGeometry, startMarkerMaterial);
    startMarker.position.copy(startPoint);
    this.scene.add(startMarker);
    this.pathMarkers.push(startMarker);

    // 创建终点标记（绿色球体）
    const endPoint = this.interpolatedPath[this.interpolatedPath.length - 1].clone();
    endPoint.y += 0.1; // 稍微抬高一点，使其更明显，但高度减小

    const endMarkerGeometry = new THREE.SphereGeometry(0.12, 16, 16); // 减小到原来的40%
    const endMarkerMaterial = new THREE.MeshBasicMaterial({
      color: 0x4ade80,
      transparent: true,
      opacity: this.isPathTransparent ? this.patrolOpacity : this.normalOpacity,
    });
    const endMarker = new THREE.Mesh(endMarkerGeometry, endMarkerMaterial);
    endMarker.position.copy(endPoint);
    this.scene.add(endMarker);
    this.pathMarkers.push(endMarker);
  }

  /**
   * 移除路径可视化
   */
  private _removePathVisualization(): void {
    // 移除路径线
    if (this.pathLine) {
      this.scene.remove(this.pathLine);
      this.pathLine = null;
    }

    // 移除路径标记
    this.pathMarkers.forEach((marker) => {
      this.scene.remove(marker);
    });
    this.pathMarkers = [];
  }

  /**
   * 设置路径为半透明状态
   * 在巡检开始时调用，使路径线条和点位球体变为半透明
   */
  private _setPathTransparent(): void {
    if (this.isPathTransparent) return; // 已经是半透明状态，无需重复设置

    this.isPathTransparent = true;

    // 设置路径线透明度
    if (this.pathLine && this.pathLine.material) {
      const material = this.pathLine.material as THREE.LineBasicMaterial;
      material.transparent = true;
      material.opacity = this.patrolOpacity;
      material.needsUpdate = true;
    }

    // 设置路径点位球体透明度
    this.pathMarkers.forEach((marker) => {
      if (marker.material) {
        const material = marker.material as THREE.MeshBasicMaterial;
        material.transparent = true;
        material.opacity = this.patrolOpacity;
        material.needsUpdate = true;
      }
    });

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 恢复路径正常显示状态
   * 在巡检结束时调用，恢复路径线条和点位球体的原始显示效果
   */
  private _resetPathTransparency(): void {
    if (!this.isPathTransparent) return; // 已经是正常状态，无需重置

    this.isPathTransparent = false;

    // 恢复路径线透明度
    if (this.pathLine && this.pathLine.material) {
      const material = this.pathLine.material as THREE.LineBasicMaterial;
      material.opacity = this.normalOpacity;
      material.needsUpdate = true;
    }

    // 恢复路径点位球体透明度
    this.pathMarkers.forEach((marker) => {
      if (marker.material) {
        const material = marker.material as THREE.MeshBasicMaterial;
        material.opacity = this.normalOpacity;
        material.needsUpdate = true;
      }
    });

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 完全隐藏路径可视化
   * 在巡检开始时调用，完全隐藏路径线条和点位球体
   */
  private _hidePathVisualization(): void {
    if (this.isPathHidden) return; // 已经是隐藏状态，无需重复设置

    this.isPathHidden = true;
    this.isPathTransparent = false; // 重置半透明状态

    // 设置路径线透明度为0（完全隐藏）
    if (this.pathLine && this.pathLine.material) {
      const material = this.pathLine.material as THREE.LineBasicMaterial;
      material.transparent = true;
      material.opacity = this.hiddenOpacity;
      material.needsUpdate = true;
    }

    // 设置路径点位球体透明度为0（完全隐藏）
    this.pathMarkers.forEach((marker) => {
      if (marker.material) {
        const material = marker.material as THREE.MeshBasicMaterial;
        material.transparent = true;
        material.opacity = this.hiddenOpacity;
        material.needsUpdate = true;
      }
    });

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
    console.log('[CustomPatrolController] 路径可视化已完全隐藏');
  }

  /**
   * 显示路径可视化
   * 在巡检结束或被用户手动停止时调用，恢复显示路径线条和点位球体
   */
  private _showPathVisualization(): void {
    if (!this.isPathHidden) return; // 已经是显示状态，无需操作

    this.isPathHidden = false;

    // 恢复路径线透明度为正常值
    if (this.pathLine && this.pathLine.material) {
      const material = this.pathLine.material as THREE.LineBasicMaterial;
      material.opacity = this.normalOpacity;
      material.needsUpdate = true;
    }

    // 恢复路径点位球体透明度为正常值
    this.pathMarkers.forEach((marker) => {
      if (marker.material) {
        const material = marker.material as THREE.MeshBasicMaterial;
        material.opacity = this.normalOpacity;
        material.needsUpdate = true;
      }
    });

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
    console.log('[CustomPatrolController] 路径可视化已恢复显示');
  }

  /**
   * 开始巡检
   * @param events 事件回调
   */
  public start(events?: PatrolEvents): void {
    try {
      console.log('[CustomPatrolController] 开始执行start方法');

      // 检查播放功能是否激活
      const globalThreeStore = useGlobalThreeStore();
      if (globalThreeStore.isPlayActive) {
        console.warn('[CustomPatrolController] 无法开始巡检：播放功能正在运行中');
        if (window.$message) {
          window.$message.warning('播放功能正在运行中，请先停止播放再使用巡检功能');
        }
        return;
      }

      // 如果已经在运行，先停止
      if (this.state === PatrolState.RUNNING) {
        console.log('[CustomPatrolController] 巡检已在运行中，先停止');
        this.stop();
      }

      // 检查路径是否有效
      console.log('[CustomPatrolController] 检查路径点，当前插值路径点数量:', this.interpolatedPath.length);
      console.log('[CustomPatrolController] 原始路径点数量:', this.pathPoints.length);

      if (this.interpolatedPath.length < 2) {
        console.warn('[CustomPatrolController] 无法开始巡检：插值路径点不足');

        // 如果插值路径无效但原始路径有效，尝试重新生成插值路径
        if (this.pathPoints.length >= 2) {
          console.log('[CustomPatrolController] 尝试重新生成插值路径');
          this._generateInterpolatedPath();
          this._createPathVisualization();

          // 再次检查插值路径
          if (this.interpolatedPath.length < 2) {
            console.error('[CustomPatrolController] 重新生成插值路径失败，仍然无法开始巡检');
            return;
          }

          console.log('[CustomPatrolController] 重新生成插值路径成功，路径点数量:', this.interpolatedPath.length);
        } else {
          console.error('[CustomPatrolController] 原始路径点不足，无法开始巡检');
          return;
        }
      }

      // 设置事件回调
      if (events) {
        console.log('[CustomPatrolController] 设置事件回调');
        this.events = events;
      }

      // 初始化状态
      this.state = PatrolState.RUNNING;
      this.direction = PatrolDirection.FORWARD;
      this.currentIndex = 0;
      this.progress = 0;

      // 更新全局状态
      globalThreeStore.setPatrolActive(true);

      // 重置所有缓存
      this.lastCameraPosition.set(0, 0, 0);
      this.lastTargetPosition.set(0, 0, 0);
      this.velocityVector.set(0, 0, 0);
      this.positionHistory = [];
      this.lastDirection.set(0, 0, 0);
      this.directionHistory = [];

      // 设置初始位置和朝向
      console.log('[CustomPatrolController] 设置初始位置和朝向');
      this.currentPosition.copy(this.interpolatedPath[0]);
      this.currentPosition.y = this.config.cameraHeight;

      // 计算初始目标点
      this._updateLookAtTarget();
      this.currentLookAt.copy(this.targetLookAt);

      // 更新相机位置和朝向
      this.camera.position.copy(this.currentPosition);
      this.camera.lookAt(this.currentLookAt);

      // 设置超广角FOV
      if (this.config.useUltraWideFov) {
        console.log('[CustomPatrolController] 设置超广角FOV，原始FOV:', this.camera.fov, '新FOV:', this.config.cameraFov);
        this.originalFov = this.camera.fov; // 保存原始FOV
        this.camera.fov = this.config.cameraFov; // 设置超广角FOV
        this.camera.updateProjectionMatrix(); // 更新投影矩阵
      }

      // 检查当前透视状态，如果透视功能已启用，确保在巡检过程中保持透视效果
      if (globalThreeStore.transparencyMode) {
        console.log('[CustomPatrolController] 检测到透视模式已启用，确保在巡检过程中保持透视效果');
        try {
          // 重新应用透视效果，确保所有对象（包括地板）保持透明状态
          const transparencyManager = HighPerformanceTransparencyManager.getInstance();
          transparencyManager.toggleTransparency(true);
        } catch (error) {
          console.error('[CustomPatrolController] 重新应用透视效果失败:', error);
        }
      }

      // 禁用控制器
      console.log('[CustomPatrolController] 禁用控制器');
      this.controlManager.disableControl();

      // 开始动画循环
      console.log('[CustomPatrolController] 开始动画循环');
      this._startAnimationLoop();

      // 完全隐藏路径可视化
      this._hidePathVisualization();

      // 同时隐藏PathDrawingTool中的路径可视化
      const drawingTool = PathDrawingTool.getInstance();
      drawingTool.hidePathVisualization();

      // 触发开始事件
      if (this.events.onStart) {
        console.log('[CustomPatrolController] 触发onStart事件');
        this.events.onStart();
      }

      console.log('[CustomPatrolController] 巡检已成功启动');
    } catch (error) {
      console.error('[CustomPatrolController] 启动巡检时发生错误:', error);

      // 确保在发生错误时恢复控制器
      try {
        this.controlManager.enableControl();
      } catch (e) {
        console.error('[CustomPatrolController] 恢复控制器时发生错误:', e);
      }
    }
  }

  /**
   * 暂停巡检
   */
  public pause(): void {
    if (this.state !== PatrolState.RUNNING) return;

    // 更新状态
    this.state = PatrolState.PAUSED;

    // 停止动画循环
    this._stopAnimationLoop();

    // 触发暂停事件
    if (this.events.onPause) {
      this.events.onPause();
    }

    // 注意：暂停时保持路径的半透明状态，不做改变
    // Console logs removed for clean interface during patrol
  }

  /**
   * 恢复巡检
   */
  public resume(): void {
    if (this.state !== PatrolState.PAUSED) return;

    // 更新状态
    this.state = PatrolState.RUNNING;

    // 重新开始动画循环
    this._startAnimationLoop();

    // 触发恢复事件
    if (this.events.onResume) {
      this.events.onResume();
    }

    // 注意：恢复巡检时保持路径的半透明状态，不做改变
    // Console logs removed for clean interface during patrol
  }

  /**
   * 停止巡检
   */
  public stop(): void {
    if (this.state === PatrolState.IDLE) return;

    // 更新状态
    this.state = PatrolState.IDLE;

    // 停止动画循环
    this._stopAnimationLoop();

    // 如果路径处于隐藏状态，恢复显示
    if (this.isPathHidden) {
      this._showPathVisualization();
    } else if (this.isPathTransparent) {
      // 如果路径处于半透明状态，恢复正常透明度
      this._resetPathTransparency();
    }

    // 同时显示PathDrawingTool中的路径可视化
    const drawingTool = PathDrawingTool.getInstance();
    drawingTool.showPathVisualization();

    // 移除路径可视化
    this._removePathVisualization();

    // 恢复原始FOV
    if (this.config.useUltraWideFov) {
      console.log('[CustomPatrolController] 恢复原始FOV:', this.originalFov);
      this.camera.fov = this.originalFov;
      this.camera.updateProjectionMatrix();
    }

    // 启用控制器
    this.controlManager.enableControl();

    // 更新全局状态
    const globalThreeStore = useGlobalThreeStore();
    globalThreeStore.setPatrolActive(false);

    // 检查当前透视状态，如果透视功能已启用，确保在巡检结束后保持透视效果
    if (globalThreeStore.transparencyMode) {
      console.log('[CustomPatrolController] 检测到透视模式已启用，确保在巡检结束后保持透视效果');
      try {
        // 重新应用透视效果，确保所有对象（包括地板）保持透明状态
        const transparencyManager = HighPerformanceTransparencyManager.getInstance();
        transparencyManager.toggleTransparency(true);
      } catch (error) {
        console.error('[CustomPatrolController] 重新应用透视效果失败:', error);
      }
    }

    // 触发停止事件
    if (this.events.onStop) {
      this.events.onStop();
    }

    // Console logs removed for clean interface during patrol
  }

  // 动画和平滑处理相关属性
  private lastTime: number = 0;

  // 速度平滑处理相关属性
  private velocityVector: THREE.Vector3 = new THREE.Vector3();
  private lastTargetPosition: THREE.Vector3 = new THREE.Vector3();
  private velocityDamping: number = 0.92; // 速度阻尼系数
  private positionHistory: THREE.Vector3[] = []; // 位置历史记录，用于平滑处理
  private historyMaxLength: number = 5; // 历史记录最大长度

  /**
   * 开始动画循环
   * 简化版本：专注于平滑性和连贯性
   */
  private _startAnimationLoop(): void {
    // 停止现有动画循环
    this._stopAnimationLoop();

    // 重置时间
    this.lastTime = performance.now();

    // 创建新的动画循环
    const animate = (time: number) => {
      if (this.state !== PatrolState.RUNNING) return;

      // 计算时间增量（秒）
      const deltaTime = Math.min((time - this.lastTime) / 1000, 0.1); // 限制最大增量为0.1秒
      this.lastTime = time;

      // 更新巡检
      this._update(deltaTime);

      // 继续动画循环
      this.animationId = requestAnimationFrame(animate);
    };

    // 开始动画循环
    this.animationId = requestAnimationFrame(animate);
  }

  /**
   * 停止动画循环
   */
  private _stopAnimationLoop(): void {
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  // 用于平滑相机移动的缓存
  private lastCameraPosition: THREE.Vector3 = new THREE.Vector3();
  private targetCameraPosition: THREE.Vector3 = new THREE.Vector3();

  /**
   * 更新巡检
   * @param deltaTime 时间增量（秒）
   */
  private _update(deltaTime: number): void {
    if (this.state !== PatrolState.RUNNING || this.interpolatedPath.length < 2) return;

    // 计算当前段
    const currentSegment = this._getCurrentSegment();
    if (!currentSegment) return;

    const { start, end, nextIndex } = currentSegment;

    // 计算移动距离 - 使用较低的固定速度以确保平滑
    // 使用固定的deltaTime值来避免帧率波动导致的速度不一致
    const fixedDeltaTime = Math.min(deltaTime, 1 / 30); // 限制最小帧率为30fps
    const moveDistance = this.config.speed * fixedDeltaTime;
    const segmentLength = start.distanceTo(end);

    // 计算当前段内的进度增量
    const progressDelta = moveDistance / segmentLength;

    // 更新进度
    this.progress += progressDelta;

    // 如果到达段终点
    if (this.progress >= 1.0) {
      // 移动到下一段
      this.currentIndex = nextIndex;
      this.progress = 0;

      // 检查是否需要改变方向
      if (this._shouldChangeDirection()) {
        this._changeDirection();
      }
    }

    // 计算目标位置
    const newTargetPosition = new THREE.Vector3();
    newTargetPosition.lerpVectors(start, end, this.progress);

    // 设置相机高度
    let cameraHeight = this.config.cameraHeight;

    // 如果启用了自适应相机高度
    if (this.config.adaptiveCameraHeight) {
      // 执行射线检测，确定地面高度
      const raycaster = new THREE.Raycaster();
      raycaster.set(new THREE.Vector3(newTargetPosition.x, newTargetPosition.y + 10, newTargetPosition.z), new THREE.Vector3(0, -1, 0));

      // 获取场景中的地面对象
      const floorObjects = this._getFloorObjects();

      // 执行射线检测
      const intersects = raycaster.intersectObjects(floorObjects, true);

      if (intersects.length > 0) {
        // 找到地面，设置相机高度为地面高度 + 配置的相机高度
        const groundHeight = intersects[0].point.y;
        cameraHeight = groundHeight + this.config.cameraHeight;
      }
    }

    newTargetPosition.y = cameraHeight;

    // 检测是否在转弯处
    let isInTurn = false;
    let turnAngle = 0;
    if (this.currentIndex < this.interpolatedPath.length - 2) {
      // 获取当前方向和下一段方向
      const currentDir = new THREE.Vector3().subVectors(end, start).normalize();
      const nextStart = this.interpolatedPath[this.currentIndex + 1];
      const nextEnd = this.interpolatedPath[this.currentIndex + 2];
      const nextDir = new THREE.Vector3().subVectors(nextEnd, nextStart).normalize();

      // 计算角度
      turnAngle = currentDir.angleTo(nextDir);
      isInTurn = turnAngle > 0.2; // 约11.5度
    }

    // 计算速度向量 - 当前目标位置与上一个目标位置的差值
    if (this.lastTargetPosition.lengthSq() > 0) {
      // 计算位置变化
      this.velocityVector.subVectors(newTargetPosition, this.lastTargetPosition);

      // 应用阻尼 - 在转弯处使用更大的阻尼
      const dampingFactor = isInTurn ? this.velocityDamping * 0.8 : this.velocityDamping;
      this.velocityVector.multiplyScalar(dampingFactor);
    }

    // 更新目标位置
    this.targetCameraPosition.copy(newTargetPosition);

    // 保存当前目标位置用于下一帧
    this.lastTargetPosition.copy(newTargetPosition);

    // 添加位置到历史记录
    this.positionHistory.push(this.targetCameraPosition.clone());

    // 限制历史记录长度
    if (this.positionHistory.length > this.historyMaxLength) {
      this.positionHistory.shift();
    }

    // 使用位置历史记录计算平滑位置
    let smoothedPosition = new THREE.Vector3();

    if (this.positionHistory.length > 0) {
      // 使用加权平均计算平滑位置
      let totalWeight = 0;

      for (let i = 0; i < this.positionHistory.length; i++) {
        // 越新的位置权重越大
        const weight = (i + 1) / this.positionHistory.length;
        totalWeight += weight;

        // 累加加权位置
        smoothedPosition.add(this.positionHistory[i].clone().multiplyScalar(weight));
      }

      // 归一化
      if (totalWeight > 0) {
        smoothedPosition.divideScalar(totalWeight);
      }
    } else {
      smoothedPosition.copy(this.targetCameraPosition);
    }

    // 在转弯处使用更平滑的插值
    const baseLerpFactor = isInTurn ? 0.08 : 0.15;

    // 根据转弯角度调整插值因子 - 角度越大，插值因子越小
    const angleFactor = isInTurn ? Math.max(0.5, 1.0 - turnAngle / Math.PI) : 1.0;
    const positionLerpFactor = baseLerpFactor * angleFactor;

    // 根据deltaTime调整插值因子，确保在不同帧率下保持一致的平滑度
    const adjustedPositionLerpFactor = Math.min(0.3, positionLerpFactor * (fixedDeltaTime * 60)); // 限制最大值为0.3

    // 如果是第一次更新，直接设置位置
    if (this.lastCameraPosition.lengthSq() === 0) {
      this.lastCameraPosition.copy(smoothedPosition);
      this.currentPosition.copy(smoothedPosition);
    } else {
      // 平滑插值更新位置
      this.currentPosition.lerp(smoothedPosition, adjustedPositionLerpFactor);
    }

    // 保存当前位置用于下一帧
    this.lastCameraPosition.copy(this.currentPosition);

    // 更新目标点
    this._updateLookAtTarget();

    // 平滑更新相机朝向
    // 在转弯处使用更小的插值因子
    const lookLerpFactor = isInTurn ? 0.05 : 0.1;
    const adjustedLookLerpFactor = Math.min(0.2, lookLerpFactor * (fixedDeltaTime * 60)); // 限制最大值为0.2
    this.currentLookAt.lerp(this.targetLookAt, adjustedLookLerpFactor);

    // 更新相机
    this.camera.position.copy(this.currentPosition);
    this.camera.lookAt(this.currentLookAt);

    // 触发位置更新事件
    if (this.events.onPositionUpdate) {
      // 计算总进度（0-1）
      const totalProgress = this._calculateTotalProgress();
      this.events.onPositionUpdate(this.currentPosition.clone(), totalProgress);
    }

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 获取当前路径段
   */
  private _getCurrentSegment(): { start: THREE.Vector3; end: THREE.Vector3; nextIndex: number } | null {
    if (this.interpolatedPath.length < 2) return null;

    const pathLength = this.interpolatedPath.length;

    // 根据当前方向确定起点、终点和下一个索引
    if (this.direction === PatrolDirection.FORWARD) {
      if (this.currentIndex >= pathLength - 1) return null;

      return {
        start: this.interpolatedPath[this.currentIndex],
        end: this.interpolatedPath[this.currentIndex + 1],
        nextIndex: this.currentIndex + 1,
      };
    } else {
      if (this.currentIndex <= 0) return null;

      // 在BACKWARD方向时，我们仍然是从当前点向前移动到下一个点
      // 但是索引是递减的，所以我们从当前点移动到前一个点
      return {
        start: this.interpolatedPath[this.currentIndex],
        end: this.interpolatedPath[this.currentIndex - 1],
        nextIndex: this.currentIndex - 1,
      };
    }
  }

  // 用于平滑视角变化的缓存
  private lastDirection: THREE.Vector3 = new THREE.Vector3();
  private directionHistory: THREE.Vector3[] = []; // 方向历史记录
  private directionHistoryMaxLength: number = 10; // 方向历史记录最大长度

  /**
   * 更新目标点
   * 增强版本：使用方向历史记录和预测，确保平滑视角变化
   */
  private _updateLookAtTarget(): void {
    // 获取当前段
    const currentSegment = this._getCurrentSegment();
    if (!currentSegment) return;

    const { start, end } = currentSegment;

    // 计算前进方向
    const currentDirection = new THREE.Vector3().subVectors(end, start).normalize();

    // 检查是否在转弯处
    let isInTurn = false;
    let turnAngle = 0;
    let nextDirection = null;

    // 如果不是最后一段，检查是否在转弯处
    if (this.currentIndex < this.interpolatedPath.length - 2) {
      const nextStart = this.interpolatedPath[this.currentIndex + 1];
      const nextEnd = this.interpolatedPath[this.currentIndex + 2];
      nextDirection = new THREE.Vector3().subVectors(nextEnd, nextStart).normalize();

      // 计算转弯角度
      turnAngle = currentDirection.angleTo(nextDirection);
      isInTurn = turnAngle > 0.1; // 约5.7度
    }

    // 计算平滑方向
    let smoothDirection;

    // 如果是第一次更新，直接使用当前方向
    if (this.lastDirection.lengthSq() === 0) {
      smoothDirection = currentDirection.clone();
      this.lastDirection.copy(currentDirection);
    } else {
      // 添加当前方向到历史记录
      this.directionHistory.push(currentDirection.clone());

      // 限制历史记录长度
      if (this.directionHistory.length > this.directionHistoryMaxLength) {
        this.directionHistory.shift();
      }

      // 使用方向历史记录计算平滑方向
      smoothDirection = new THREE.Vector3();

      if (this.directionHistory.length > 0) {
        // 使用加权平均计算平滑方向
        let totalWeight = 0;

        for (let i = 0; i < this.directionHistory.length; i++) {
          // 越新的方向权重越大
          const weight = (i + 1) / this.directionHistory.length;
          totalWeight += weight;

          // 累加加权方向
          smoothDirection.add(this.directionHistory[i].clone().multiplyScalar(weight));
        }

        // 归一化
        if (totalWeight > 0) {
          smoothDirection.divideScalar(totalWeight);
          smoothDirection.normalize();
        }
      } else {
        smoothDirection.copy(currentDirection);
      }

      // 在转弯处，预测并平滑过渡到下一段方向
      if (isInTurn && nextDirection) {
        // 根据转弯角度和当前进度计算插值因子
        // 角度越大，插值越早开始
        const angleFactor = Math.min(1.0, turnAngle / (Math.PI / 4)); // 最大考虑45度
        const progressFactor = this.progress * angleFactor;

        // 在当前平滑方向和下一个方向之间插值
        smoothDirection.lerp(nextDirection, progressFactor * 0.3); // 使用较小的插值因子
        smoothDirection.normalize();
      }

      // 更新最后的方向
      this.lastDirection.copy(smoothDirection);
    }

    // 计算前视距离 - 在转弯处减小前视距离
    let lookAheadDistance = this.config.lookAheadDistance;
    if (isInTurn) {
      // 根据转弯角度调整前视距离 - 角度越大，前视距离越小
      const angleFactor = Math.max(0.6, 1.0 - turnAngle / Math.PI);
      lookAheadDistance *= angleFactor;
    }

    // 计算目标点（在前方lookAheadDistance距离处）
    this.targetLookAt.copy(this.currentPosition).addScaledVector(smoothDirection, lookAheadDistance);

    // 保持目标点与相机在同一高度，避免上下视角变化
    this.targetLookAt.y = this.currentPosition.y;
  }

  /**
   * 检查是否应该改变方向或循环
   */
  private _shouldChangeDirection(): boolean {
    // 检查是否到达路径端点
    if (this.direction === PatrolDirection.FORWARD) {
      return this.currentIndex >= this.interpolatedPath.length - 1;
    } else {
      return this.currentIndex <= 0;
    }
  }

  /**
   * 改变巡检方向或执行循环
   */
  private _changeDirection(): void {
    // 根据终点处理模式选择不同的处理方式
    if (this.config.endpointMode === PatrolEndpointMode.REVERSE) {
      // 反转方向模式 - 原有的180度旋转实现
      this._handleReverseMode();
    } else {
      // 循环模式 - 从终点平滑过渡到起点
      this._handleLoopMode();
    }
  }

  /**
   * 处理反转方向模式（原有的180度旋转实现）
   */
  private _handleReverseMode(): void {
    // 反转方向
    this.direction = this.direction === PatrolDirection.FORWARD ? PatrolDirection.BACKWARD : PatrolDirection.FORWARD;

    // 执行180度旋转
    this._rotate180Degrees();

    // 触发方向改变事件
    if (this.events.onDirectionChange) {
      this.events.onDirectionChange(this.direction);
    }

    // Console logs removed for clean interface during patrol
  }

  /**
   * 处理循环模式（从终点平滑过渡到起点）
   */
  private _handleLoopMode(): void {
    // 暂停更新
    const wasRunning = this.state === PatrolState.RUNNING;
    if (wasRunning) {
      this.pause();
    }

    // 确定起点和终点
    let startPoint: THREE.Vector3;
    let startLookAt: THREE.Vector3;
    let endPoint: THREE.Vector3;
    let endLookAt: THREE.Vector3;

    if (this.direction === PatrolDirection.FORWARD) {
      // 从路径终点过渡到起点
      startPoint = this.interpolatedPath[this.interpolatedPath.length - 1].clone();
      endPoint = this.interpolatedPath[0].clone();

      // 计算起点和终点的朝向
      const startDir = new THREE.Vector3()
        .subVectors(this.interpolatedPath[this.interpolatedPath.length - 1], this.interpolatedPath[this.interpolatedPath.length - 2])
        .normalize();

      const endDir = new THREE.Vector3().subVectors(this.interpolatedPath[1], this.interpolatedPath[0]).normalize();

      // 设置起点和终点的朝向目标
      startLookAt = new THREE.Vector3().copy(startPoint).addScaledVector(startDir, this.config.lookAheadDistance);
      endLookAt = new THREE.Vector3().copy(endPoint).addScaledVector(endDir, this.config.lookAheadDistance);
    } else {
      // 从路径起点过渡到终点
      startPoint = this.interpolatedPath[0].clone();
      endPoint = this.interpolatedPath[this.interpolatedPath.length - 1].clone();

      // 计算起点和终点的朝向
      const startDir = new THREE.Vector3().subVectors(this.interpolatedPath[0], this.interpolatedPath[1]).normalize();

      const endDir = new THREE.Vector3()
        .subVectors(this.interpolatedPath[this.interpolatedPath.length - 2], this.interpolatedPath[this.interpolatedPath.length - 1])
        .normalize();

      // 设置起点和终点的朝向目标
      startLookAt = new THREE.Vector3().copy(startPoint).addScaledVector(startDir, this.config.lookAheadDistance);
      endLookAt = new THREE.Vector3().copy(endPoint).addScaledVector(endDir, this.config.lookAheadDistance);
    }

    // 设置高度
    startPoint.y = this.config.cameraHeight;
    endPoint.y = this.config.cameraHeight;
    startLookAt.y = this.config.cameraHeight;
    endLookAt.y = this.config.cameraHeight;

    // 不需要保存当前位置和朝向，因为我们直接使用起点和终点

    // 创建动画对象
    const animationObj = { progress: 0 };
    const duration = this.config.loopTransitionDuration;

    // 使用GSAP执行平滑过渡
    gsap.to(animationObj, {
      progress: 1,
      duration: duration,
      ease: 'power2.inOut',
      onUpdate: () => {
        // 在起点和终点之间插值计算位置
        this.currentPosition.lerpVectors(startPoint, endPoint, animationObj.progress);

        // 在起点朝向和终点朝向之间插值
        this.currentLookAt.lerpVectors(startLookAt, endLookAt, animationObj.progress);

        // 更新相机位置和朝向
        this.camera.position.copy(this.currentPosition);
        this.camera.lookAt(this.currentLookAt);

        // 请求渲染
        SceneManager.getInstance().needsRender = true;
      },
      onComplete: () => {
        // 重置索引和进度
        if (this.direction === PatrolDirection.FORWARD) {
          // 重置到路径起点
          this.currentIndex = 0;
        } else {
          // 重置到路径终点
          this.currentIndex = this.interpolatedPath.length - 1;
        }
        this.progress = 0;

        // 更新目标点
        this._updateLookAtTarget();

        // 如果之前在运行，恢复运行
        if (wasRunning) {
          this.resume();
        }

        // Console logs removed for clean interface during patrol
      },
    });
  }

  /**
   * 执行180度旋转
   * 原地调头，只旋转视角，不改变位置
   */
  private _rotate180Degrees(): void {
    // 暂停更新
    const wasRunning = this.state === PatrolState.RUNNING;
    if (wasRunning) {
      this.pause();
    }

    // 获取当前相机朝向
    const currentDirection = new THREE.Vector3();
    this.camera.getWorldDirection(currentDirection);

    // 计算反向
    const oppositeDirection = currentDirection.multiplyScalar(-1);

    // 计算新的目标点
    const newTarget = new THREE.Vector3().copy(this.camera.position).add(oppositeDirection);

    // 使用GSAP执行平滑旋转
    const duration = 1.0; // 旋转持续时间（秒）

    // 保存当前目标点
    const originalLookAt = this.currentLookAt.clone();

    // 创建动画对象
    const animationObj = { progress: 0 };

    // 保存原始位置，确保在旋转过程中不改变位置
    const originalPosition = this.currentPosition.clone();

    gsap.to(animationObj, {
      progress: 1,
      duration: duration,
      ease: 'power2.inOut',
      onUpdate: () => {
        // 旋转视角 - 在原始目标点和新目标点之间插值
        this.currentLookAt.lerpVectors(originalLookAt, newTarget, animationObj.progress);

        // 确保位置不变
        this.currentPosition.copy(originalPosition);

        // 更新相机位置和朝向
        this.camera.position.copy(this.currentPosition);
        this.camera.lookAt(this.currentLookAt);

        // 请求渲染
        SceneManager.getInstance().needsRender = true;
      },
      onComplete: () => {
        // 更新目标点
        this.targetLookAt.copy(newTarget);

        // 如果之前在运行，恢复运行
        if (wasRunning) {
          this.resume();
        }
      },
    });
  }

  /**
   * 计算总进度（0-1）
   */
  private _calculateTotalProgress(): number {
    if (this.interpolatedPath.length < 2) return 0;

    const pathLength = this.interpolatedPath.length - 1;

    if (this.direction === PatrolDirection.FORWARD) {
      return (this.currentIndex + this.progress) / pathLength;
    } else {
      return 1 - (this.currentIndex - this.progress) / pathLength;
    }
  }

  /**
   * 获取当前状态
   */
  public getState(): PatrolState {
    return this.state;
  }

  /**
   * 获取当前方向
   */
  public getDirection(): PatrolDirection {
    return this.direction;
  }

  /**
   * 设置配置
   * @param config 配置对象
   */
  public setConfig(config: Partial<PatrolConfig>): void {
    this.config = { ...this.config, ...config };

    // 如果路径可视化配置改变，更新可视化
    if ('showPathLine' in config || 'pathLineColor' in config || 'pathLineWidth' in config) {
      this._createPathVisualization();
    }
  }

  /**
   * 获取配置
   */
  public getConfig(): PatrolConfig {
    return { ...this.config };
  }

  /**
   * 获取场景中的地面对象
   * @returns 地面对象数组
   */
  private _getFloorObjects(): THREE.Object3D[] {
    const floorObjects: THREE.Object3D[] = [];

    // 遍历场景中的所有对象
    this.scene.traverse((object) => {
      // 检查对象名称是否包含"Floor"或"floor"
      if (
        object instanceof THREE.Mesh &&
        (object.name.includes('Floor') ||
          object.name.includes('floor') ||
          (object.parent && (object.parent.name.includes('Floor') || object.parent.name.includes('floor'))))
      ) {
        floorObjects.push(object);
      }
    });

    return floorObjects;
  }

  /**
   * 保存当前巡检路径
   * @param name 路径名称
   * @returns 保存的路径数据
   */
  public savePatrolPath(name: string): { name: string; points: any[]; config: PatrolConfig } {
    // 将Vector3对象转换为普通对象，以便于JSON序列化
    const serializedPoints = this.pathPoints.map((p) => ({
      x: p.x,
      y: p.y,
      z: p.z,
    }));

    return {
      name,
      points: serializedPoints,
      config: { ...this.config },
    };
  }

  /**
   * 加载保存的巡检路径
   * @param pathData 保存的路径数据
   */
  public loadPatrolPath(pathData: { name: string; points: any[]; config: PatrolConfig }): void {
    try {
      // 将普通对象转换回Vector3对象
      this.pathPoints = pathData.points.map((p) => {
        // 处理可能的不同格式
        if (p instanceof THREE.Vector3) {
          return p.clone();
        } else if (typeof p === 'object' && 'x' in p && 'y' in p && 'z' in p) {
          return new THREE.Vector3(p.x, p.y, p.z);
        } else {
          console.error('[CustomPatrolController] 无效的路径点格式:', p);
          return new THREE.Vector3();
        }
      });

      // 设置配置
      this.setConfig(pathData.config);

      // 生成插值路径
      this._generateInterpolatedPath();

      // 创建路径可视化
      this._createPathVisualization();

      // 如果当前正在巡检中，根据当前状态设置路径显示
      if (this.state === PatrolState.RUNNING || this.state === PatrolState.PAUSED) {
        // 如果当前是隐藏状态，则隐藏路径
        if (this.isPathHidden) {
          this._hidePathVisualization();
        } else if (this.isPathTransparent) {
          // 如果当前是半透明状态，则设置为半透明
          this._setPathTransparent();
        }
      }

      console.log(`[CustomPatrolController] 已加载路径 "${pathData.name}"，共 ${this.pathPoints.length} 个点`);
    } catch (error) {
      console.error('[CustomPatrolController] 加载路径失败:', error);
      throw new Error('加载路径失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }
}
