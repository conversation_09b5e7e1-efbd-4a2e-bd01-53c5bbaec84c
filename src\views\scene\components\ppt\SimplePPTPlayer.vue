<template>
  <div class="simple-ppt-player h-full flex flex-col bg-[#0a0f1c]">
    <!-- PPT标题栏 -->
    <div class="flex items-center justify-between p-4 bg-[#1a2332] border-b border-[#2a3441]">
      <div class="flex items-center">
        <FileTextOutlined class="text-[#3B8EE6] text-lg mr-2" />
        <span class="text-white text-sm font-medium">DCIM平台介绍演示</span>
      </div>
      <div class="flex items-center space-x-2">
        <span class="text-gray-400 text-xs">{{ currentSlide + 1 }} / {{ totalSlides }}</span>
        <a-button size="small" type="text" @click="$emit('close')" class="text-gray-400 hover:text-white">
          <CloseOutlined />
        </a-button>
      </div>
    </div>

    <!-- PPT内容区域 -->
    <div class="flex-1 relative overflow-hidden">
      <!-- PPT幻灯片显示区域 -->
      <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 relative">
        <!-- DCIM PPT内容 -->
        <div class="w-full h-full flex items-center justify-center text-center p-8 overflow-y-auto">
          <div class="max-w-6xl w-full">
            <!-- 标题区域 -->
            <div class="mb-8">
              <h1 class="text-5xl font-bold text-gray-800 mb-4">{{ currentSlideContent.title }}</h1>
              <h2 v-if="currentSlideContent.subtitle" class="text-2xl text-blue-600 font-medium">{{ currentSlideContent.subtitle }}</h2>
            </div>

            <!-- 封面页 -->
            <div v-if="currentSlideContent.type === 'cover'" class="space-y-8">
              <div class="text-xl text-gray-700 space-y-4">
                <p v-for="(content, index) in currentSlideContent.content" :key="index" class="leading-relaxed">
                  {{ content }}
                </p>
              </div>
              <div v-if="currentSlideContent.features" class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                <div
                  v-for="feature in currentSlideContent.features"
                  :key="feature"
                  class="bg-white rounded-lg p-4 shadow-md border-l-4 border-blue-500"
                >
                  <span class="text-lg font-semibold text-gray-800">{{ feature }}</span>
                </div>
              </div>
            </div>

            <!-- 架构页 -->
            <div v-else-if="currentSlideContent.type === 'architecture'" class="space-y-8">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>
              <div v-if="currentSlideContent.architecture" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div v-for="(desc, layer) in currentSlideContent.architecture" :key="layer" class="bg-white rounded-lg p-4 shadow-md text-center">
                  <div class="text-lg font-bold text-blue-600 mb-2 capitalize">{{ layer }}层</div>
                  <div class="text-sm text-gray-600">{{ desc }}</div>
                </div>
              </div>
            </div>

            <!-- 功能模块页 -->
            <div v-else-if="currentSlideContent.type === 'modules'" class="space-y-8">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>
              <div v-if="currentSlideContent.modules" class="grid grid-cols-2 md:grid-cols-3 gap-6">
                <div
                  v-for="module in currentSlideContent.modules"
                  :key="module.name"
                  class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow"
                >
                  <div class="text-3xl mb-3">{{ module.icon }}</div>
                  <div class="text-xl font-bold text-gray-800 mb-2">{{ module.name }}</div>
                  <div class="text-sm text-gray-600">{{ module.desc }}</div>
                </div>
              </div>
            </div>

            <!-- 可视化页 -->
            <div v-else-if="currentSlideContent.type === 'visualization'" class="space-y-8">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div v-if="currentSlideContent.features" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">核心特性</h3>
                  <ul class="space-y-2">
                    <li v-for="feature in currentSlideContent.features" :key="feature" class="flex items-center text-gray-700">
                      <span class="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                      {{ feature }}
                    </li>
                  </ul>
                </div>
                <div v-if="currentSlideContent.capabilities" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">技术能力</h3>
                  <div class="space-y-3">
                    <div v-for="(desc, cap) in currentSlideContent.capabilities" :key="cap" class="border-l-4 border-green-500 pl-4">
                      <div class="font-semibold text-gray-800 capitalize">{{ cap }}</div>
                      <div class="text-sm text-gray-600">{{ desc }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 消防安全页 -->
            <div v-else-if="currentSlideContent.type === 'fire-safety'" class="space-y-8">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div v-if="currentSlideContent.systems" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">系统状态</h3>
                  <div class="space-y-3">
                    <div
                      v-for="system in currentSlideContent.systems"
                      :key="system.name"
                      class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                    >
                      <div>
                        <div class="font-semibold text-gray-800">{{ system.name }}</div>
                        <div class="text-sm text-gray-600">{{ system.desc }}</div>
                      </div>
                      <div class="text-right">
                        <div class="text-green-600 font-semibold">{{ system.status }}</div>
                        <div class="text-sm text-gray-500">{{ system.devices }}台设备</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="currentSlideContent.features" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">核心功能</h3>
                  <ul class="space-y-2">
                    <li v-for="feature in currentSlideContent.features" :key="feature" class="flex items-center text-gray-700">
                      <span class="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                      {{ feature }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 能耗管理页 -->
            <div v-else-if="currentSlideContent.type === 'energy'" class="space-y-8">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div v-if="currentSlideContent.metrics" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">能耗指标</h3>
                  <div class="grid grid-cols-2 gap-4">
                    <div v-for="(value, key) in currentSlideContent.metrics" :key="key" class="text-center p-3 bg-blue-50 rounded-lg">
                      <div class="text-2xl font-bold text-blue-600">{{ value }}</div>
                      <div class="text-sm text-gray-600 capitalize">{{ key.replace(/([A-Z])/g, ' $1') }}</div>
                    </div>
                  </div>
                </div>
                <div v-if="currentSlideContent.features" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">管理功能</h3>
                  <ul class="space-y-2">
                    <li v-for="feature in currentSlideContent.features" :key="feature" class="flex items-center text-gray-700">
                      <span class="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
                      {{ feature }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 数据管理页 -->
            <div v-else-if="currentSlideContent.type === 'data'" class="space-y-8">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div v-if="currentSlideContent.dataTypes" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">数据类型</h3>
                  <div class="space-y-3">
                    <div
                      v-for="dataType in currentSlideContent.dataTypes"
                      :key="dataType.type"
                      class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                    >
                      <div>
                        <div class="font-semibold text-gray-800">{{ dataType.type }}</div>
                        <div class="text-sm text-gray-600">{{ dataType.desc }}</div>
                      </div>
                      <div class="text-2xl font-bold text-blue-600">{{ dataType.count }}</div>
                    </div>
                  </div>
                </div>
                <div v-if="currentSlideContent.analytics" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">分析能力</h3>
                  <ul class="space-y-2">
                    <li v-for="analytic in currentSlideContent.analytics" :key="analytic" class="flex items-center text-gray-700">
                      <span class="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                      {{ analytic }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 告警系统页 -->
            <div v-else-if="currentSlideContent.type === 'alarm'" class="space-y-8">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div v-if="currentSlideContent.alarmLevels" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">告警级别</h3>
                  <div class="space-y-3">
                    <div
                      v-for="alarm in currentSlideContent.alarmLevels"
                      :key="alarm.level"
                      class="flex justify-between items-center p-3 rounded-lg"
                      :style="{ backgroundColor: alarm.color + '20', borderLeft: '4px solid ' + alarm.color }"
                    >
                      <div>
                        <div class="font-semibold text-gray-800">{{ alarm.level }}</div>
                        <div class="text-sm text-gray-600">{{ alarm.desc }}</div>
                      </div>
                      <div class="text-2xl font-bold" :style="{ color: alarm.color }">{{ alarm.count }}</div>
                    </div>
                  </div>
                </div>
                <div v-if="currentSlideContent.features" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">智能功能</h3>
                  <ul class="space-y-2">
                    <li v-for="feature in currentSlideContent.features" :key="feature" class="flex items-center text-gray-700">
                      <span class="w-2 h-2 bg-orange-500 rounded-full mr-3"></span>
                      {{ feature }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 运维管理页 -->
            <div v-else-if="currentSlideContent.type === 'operation'" class="space-y-8">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div v-if="currentSlideContent.workflows" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">工作流程</h3>
                  <div class="space-y-3">
                    <div v-for="workflow in currentSlideContent.workflows" :key="workflow.name" class="p-3 bg-gray-50 rounded-lg">
                      <div class="flex justify-between items-center mb-2">
                        <div class="font-semibold text-gray-800">{{ workflow.name }}</div>
                        <div class="text-sm text-gray-600">{{ workflow.status }}</div>
                      </div>
                      <div class="text-sm text-gray-600 mb-2">{{ workflow.desc }}</div>
                      <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" :style="{ width: workflow.progress + '%' }"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="currentSlideContent.automation" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">自动化功能</h3>
                  <ul class="space-y-2">
                    <li v-for="auto in currentSlideContent.automation" :key="auto" class="flex items-center text-gray-700">
                      <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      {{ auto }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 资产管理页 -->
            <div v-else-if="currentSlideContent.type === 'asset'" class="space-y-8">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div v-if="currentSlideContent.lifecycle" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">生命周期</h3>
                  <div class="space-y-3">
                    <div v-for="stage in currentSlideContent.lifecycle" :key="stage.stage" class="flex items-center p-3 bg-gray-50 rounded-lg">
                      <div class="text-2xl mr-4">{{ stage.icon }}</div>
                      <div>
                        <div class="font-semibold text-gray-800">{{ stage.stage }}</div>
                        <div class="text-sm text-gray-600">{{ stage.desc }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="currentSlideContent.statistics" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">资产统计</h3>
                  <div class="grid grid-cols-2 gap-4">
                    <div v-for="(value, key) in currentSlideContent.statistics" :key="key" class="text-center p-3 bg-green-50 rounded-lg">
                      <div class="text-2xl font-bold text-green-600">{{ value }}</div>
                      <div class="text-sm text-gray-600 capitalize">{{ key.replace(/([A-Z])/g, ' $1') }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 价值优势页 -->
            <div v-else-if="currentSlideContent.type === 'value'" class="space-y-8">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div v-if="currentSlideContent.values" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">核心价值</h3>
                  <div class="grid grid-cols-2 gap-4">
                    <div
                      v-for="value in currentSlideContent.values"
                      :key="value.title"
                      class="text-center p-4 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg"
                    >
                      <div class="text-3xl font-bold text-blue-600 mb-2">{{ value.value }}</div>
                      <div class="font-semibold text-gray-800 mb-1">{{ value.title }}</div>
                      <div class="text-sm text-gray-600">{{ value.desc }}</div>
                    </div>
                  </div>
                </div>
                <div v-if="currentSlideContent.advantages" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">平台优势</h3>
                  <ul class="space-y-2">
                    <li v-for="advantage in currentSlideContent.advantages" :key="advantage" class="flex items-center text-gray-700">
                      <span class="w-2 h-2 bg-indigo-500 rounded-full mr-3"></span>
                      {{ advantage }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 结束页 -->
            <div v-else-if="currentSlideContent.type === 'closing'" class="space-y-8">
              <div class="text-xl text-gray-700 space-y-4">
                <p v-for="(content, index) in currentSlideContent.content" :key="index" class="leading-relaxed">
                  {{ content }}
                </p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div v-if="currentSlideContent.contact" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">联系我们</h3>
                  <div class="space-y-3">
                    <div v-for="(value, key) in currentSlideContent.contact" :key="key" class="flex justify-between items-center">
                      <span class="text-gray-600 capitalize">{{ key }}:</span>
                      <span class="text-gray-800 font-medium">{{ value }}</span>
                    </div>
                  </div>
                </div>
                <div v-if="currentSlideContent.closing" class="bg-white rounded-lg p-6 shadow-md">
                  <h3 class="text-xl font-bold text-gray-800 mb-4">我们的优势</h3>
                  <ul class="space-y-2">
                    <li v-for="item in currentSlideContent.closing" :key="item" class="flex items-center text-gray-700">
                      <span class="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                      {{ item }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 默认内容页 -->
            <div v-else class="space-y-6">
              <div class="text-lg text-gray-700 space-y-3">
                <p v-for="(content, index) in currentSlideContent.content" :key="index">{{ content }}</p>
              </div>

              <!-- 根据不同类型显示特定内容 -->
              <div v-if="currentSlideContent.features" class="bg-white rounded-lg p-6 shadow-md">
                <h3 class="text-xl font-bold text-gray-800 mb-4">主要特性</h3>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <div v-for="feature in currentSlideContent.features" :key="feature" class="bg-blue-50 rounded-lg p-3 text-center">
                    <span class="text-gray-800">{{ feature }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 导航覆盖层 -->
        <div class="absolute inset-0 pointer-events-none">
          <!-- 左侧导航区域 -->
          <div
            class="absolute left-0 top-0 w-16 h-full pointer-events-auto cursor-pointer hover:bg-black/10 transition-colors flex items-center justify-center"
            @click="previousSlide"
            v-if="currentSlide > 0"
          >
            <LeftOutlined class="text-gray-600 text-2xl opacity-70 hover:opacity-100" />
          </div>

          <!-- 右侧导航区域 -->
          <div
            class="absolute right-0 top-0 w-16 h-full pointer-events-auto cursor-pointer hover:bg-black/10 transition-colors flex items-center justify-center"
            @click="nextSlide"
            v-if="currentSlide < totalSlides - 1"
          >
            <RightOutlined class="text-gray-600 text-2xl opacity-70 hover:opacity-100" />
          </div>
        </div>
      </div>
    </div>

    <!-- 控制栏 - 调整底部间距避免被遮挡 -->
    <div class="p-4 pb-16 bg-[#1a2332] border-t border-[#2a3441]">
      <div class="flex items-center justify-between">
        <!-- 播放控制 -->
        <div class="flex items-center space-x-2">
          <CustomButton :icon="isAutoPlaying ? 'pause' : 'play'" @click="toggleAutoPlay" type="primary">
            {{ isAutoPlaying ? '暂停' : '自动播放' }}
          </CustomButton>
          <CustomSelect v-model:value="autoPlayInterval" :options="intervalOptions" :disabled="!isAutoPlaying" style="width: 100px" />
        </div>

        <!-- 页面导航 -->
        <div class="flex items-center space-x-2">
          <CustomButton icon="left" @click="previousSlide" :disabled="currentSlide <= 0"> 上一页 </CustomButton>
          <CustomInputNumber v-model:value="jumpToSlide" :min="1" :max="totalSlides" style="width: 80px" @pressEnter="goToSlide" />
          <CustomButton icon="right" @click="nextSlide" :disabled="currentSlide >= totalSlides - 1"> 下一页 </CustomButton>
        </div>

        <!-- 视角绑定 -->
        <div class="flex items-center space-x-2">
          <CustomButton icon="eye" @click="showViewBindingModal = true"> 视角绑定 </CustomButton>
          <CustomButton type="primary" variant="ghost" @click="bindCurrentView"> 绑定当前视角 </CustomButton>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="mt-3">
        <CustomSlider
          v-model:value="currentSlide"
          :min="0"
          :max="totalSlides - 1"
          :step="1"
          @change="goToSlideBySlider"
          :tooltip-formatter="(value) => `第 ${value + 1} 页`"
        />
      </div>
    </div>

    <!-- 视角绑定模态框 -->
    <ViewBindingModal
      v-model:visible="showViewBindingModal"
      :current-slide="currentSlide"
      :total-slides="totalSlides"
      @bind-view="handleViewBinding"
      @remove-binding="handleRemoveBinding"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onUnmounted, h } from 'vue';
  import {
    CloseOutlined,
    LeftOutlined,
    RightOutlined,
    PlayCircleOutlined,
    PauseOutlined,
    EyeOutlined,
    FileTextOutlined,
  } from '@ant-design/icons-vue';
  import { useGlobalThreeStore } from '../../store/globalThreeStore';
  import type { PPTViewBinding } from '../../store/globalThreeStore';
  import ViewBindingModal from './ViewBindingModal.vue';
  import { CameraController } from '../../lib/CameraController';
  import CustomButton from './CustomButton.vue';
  import CustomSelect from './CustomSelect.vue';
  import CustomInputNumber from './CustomInputNumber.vue';
  import CustomSlider from './CustomSlider.vue';

  // Props
  interface Props {
    pptPath: string;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    close: [];
    slideChange: [slideIndex: number];
  }>();

  // Store
  const globalThreeStore = useGlobalThreeStore();

  // 响应式数据
  const currentSlide = ref(0);
  const totalSlides = ref(0);
  const jumpToSlide = ref(1);
  const isAutoPlaying = ref(false);
  const autoPlayInterval = ref(5000);
  const showViewBindingModal = ref(false);

  // 自动播放定时器
  let autoPlayTimer: number | null = null;

  // 选项数据
  const intervalOptions = ref([
    { value: 3000, label: '3秒' },
    { value: 5000, label: '5秒' },
    { value: 8000, label: '8秒' },
    { value: 10000, label: '10秒' },
  ]);

  // DCIM平台介绍PPT内容 - 基于docs/副本DCIM平台介绍0527.pptx
  const slideContents = ref([
    {
      title: 'DCIM数据中心基础设施管理平台',
      subtitle: 'Data Center Infrastructure Management Platform',
      content: ['智能化数据中心管理解决方案', '集成监控、运维、能耗、安全于一体', '提升数据中心运营效率与可靠性'],
      features: ['实时监控', '智能运维', '节能降耗', '安全保障'],
      type: 'cover',
    },
    {
      title: '平台架构概述',
      subtitle: 'Platform Architecture Overview',
      content: ['分层架构设计，模块化部署', '支持多数据中心统一管理', '开放式接口，易于集成扩展'],
      architecture: {
        sensing: '传感器、监控设备、智能终端',
        network: '有线网络、无线网络、物联网',
        data: '数据采集、存储、处理、分析',
        application: '监控管理、运维管理、能耗管理',
        presentation: 'Web界面、移动端、3D可视化',
      },
      type: 'architecture',
    },
    {
      title: '核心功能模块',
      subtitle: 'Core Function Modules',
      content: ['涵盖数据中心运营全生命周期', '模块化设计，灵活配置', '统一平台，协同管理'],
      modules: [
        { name: '3D可视化', desc: '三维场景展示与交互', icon: '🏢' },
        { name: '设备监控', desc: '实时状态监测与告警', icon: '📊' },
        { name: '能耗管理', desc: '能源消耗分析优化', icon: '⚡' },
        { name: '空间管理', desc: 'U位资源规划配置', icon: '📦' },
        { name: '运维管理', desc: '工单流程自动化', icon: '🔧' },
        { name: '安全管理', desc: '门禁消防一体化', icon: '🛡️' },
      ],
      type: 'modules',
    },
    {
      title: '3D可视化场景',
      subtitle: '3D Visualization Scene',
      content: ['真实还原数据中心物理环境', '设备状态实时映射显示', '支持多视角漫游与交互'],
      features: ['1:1比例三维建模', '设备状态实时同步', '多层级场景切换', '设备详情快速查看', '告警状态可视化', '巡检路径规划'],
      capabilities: {
        modeling: '精确建模，细节丰富',
        interaction: '鼠标交互，操作便捷',
        realtime: '实时数据，动态更新',
        navigation: '自由漫游，多角度观察',
      },
      type: 'visualization',
    },
    {
      title: '智慧安消防系统',
      subtitle: 'Smart Fire Safety System',
      content: ['集成火灾报警与消防联动', '智能烟感温感监测', '应急疏散路径规划'],
      systems: [
        { name: '火灾报警', status: '正常', devices: 128, desc: '烟感、温感、手报等设备' },
        { name: '消防联动', status: '正常', devices: 64, desc: '喷淋、排烟、防火门等' },
        { name: '应急广播', status: '正常', devices: 32, desc: '语音播报、疏散指引' },
        { name: '视频监控', status: '正常', devices: 96, desc: '火点定位、现场监视' },
      ],
      features: ['24小时实时监控', '多级告警机制', '自动联动控制', '应急预案执行'],
      type: 'fire-safety',
    },
    {
      title: '智慧能耗管理',
      subtitle: 'Smart Energy Management',
      content: ['全方位能耗监测分析', 'PUE值实时计算优化', '节能策略智能推荐'],
      metrics: {
        totalPower: '1250 kW',
        itPower: '875 kW',
        coolingPower: '312 kW',
        otherPower: '63 kW',
        pue: '1.43',
        efficiency: '92.5%',
      },
      features: ['分级分区能耗统计', 'PUE趋势分析', '能效对比评估', '节能建议推送', '成本核算分析', '碳排放计算'],
      type: 'energy',
    },
    {
      title: '运营数据管理',
      subtitle: 'Operation Data Management',
      content: ['多维度数据采集整合', '运营指标实时监控', '数据驱动决策支持'],
      dataTypes: [
        { type: '设备数据', count: '2,580', desc: '服务器、网络、存储设备' },
        { type: '环境数据', count: '1,240', desc: '温湿度、气流、洁净度' },
        { type: '能耗数据', count: '856', desc: '电力、制冷、照明消耗' },
        { type: '安全数据', count: '432', desc: '门禁、监控、消防状态' },
      ],
      analytics: ['实时数据大屏', '历史趋势分析', '异常模式识别', '预测性分析', '报表自动生成', 'KPI指标监控'],
      type: 'data',
    },
    {
      title: '智慧告警系统',
      subtitle: 'Smart Alarm System',
      content: ['多层级智能告警机制', '故障根因自动分析', '预防性维护提醒'],
      alarmLevels: [
        { level: '紧急', color: '#ff4d4f', count: 0, desc: '需立即处理的严重故障' },
        { level: '重要', color: '#ff7a45', count: 2, desc: '影响业务的重要告警' },
        { level: '一般', color: '#ffa940', count: 5, desc: '需要关注的一般告警' },
        { level: '提示', color: '#52c41a', count: 12, desc: '信息提示类告警' },
      ],
      features: ['智能告警过滤', '告警关联分析', '自动故障定位', '多渠道通知', '处理流程跟踪', '告警统计分析'],
      type: 'alarm',
    },
    {
      title: '智慧运维管理',
      subtitle: 'Smart Operation & Maintenance',
      content: ['全流程运维管理', '自动化运维工具', '移动端运维支持'],
      workflows: [
        { name: '巡检管理', status: '进行中', progress: 75, desc: '设备巡检计划执行' },
        { name: '维修工单', status: '待处理', progress: 0, desc: '设备故障维修申请' },
        { name: '变更管理', status: '已完成', progress: 100, desc: '设备配置变更记录' },
        { name: '资产管理', status: '正常', progress: 95, desc: '资产生命周期管理' },
      ],
      automation: ['自动巡检任务', '智能工单分派', '预防性维护', '备件库存管理', '知识库检索', '移动端支持'],
      type: 'operation',
    },
    {
      title: '资产可视化管理',
      subtitle: 'Asset Visualization Management',
      content: ['资产全生命周期管理', '可视化资产地图', '智能盘点与追踪'],
      lifecycle: [
        { stage: '采购入库', desc: '资产采购、验收、入库登记', icon: '📦' },
        { stage: '部署上架', desc: '设备安装、配置、上架部署', icon: '🔧' },
        { stage: '运行维护', desc: '日常运行、维护、监控管理', icon: '⚙️' },
        { stage: '变更升级', desc: '配置变更、硬件升级、迁移', icon: '🔄' },
        { stage: '退役处置', desc: '设备退役、数据清除、资产处置', icon: '♻️' },
      ],
      statistics: {
        totalAssets: '2,847',
        servers: '1,256',
        network: '432',
        storage: '189',
        others: '970',
        utilization: '87.3%',
      },
      type: 'asset',
    },
    {
      title: '平台价值与优势',
      subtitle: 'Platform Value & Advantages',
      content: ['提升运营效率，降低运维成本', '增强系统可靠性与安全性', '实现绿色节能与可持续发展'],
      values: [
        { title: '效率提升', value: '40%', desc: '运维效率提升' },
        { title: '成本降低', value: '25%', desc: '运营成本降低' },
        { title: '可靠性', value: '99.9%', desc: '系统可用性' },
        { title: '节能效果', value: '15%', desc: '能耗降低' },
      ],
      advantages: [
        '统一管理平台，消除信息孤岛',
        '智能化运维，减少人工干预',
        '预防性维护，降低故障风险',
        '数据驱动决策，优化资源配置',
        '3D可视化，直观便捷操作',
        '移动化支持，随时随地管理',
      ],
      type: 'value',
    },
    {
      title: '感谢观看',
      subtitle: 'Thank You for Watching',
      content: ['感谢您对DCIM平台的关注', '期待与您深入合作交流', '共同推进数据中心智能化发展'],
      contact: {
        company: '数据中心管理解决方案提供商',
        phone: '400-xxx-xxxx',
        email: '<EMAIL>',
        website: 'www.dcim-platform.com',
      },
      closing: ['专业的技术团队', '丰富的项目经验', '完善的服务体系', '持续的技术创新'],
      type: 'closing',
    },
  ]);

  // 计算属性
  const currentSlideContent = computed(() => {
    return slideContents.value[currentSlide.value] || slideContents.value[0];
  });

  // 方法
  const initializePPT = () => {
    totalSlides.value = slideContents.value.length;
    globalThreeStore.setPPTTotalSlides(totalSlides.value);
    console.log('DCIM平台介绍PPT初始化成功，总页数:', totalSlides.value);
  };

  const previousSlide = () => {
    if (currentSlide.value > 0) {
      goToSlide(currentSlide.value - 1);
    }
  };

  const nextSlide = () => {
    if (currentSlide.value < totalSlides.value - 1) {
      goToSlide(currentSlide.value + 1);
    }
  };

  const goToSlide = (slideIndex?: number) => {
    const targetSlide = slideIndex !== undefined ? slideIndex : jumpToSlide.value - 1;
    if (targetSlide >= 0 && targetSlide < totalSlides.value) {
      currentSlide.value = targetSlide;
      jumpToSlide.value = targetSlide + 1;

      // 更新store状态
      globalThreeStore.setPPTCurrentSlide(targetSlide);

      // 触发视角切换
      triggerViewChange(targetSlide);

      // 触发事件
      emit('slideChange', targetSlide);
    }
  };

  const goToSlideBySlider = (value: number) => {
    goToSlide(value);
  };

  const toggleAutoPlay = () => {
    isAutoPlaying.value = !isAutoPlaying.value;
    globalThreeStore.setPPTPlaying(isAutoPlaying.value);

    if (isAutoPlaying.value) {
      startAutoPlay();
    } else {
      stopAutoPlay();
    }
  };

  const startAutoPlay = () => {
    stopAutoPlay(); // 清除现有定时器
    autoPlayTimer = window.setInterval(() => {
      if (currentSlide.value < totalSlides.value - 1) {
        nextSlide();
      } else {
        // 到达最后一页，停止自动播放
        toggleAutoPlay();
      }
    }, autoPlayInterval.value);
  };

  const stopAutoPlay = () => {
    if (autoPlayTimer) {
      clearInterval(autoPlayTimer);
      autoPlayTimer = null;
    }
  };

  const triggerViewChange = (slideIndex: number) => {
    const binding = globalThreeStore.getPPTViewBinding(slideIndex);
    if (binding) {
      const cameraController = CameraController.getInstance();
      if (cameraController) {
        cameraController.moveToPosition(
          binding.cameraPosition,
          binding.cameraTarget,
          1000 // 1秒过渡时间
        );
      }
    }
  };

  const bindCurrentView = () => {
    const cameraController = CameraController.getInstance();
    if (cameraController) {
      const position = cameraController.getCameraPosition();
      const target = cameraController.currentTarget || { x: 0, y: 0, z: 0 };

      const binding: PPTViewBinding = {
        slideIndex: currentSlide.value,
        cameraPosition: { x: position.x, y: position.y, z: position.z },
        cameraTarget: { x: target.x, y: target.y, z: target.z },
        name: `第${currentSlide.value + 1}页视角`,
      };

      globalThreeStore.addPPTViewBinding(binding);
    }
  };

  const handleViewBinding = (binding: PPTViewBinding) => {
    globalThreeStore.addPPTViewBinding(binding);
  };

  const handleRemoveBinding = (slideIndex: number) => {
    globalThreeStore.removePPTViewBinding(slideIndex);
  };

  // 监听自动播放间隔变化
  watch(autoPlayInterval, () => {
    if (isAutoPlaying.value) {
      startAutoPlay(); // 重新启动定时器
    }
  });

  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowLeft':
      case 'PageUp':
        event.preventDefault();
        previousSlide();
        break;
      case 'ArrowRight':
      case 'PageDown':
        event.preventDefault();
        nextSlide();
        break;
      case ' ':
        event.preventDefault();
        toggleAutoPlay();
        break;
    }
  };

  // 生命周期
  onMounted(() => {
    initializePPT();
    jumpToSlide.value = currentSlide.value + 1;

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyDown);
  });

  onUnmounted(() => {
    stopAutoPlay();

    // 移除键盘事件监听
    document.removeEventListener('keydown', handleKeyDown);
  });
</script>

<style scoped>
  .simple-ppt-player {
    background: linear-gradient(135deg, #0a0f1c 0%, #1a2332 100%);
  }
</style>
