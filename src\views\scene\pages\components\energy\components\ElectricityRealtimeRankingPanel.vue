<template>
  <div class="w-full h-full" ref="chartRef"></div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import * as echarts from 'echarts';
  import { getElectricityTop } from '/@/api/energy/electricity';
  import { debounce } from 'lodash-es';

  const chartRef = ref<HTMLElement | null>(null);
  let chart: echarts.ECharts | null = null;

  const simplifyDeviceName = (name: string) => {
    if (!name) return '未知设备';
    // 移除 "02010001-52001-B1-1AN1" 这样的前缀
    const nameWithoutPrefix = name.replace(/^\d+-\d+-[A-Z0-9]+-[A-Z0-9]+/, '');
    // 取 "｜" 后面的部分，如果没有 "｜"，则使用移除前缀后的名称
    const parts = nameWithoutPrefix.split('｜');
    return parts.length > 1 ? parts.pop()!.trim() : nameWithoutPrefix.trim();
  };

  const renderChart = (data: any[]) => {
    if (!chart) return;
    if (!data || data.length === 0) {
      chart.clear();
      chart.setOption({
        xAxis: { show: false },
        yAxis: { show: false },
        series: [],
        graphic: {
          elements: [
            {
              type: 'text',
              left: 'center',
              top: 'center',
              style: {
                text: '暂无实时数据',
                fontSize: 16,
                fill: '#999',
              },
            },
          ],
        },
      });
      return;
    }
    // 排序后reverse，值大的在上面
    const sorted = [...data]
      .map((item) => ({
        name: simplifyDeviceName(item.deviceName || item.name), // 使用简化函数
        value: parseFloat(item.valueData) || 0,
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 10) // 电耗保持取前10
      .reverse();

    const deviceNames = sorted.map((item) => item.name);
    const values = sorted.map((item) => item.value);

    chart.setOption({
      tooltip: {
        trigger: 'axis',
        confine: true,
        appendToBody: true,
        axisPointer: { type: 'shadow' },
        formatter: (params) => {
          const data = params[0];
          return `${data.name}<br/>${data.value} kWh`;
        },
        extraCssText:
          'background: rgba(0,0,0,0.7); border-radius: 4px; border: none; box-shadow: 0 0 3px rgba(0, 0, 0, 0.2); color: #fff; font-size: 12px;',
        textStyle: { color: '#fff', fontSize: 10 },
      },
      grid: {
        top: '20%',
        left: '3%',
        right: '15%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        name: 'kWh',
        nameTextStyle: { color: '#fff', fontSize: 10 },
        axisLabel: { color: '#fff', fontSize: 10 },
        splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
      },
      yAxis: {
        type: 'category',
        data: deviceNames,
        axisLabel: {
          color: '#fff',
          fontSize: 10,
          width: 70, // 调整宽度以适应简化后的名称
          overflow: 'truncate',
        },
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
      },
      series: [
        {
          name: '耗电量',
          type: 'bar',
          data: values,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#3B8EE6' },
              { offset: 1, color: '#77A9E3' },
            ]),
          },
          barWidth: '60%',
          label: {
            show: true,
            position: 'right',
            color: '#fff',
            fontSize: 9,
            distance: 5,
            formatter: (params) => Math.round(params.value) + 'kWh',
            overflow: 'truncate',
          },
        },
      ],
    });
  };

  const loadData = async () => {
    const result = await getElectricityTop();
    if (Array.isArray(result)) {
      renderChart(result);
    } else {
      renderChart([]);
    }
  };

  onMounted(() => {
    chart = echarts.init(chartRef.value!);
    loadData();
    window.addEventListener('resize', handleResize);
  });

  const handleResize = debounce(() => {
    chart?.resize();
  }, 300);

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    chart?.dispose();
    chart = null;
  });
</script>
