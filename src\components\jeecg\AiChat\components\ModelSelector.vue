<template>
  <div class="model-selector">
    <a-dropdown :trigger="['click']">
      <a-button type="text" class="model-btn" :title="currentModel.name">
        <span class="model-logo">{{ currentModel.logo }}</span>
        <span class="model-name">{{ currentModel.name }}</span>
        <down-outlined />
      </a-button>
      <template #overlay>
        <a-menu @click="handleMenuClick">
          <a-menu-item v-for="(model, key) in AI_MODELS" :key="key">
            <span class="model-logo">{{ model.logo }}</span>
            <span class="model-name">{{ model.name }}</span>
            <span v-if="model.isCustom" class="custom-tag">自定义</span>
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="CUSTOM_SETTINGS">
            <setting-outlined />
            <span>配置自定义模型</span>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>

    <custom-model-form v-model:visible="customModelVisible" @save="handleCustomModelSave" />
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { DownOutlined, SettingOutlined } from '@ant-design/icons-vue';
  import { AI_MODELS, getCustomModel } from '/@/enums/httpEnum';
  import CustomModelForm from './CustomModelForm.vue';

  const props = defineProps({
    modelKey: {
      type: String,
      default: 'GROK',
    },
  });

  const emit = defineEmits(['update:modelKey', 'change']);

  const currentModel = ref(AI_MODELS[props.modelKey]);
  const customModelVisible = ref(false);

  // 确保自定义模型加载
  getCustomModel();

  watch(
    () => props.modelKey,
    (newVal) => {
      currentModel.value = AI_MODELS[newVal];
    }
  );

  const handleMenuClick = (e: { key: string }) => {
    if (e.key === 'CUSTOM_SETTINGS') {
      customModelVisible.value = true;
      return;
    }

    emit('update:modelKey', e.key);
    emit('change', AI_MODELS[e.key]);
    currentModel.value = AI_MODELS[e.key];
  };

  const handleCustomModelSave = (modelKey: string) => {
    emit('update:modelKey', modelKey);
    emit('change', AI_MODELS[modelKey]);
    currentModel.value = AI_MODELS[modelKey];
  };
</script>

<style lang="less" scoped>
  .model-selector {
    .model-btn {
      display: flex;
      align-items: center;
      padding: 4px 8px;
      border-radius: 4px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      .model-logo {
        font-size: 16px;
        margin-right: 6px;
      }

      .model-name {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 4px;
      }
    }
  }

  :deep(.ant-dropdown-menu-item) {
    .model-logo {
      font-size: 16px;
      margin-right: 8px;
    }

    .model-name {
      font-size: 14px;
    }

    .custom-tag {
      font-size: 12px;
      color: #1890ff;
      margin-left: 4px;
    }
  }
</style>
