<template>
  <div class="w-full h-full p-[0.8vw] flex flex-col gap-[0.8vw]">
    <!-- 设备关键指标 -->
    <div class="h-[calc((100%-1.6vw)/2)] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">设备关键指标</div>
      </div>
      <div class="flex-1 mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw] overflow-auto custom-scrollbar">
        <div v-if="loading" class="flex justify-center items-center h-full">
          <div class="text-white text-[0.7vw]">加载中...</div>
        </div>
        <div v-else-if="error" class="flex justify-center items-center h-full">
          <div class="text-red-400 text-[0.7vw]">{{ error }}</div>
        </div>
        <div v-else-if="!deviceData || deviceData.length === 0" class="flex justify-center items-center h-full">
          <div class="text-yellow-400 text-[0.7vw]">未找到设备数据</div>
        </div>
        <div v-else-if="deviceData && deviceData.length > 0">
          <!-- 关键指标卡片 -->
          <div class="grid grid-cols-2 gap-[0.6vw]">
            <!-- 运行时间指标 -->
            <div v-for="param in keyTimeParams" :key="param.id" class="bg-[rgba(59,142,230,0.1)] rounded p-[0.6vw]">
              <div class="text-[0.65vw] text-gray-300 mb-[0.3vw] truncate" :title="param.remark">{{ param.remark }}</div>
              <div class="flex items-end">
                <div class="text-[1vw] text-white font-medium">{{ param.valueData }}</div>
                <div class="text-[0.6vw] text-gray-400 ml-[0.3vw]">小时</div>
              </div>
            </div>

            <!-- 频率指标 -->
            <div v-for="param in keyFrequencyParams" :key="param.id" class="bg-[rgba(59,142,230,0.1)] rounded p-[0.6vw]">
              <div class="text-[0.65vw] text-gray-300 mb-[0.3vw] truncate" :title="param.remark">{{ param.remark }}</div>
              <div class="flex items-end">
                <div class="text-[1vw] text-white font-medium">{{ param.valueData }}</div>
                <div class="text-[0.6vw] text-gray-400 ml-[0.3vw]">Hz</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备参数设置 -->
    <div class="h-[calc((100%-1.6vw)/2)] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">设备参数设置</div>
      </div>
      <div class="flex-1 mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw] overflow-auto custom-scrollbar">
        <div v-if="loading" class="flex justify-center items-center h-full">
          <div class="text-white text-[0.7vw]">加载中...</div>
        </div>
        <div v-else-if="error" class="flex justify-center items-center h-full">
          <div class="text-red-400 text-[0.7vw]">{{ error }}</div>
        </div>
        <div v-else-if="!deviceData || deviceData.length === 0" class="flex justify-center items-center h-full">
          <div class="text-yellow-400 text-[0.7vw]">未找到设备数据</div>
        </div>
        <div v-else-if="deviceData && deviceData.length > 0">
          <!-- 参数设置表格 -->
          <div class="w-full">
            <div class="grid grid-cols-[1.5fr_1fr] gap-[0.4vw] mb-[0.4vw] bg-[rgba(59,142,230,0.2)] p-[0.4vw] rounded">
              <div class="text-[0.65vw] text-white">参数名称</div>
              <div class="text-[0.65vw] text-white">当前值</div>
            </div>

            <div class="space-y-[0.4vw]">
              <div
                v-for="param in settingParams"
                :key="param.id"
                class="grid grid-cols-[1.5fr_1fr] gap-[0.4vw] bg-[rgba(59,142,230,0.1)] p-[0.4vw] rounded hover:bg-[rgba(59,142,230,0.15)] transition-all"
              >
                <div class="text-[0.6vw] text-gray-300 truncate" :title="param.remark">{{ param.remark }}</div>
                <div class="text-[0.6vw] text-white">{{ param.valueData }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted } from 'vue';
  import { storeToRefs } from 'pinia';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';
  import { useDeviceDetailStore } from './deviceDetailStore';
  import { useGlobalThreeStore } from '@/views/scene/store/globalThreeStore';

  const deviceDetailStore = useDeviceDetailStore();
  const globalThreeStore = useGlobalThreeStore();

  // 修改：使用storeToRefs来保持响应性
  const { deviceData, loading, error } = storeToRefs(deviceDetailStore);

  // 调试：监控数据变化
  onMounted(() => {
    console.log('DeviceDetailRight 组件已挂载，当前设备编码:', globalThreeStore.currentDeviceCode);
    if (globalThreeStore.currentDeviceCode) {
      console.log('组件挂载时尝试加载设备数据');
      deviceDetailStore.loadDeviceData(globalThreeStore.currentDeviceCode);
    }
  });

  // 关键时间参数
  const keyTimeParams = computed(() => {
    if (!deviceData.value || deviceData.value.length === 0) return [];

    // 筛选出与运行时间相关的参数
    return deviceData.value.filter((item) => item.remark.includes('运行时间') || item.remark.includes('空闲时间'));
  });

  // 关键频率参数
  const keyFrequencyParams = computed(() => {
    if (!deviceData.value || deviceData.value.length === 0) return [];

    // 筛选出与频率相关的参数
    return deviceData.value.filter(
      (item) => (item.remark.includes('频率') && item.remark.includes('反馈')) || (item.remark.includes('频率') && item.remark.includes('控制输出'))
    );
  });

  // 设置参数
  const settingParams = computed(() => {
    if (!deviceData.value || deviceData.value.length === 0) return [];

    // 筛选出类型为2的参数（设定值）
    return deviceData.value.filter((item) => item.type === 2).sort((a, b) => a.addr - b.addr);
  });
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.5) rgba(21, 39, 77, 0.3);
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 0.4vw;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(21, 39, 77, 0.3);
    border-radius: 0.2vw;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.5);
    border-radius: 0.2vw;
  }
</style>
