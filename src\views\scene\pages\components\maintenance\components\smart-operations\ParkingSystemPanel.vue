<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 停车位统计 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-chart-pie mr-[0.4vw] text-blue-400"></i>
        停车位统计
      </div>
      <div class="grid grid-cols-4 gap-[0.6vw]">
        <div v-for="stat in parkingStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：车辆进出记录 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-history mr-[0.4vw] text-blue-400"></i>
            车辆进出记录
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="filter in recordFilters"
              :key="filter.key"
              @click="activeRecordFilter = filter.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activeRecordFilter === filter.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30',
              ]"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div v-for="record in filteredRecords" :key="record.id" class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all">
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <div class="w-[0.6vw] h-[0.6vw] rounded-full mr-[0.6vw]" :class="record.type === 'in' ? 'bg-green-400' : 'bg-orange-400'"></div>
                  <span class="text-[0.65vw] text-white font-medium">{{ record.plateNumber }}</span>
                </div>
                <span class="text-[0.6vw]" :class="getRecordTypeClass(record.type)">
                  {{ record.type === 'in' ? '进入' : '离开' }}
                </span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400">
                <span>{{ record.location }}</span>
                <span>{{ record.time }}</span>
              </div>
              <div v-if="record.duration" class="text-[0.6vw] text-gray-400 mt-[0.2vw]"> 停车时长：{{ record.duration }} </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：停车位状态和收费统计 -->
      <div class="w-[35%] flex flex-col gap-[0.8vw]">
        <!-- 停车位状态 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-map-marker-alt mr-[0.4vw] text-blue-400"></i>
            停车位状态
          </div>

          <div class="grid grid-cols-5 gap-[0.3vw] mb-[0.6vw]">
            <div
              v-for="space in parkingSpaces"
              :key="space.id"
              :class="[
                'aspect-square rounded text-[0.5vw] flex items-center justify-center font-medium transition-all cursor-pointer',
                getParkingSpaceClass(space.status),
              ]"
              @click="handleSpaceClick(space)"
            >
              {{ space.number }}
            </div>
          </div>

          <div class="flex justify-between text-[0.6vw]">
            <div class="flex items-center">
              <div class="w-[0.6vw] h-[0.6vw] bg-green-400 rounded mr-[0.4vw]"></div>
              <span class="text-gray-400">空闲</span>
            </div>
            <div class="flex items-center">
              <div class="w-[0.6vw] h-[0.6vw] bg-red-400 rounded mr-[0.4vw]"></div>
              <span class="text-gray-400">占用</span>
            </div>
            <div class="flex items-center">
              <div class="w-[0.6vw] h-[0.6vw] bg-yellow-400 rounded mr-[0.4vw]"></div>
              <span class="text-gray-400">预约</span>
            </div>
          </div>
        </div>

        <!-- 收费统计 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-coins mr-[0.4vw] text-blue-400"></i>
            今日收费统计
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="fee in feeStats" :key="fee.label" class="flex justify-between">
              <span class="text-[0.6vw] text-gray-400">{{ fee.label }}</span>
              <span class="text-[0.6vw] text-white font-medium">{{ fee.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 停车位统计数据
  const parkingStats = ref([
    { label: '总车位', value: '45', valueClass: 'text-blue-400' },
    { label: '已占用', value: '14', valueClass: 'text-red-400' },
    { label: '空闲', value: '31', valueClass: 'text-green-400' },
    { label: '使用率', value: '31.1%', valueClass: 'text-yellow-400' },
  ]);

  // 记录筛选器
  const recordFilters = ref([
    { key: 'all', label: '全部' },
    { key: 'in', label: '进入' },
    { key: 'out', label: '离开' },
  ]);

  const activeRecordFilter = ref('all');

  // 车辆进出记录
  const vehicleRecords = ref([
    {
      id: 1,
      plateNumber: '京A12345',
      type: 'in',
      location: 'A区-01号位',
      time: '2024-02-28 14:30',
    },
    {
      id: 2,
      plateNumber: '京B67890',
      type: 'out',
      location: 'B区-15号位',
      time: '2024-02-28 14:25',
      duration: '2小时30分钟',
    },
    {
      id: 3,
      plateNumber: '京C11111',
      type: 'in',
      location: 'A区-08号位',
      time: '2024-02-28 14:20',
    },
    {
      id: 4,
      plateNumber: '京D22222',
      type: 'out',
      location: 'C区-22号位',
      time: '2024-02-28 14:15',
      duration: '4小时15分钟',
    },
    {
      id: 5,
      plateNumber: '京E33333',
      type: 'in',
      location: 'B区-12号位',
      time: '2024-02-28 14:10',
    },
  ]);

  // 筛选后的记录
  const filteredRecords = computed(() => {
    if (activeRecordFilter.value === 'all') {
      return vehicleRecords.value;
    }
    return vehicleRecords.value.filter((record) => record.type === activeRecordFilter.value);
  });

  // 停车位状态（模拟45个车位）
  const parkingSpaces = ref(
    Array.from({ length: 45 }, (_, index) => ({
      id: index + 1,
      number: String(index + 1).padStart(2, '0'),
      status: index < 14 ? 'occupied' : index < 16 ? 'reserved' : 'available',
    }))
  );

  // 收费统计
  const feeStats = ref([
    { label: '总收入', value: '¥1,280' },
    { label: '车次', value: '32次' },
    { label: '平均停车时长', value: '2.5小时' },
    { label: '平均收费', value: '¥40' },
  ]);

  // 获取记录类型样式
  const getRecordTypeClass = (type) => {
    return type === 'in' ? 'text-green-400' : 'text-orange-400';
  };

  // 获取停车位样式
  const getParkingSpaceClass = (status) => {
    switch (status) {
      case 'available':
        return 'bg-green-400/20 text-green-400 hover:bg-green-400/30';
      case 'occupied':
        return 'bg-red-400/20 text-red-400 hover:bg-red-400/30';
      case 'reserved':
        return 'bg-yellow-400/20 text-yellow-400 hover:bg-yellow-400/30';
      default:
        return 'bg-gray-400/20 text-gray-400';
    }
  };

  // 处理停车位点击
  const handleSpaceClick = (space) => {
    console.log('点击停车位:', space);
    // 这里可以添加停车位详情查看逻辑
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
