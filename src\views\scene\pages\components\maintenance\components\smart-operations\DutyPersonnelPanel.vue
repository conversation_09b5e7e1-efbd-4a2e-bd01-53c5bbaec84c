<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 值班概览 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-users mr-[0.4vw] text-blue-400"></i>
        值班人员概览
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in dutyStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：当前值班安排 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-clock mr-[0.4vw] text-blue-400"></i>
            当前值班安排
          </div>
          <div class="text-[0.6vw] text-gray-400"> {{ currentDate }} {{ currentShift }} </div>
        </div>

        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div
              v-for="person in currentDutyPersonnel"
              :key="person.id"
              class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all"
            >
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <div class="w-[2vw] h-[2vw] rounded-full bg-blue-400/20 flex items-center justify-center mr-[0.6vw]">
                    <i class="fas fa-user text-blue-400"></i>
                  </div>
                  <div>
                    <div class="text-[0.65vw] text-white font-medium">{{ person.name }}</div>
                    <div class="text-[0.6vw] text-gray-400">{{ person.position }}</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-[0.6vw]" :class="getStatusClass(person.status)">{{ getStatusText(person.status) }}</div>
                  <div class="text-[0.6vw] text-gray-400">{{ person.location }}</div>
                </div>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400">
                <span>联系方式：{{ person.phone }}</span>
                <span>值班时间：{{ person.shiftTime }}</span>
              </div>
              <div v-if="person.currentTask" class="mt-[0.3vw] text-[0.6vw] text-yellow-400"> 当前任务：{{ person.currentTask }} </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：交接班记录和人员状态 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 交接班记录 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-exchange-alt mr-[0.4vw] text-blue-400"></i>
            交接班记录
          </div>

          <div class="space-y-[0.4vw] max-h-[15vw] overflow-y-auto custom-scrollbar">
            <div v-for="record in handoverRecords" :key="record.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ record.fromPerson }} → {{ record.toPerson }}</span>
                <span class="text-[0.5vw] text-gray-400">{{ record.time }}</span>
              </div>
              <div class="text-[0.6vw] text-gray-400 mb-[0.2vw]">{{ record.shift }}</div>
              <div class="text-[0.5vw] text-gray-300">{{ record.notes }}</div>
              <div v-if="record.issues?.length" class="mt-[0.2vw]">
                <div class="text-[0.5vw] text-yellow-400">待处理事项：</div>
                <ul class="text-[0.5vw] text-gray-400 ml-[0.8vw]">
                  <li v-for="issue in record.issues" :key="issue">• {{ issue }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 人员状态监控 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-heartbeat mr-[0.4vw] text-blue-400"></i>
            人员状态监控
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="person in personnelStatus" :key="person.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ person.name }}</span>
                <div class="flex items-center">
                  <div class="w-[0.6vw] h-[0.6vw] rounded-full mr-[0.4vw]" :class="getHealthStatusClass(person.healthStatus)"></div>
                  <span class="text-[0.6vw] text-gray-400">{{ person.healthStatus }}</span>
                </div>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>最后活动：{{ person.lastActivity }}</span>
                <span>位置：{{ person.currentLocation }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 值班计划 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-calendar-alt mr-[0.4vw] text-blue-400"></i>
            值班计划
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="schedule in upcomingSchedules" :key="schedule.id" class="flex justify-between items-center">
              <div>
                <div class="text-[0.6vw] text-white">{{ schedule.date }}</div>
                <div class="text-[0.5vw] text-gray-400">{{ schedule.shift }}</div>
              </div>
              <div class="text-right">
                <div class="text-[0.6vw] text-gray-300">{{ schedule.personnel.join(', ') }}</div>
                <div class="text-[0.5vw] text-gray-400">{{ schedule.personnel.length }}人</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 紧急联系 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-phone-alt mr-[0.4vw] text-blue-400"></i>
            紧急联系
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="contact in emergencyContacts" :key="contact.role" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ contact.role }}</span>
              <div class="text-right">
                <div class="text-[0.6vw] text-white">{{ contact.name }}</div>
                <div class="text-[0.5vw] text-blue-400">{{ contact.phone }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 值班统计数据
  const dutyStats = ref([
    { label: '在岗人员', value: '8', valueClass: 'text-green-400' },
    { label: '值班班次', value: '3', valueClass: 'text-blue-400' },
    { label: '今日交接', value: '6', valueClass: 'text-green-400' },
    { label: '待处理事项', value: '0', valueClass: 'text-green-400' },
    { label: '出勤率', value: '100%', valueClass: 'text-green-400' },
  ]);

  // 当前日期和班次
  const currentDate = ref('2024-02-28');
  const currentShift = ref('白班 (08:00-20:00)');

  // 当前值班人员
  const currentDutyPersonnel = ref([
    {
      id: 1,
      name: '张工程师',
      position: '运维主管',
      phone: '138****1234',
      status: 'online',
      location: '1F机房',
      shiftTime: '08:00-20:00',
      currentTask: '设备巡检',
    },
    {
      id: 2,
      name: '李技师',
      position: '网络工程师',
      phone: '139****5678',
      status: 'online',
      location: '2F网络间',
      shiftTime: '08:00-20:00',
      currentTask: null,
    },
    {
      id: 3,
      name: '王师傅',
      position: '电力工程师',
      phone: '137****9012',
      status: 'online',
      location: '配电室',
      shiftTime: '08:00-20:00',
      currentTask: '配电巡检',
    },
    {
      id: 4,
      name: '赵工',
      position: '安全员',
      phone: '136****3456',
      status: 'online',
      location: '监控中心',
      shiftTime: '08:00-20:00',
      currentTask: null,
    },
  ]);

  // 交接班记录
  const handoverRecords = ref([
    {
      id: 1,
      fromPerson: '陈工程师',
      toPerson: '张工程师',
      shift: '夜班 → 白班',
      time: '2024-02-28 08:00',
      notes: '夜间运行正常，无异常情况',
      issues: [],
    },
    {
      id: 2,
      fromPerson: '孙技师',
      toPerson: '李技师',
      shift: '夜班 → 白班',
      time: '2024-02-28 08:00',
      notes: '网络设备运行稳定，带宽使用率正常',
      issues: [],
    },
    {
      id: 3,
      fromPerson: '周师傅',
      toPerson: '王师傅',
      shift: '夜班 → 白班',
      time: '2024-02-28 08:00',
      notes: '配电系统正常，负载均衡',
      issues: [],
    },
  ]);

  // 人员状态监控
  const personnelStatus = ref([
    {
      id: 1,
      name: '张工程师',
      healthStatus: '正常',
      lastActivity: '5分钟前',
      currentLocation: '1F机房',
    },
    {
      id: 2,
      name: '李技师',
      healthStatus: '正常',
      lastActivity: '2分钟前',
      currentLocation: '2F网络间',
    },
    {
      id: 3,
      name: '王师傅',
      healthStatus: '正常',
      lastActivity: '1分钟前',
      currentLocation: '配电室',
    },
    {
      id: 4,
      name: '赵工',
      healthStatus: '正常',
      lastActivity: '刚刚',
      currentLocation: '监控中心',
    },
  ]);

  // 即将到来的值班计划
  const upcomingSchedules = ref([
    {
      id: 1,
      date: '2024-02-28',
      shift: '夜班 (20:00-08:00)',
      personnel: ['陈工程师', '孙技师', '周师傅'],
    },
    {
      id: 2,
      date: '2024-02-29',
      shift: '白班 (08:00-20:00)',
      personnel: ['张工程师', '李技师', '王师傅', '赵工'],
    },
    {
      id: 3,
      date: '2024-02-29',
      shift: '夜班 (20:00-08:00)',
      personnel: ['陈工程师', '孙技师'],
    },
  ]);

  // 紧急联系人
  const emergencyContacts = ref([
    { role: '运维经理', name: '刘经理', phone: '135****7890' },
    { role: '技术总监', name: '马总监', phone: '134****2345' },
    { role: '安全负责人', name: '杨主管', phone: '133****6789' },
    { role: '设施管理', name: '吴主任', phone: '132****0123' },
  ]);

  // 获取状态样式
  const getStatusClass = (status) => {
    switch (status) {
      case 'online':
        return 'text-green-400';
      case 'busy':
        return 'text-yellow-400';
      case 'offline':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取状态文本
  const getStatusText = (status) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'busy':
        return '忙碌';
      case 'offline':
        return '离线';
      default:
        return '未知';
    }
  };

  // 获取健康状态样式
  const getHealthStatusClass = (status) => {
    switch (status) {
      case '正常':
        return 'bg-green-400';
      case '注意':
        return 'bg-yellow-400';
      case '异常':
        return 'bg-red-400';
      default:
        return 'bg-gray-400';
    }
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
